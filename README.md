# نظام إدارة الدراسات العليا
## Graduate Studies Management System

نظام شامل لإدارة شؤون الطلبة والدرجات في الدراسات العليا، مطور باستخدام Python و Flask مع واجهة مستخدم متجاوبة باستخدام Bootstrap.

## المميزات الرئيسية

### 🏛️ إدارة البيانات الأساسية
- **إدارة الجامعات**: تسجيل وإدارة بيانات الجامعات مع الشعارات والعناوين
- **إدارة الكليات**: ربط الكليات بالجامعات مع البيانات التفصيلية
- **إدارة الأقسام**: تنظيم الأقسام العلمية مع بيانات رؤساء الأقسام
- **البرامج الأكاديمية**: إدارة برامج الماجستير والدكتوراه والدبلوم العالي
- **المواد الدراسية**: تحديد المواد لكل برنامج مع الوحدات والساعات

### 👥 إدارة الأشخاص
- **إدارة الطلبة**: تسجيل بيانات الطلبة مع التخصصات وقنوات القبول
- **إدارة الأساتذة**: بيانات الأساتذة الأكاديميين مع الألقاب العلمية والتخصصات
- **إدارة المستخدمين**: نظام مستخدمين متعدد المستويات (مدير، مستخدم عادي)

### 📊 إدارة النتائج والدرجات
- **إدخال الدرجات**: نظام متقدم لإدخال درجات السعي والامتحانات النهائية
- **حساب المعدلات**: حساب تلقائي للمعدلات الفصلية والتراكمية
- **التقديرات**: تحديد التقديرات (امتياز، جيد جداً، جيد، مقبول، ضعيف)
- **نسب النجاح**: حساب نسب النجاح للمواد والبرامج

### 📋 التقارير والكشوف
- **كشوف الدرجات الفردية**: كشف درجات مفصل لكل طالب
- **الكشف الكامل**: عرض جميع درجات الطالب عبر جميع الفصول
- **تقارير المواد**: إحصائيات شاملة لكل مادة دراسية
- **الإحصائيات العامة**: لوحة تحكم بالإحصائيات المهمة

## التقنيات المستخدمة

### Backend
- **Python 3.8+**: لغة البرمجة الأساسية
- **Flask**: إطار عمل الويب
- **SQLAlchemy**: ORM لقاعدة البيانات
- **SQLite**: قاعدة البيانات (قابلة للتغيير إلى MySQL/PostgreSQL)
- **Flask-Login**: إدارة جلسات المستخدمين
- **Werkzeug**: أمان كلمات المرور

### Frontend
- **HTML5 & CSS3**: هيكل وتنسيق الصفحات
- **Bootstrap 5.3**: إطار عمل CSS متجاوب
- **Bootstrap Icons**: مكتبة الأيقونات
- **JavaScript**: التفاعل والتحقق من البيانات

## متطلبات التشغيل

```bash
Python 3.8+
Flask 2.3.3
Flask-SQLAlchemy 3.0.5
Flask-Login 0.6.3
Flask-WTF 1.1.1
WTForms 3.0.1
Werkzeug 2.3.7
Pillow 10.0.1
```

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd DrasatUliaSystem
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل النظام
```bash
python complete_app.py
```

### 4. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost:5001`

## بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## هيكل المشروع

```
DrasatUliaSystem/
├── complete_app.py          # التطبيق الرئيسي الكامل
├── app.py                   # التطبيق الأصلي (مقسم)
├── models.py                # نماذج قاعدة البيانات
├── config.py                # إعدادات التطبيق
├── requirements.txt         # متطلبات Python
├── init_data.py            # بيانات أولية للنظام
├── templates/              # قوالب HTML
│   ├── login.html
│   ├── dashboard.html
│   ├── universities.html
│   ├── students.html
│   ├── grades.html
│   └── ...
├── static/                 # الملفات الثابتة
│   ├── css/
│   ├── js/
│   ├── images/
│   └── uploads/
└── routes/                 # مسارات التطبيق (النسخة المقسمة)
    ├── auth.py
    ├── main.py
    ├── admin.py
    └── reports.py
```

## الوظائف الرئيسية

### 1. لوحة التحكم
- عرض الإحصائيات العامة
- أحدث النشاطات
- روابط سريعة للوظائف المهمة

### 2. إدارة الجامعات
- إضافة جامعات جديدة
- تعديل بيانات الجامعات
- رفع شعارات الجامعات

### 3. إدارة الطلبة
- تسجيل طلبة جدد
- تعديل بيانات الطلبة
- فلترة وبحث متقدم
- تتبع حالات الطلبة

### 4. إدارة الدرجات
- إدخال درجات السعي والنهائي
- حساب تلقائي للدرجات الكاملة
- عرض التقديرات والنسب
- تحديث الدرجات الموجودة

### 5. التقارير
- كشوف درجات فردية
- تقارير شاملة
- إمكانية الطباعة
- تصدير البيانات

## الأمان والحماية

- **تشفير كلمات المرور**: استخدام Werkzeug لتشفير كلمات المرور
- **جلسات آمنة**: إدارة جلسات المستخدمين بأمان
- **صلاحيات متدرجة**: نظام صلاحيات للمديرين والمستخدمين
- **التحقق من البيانات**: فلترة وتنظيف جميع المدخلات

## التخصيص والتطوير

النظام مصمم ليكون قابلاً للتخصيص والتوسع:

- **قاعدة بيانات مرنة**: يمكن تغييرها من SQLite إلى MySQL أو PostgreSQL
- **واجهة متجاوبة**: تعمل على جميع الأجهزة والشاشات
- **كود منظم**: هيكل واضح وقابل للصيانة
- **توثيق شامل**: تعليقات وتوثيق باللغة العربية

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع التوثيق في الكود
- تحقق من ملفات السجل (logs)
- تأكد من تثبيت جميع المتطلبات

## الترخيص

هذا المشروع مطور لأغراض تعليمية وأكاديمية.

---

**تم تطوير هذا النظام باستخدام أفضل الممارسات في تطوير تطبيقات الويب مع التركيز على الأمان والأداء وسهولة الاستخدام.**
