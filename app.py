#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الدراسات العليا
Graduate Studies Management System
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from config import Config
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config.from_object(Config)

# إعداد قاعدة البيانات
db = SQLAlchemy()
db.init_app(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# استيراد النماذج
from models import *

# استيراد المسارات
from routes import auth, main, admin, reports

# تسجيل المسارات
app.register_blueprint(auth.bp)
app.register_blueprint(main.bp)
app.register_blueprint(admin.bp)
app.register_blueprint(reports.bp)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# إنشاء قاعدة البيانات
with app.app_context():
    db.create_all()

    # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
    if not User.query.filter_by(username='admin').first():
        admin_user = User(
            username='admin',
            full_name='مدير النظام',
            email='<EMAIL>',
            phone='1234567890',
            user_type='admin',
            is_active=True
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        db.session.commit()
        print("تم إنشاء المستخدم الافتراضي: admin / admin123")

# تم نقل كود التشغيل إلى run.py
