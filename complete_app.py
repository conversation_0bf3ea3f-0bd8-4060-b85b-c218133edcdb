#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الدراسات العليا الكامل
Complete Graduate Studies Management System
"""

from flask import Flask, render_template, request, flash, redirect, url_for, get_flashed_messages, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
from sqlalchemy import func
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///graduate_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# ===== النماذج =====

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    user_type = db.Column(db.String(20), nullable=False, default='user')
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class University(db.Model):
    """نموذج الجامعات"""
    __tablename__ = 'universities'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    address = db.Column(db.Text)
    logo = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    colleges = db.relationship('College', backref='university', lazy=True, cascade='all, delete-orphan')

class College(db.Model):
    """نموذج الكليات"""
    __tablename__ = 'colleges'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    address = db.Column(db.Text)
    logo = db.Column(db.String(255))
    university_id = db.Column(db.Integer, db.ForeignKey('universities.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    departments = db.relationship('Department', backref='college', lazy=True, cascade='all, delete-orphan')

class Department(db.Model):
    """نموذج الأقسام"""
    __tablename__ = 'departments'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    head_name = db.Column(db.String(200))
    secretary_name = db.Column(db.String(200))
    description = db.Column(db.Text)
    logo = db.Column(db.String(255))
    college_id = db.Column(db.Integer, db.ForeignKey('colleges.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    programs = db.relationship('AcademicProgram', backref='department', lazy=True, cascade='all, delete-orphan')
    professors = db.relationship('Professor', backref='department', lazy=True)

class Specialization(db.Model):
    """نموذج التخصصات العلمية"""
    __tablename__ = 'specializations'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    professors = db.relationship('Professor', backref='specialization', lazy=True)
    students = db.relationship('Student', backref='specialization', lazy=True)

class AdmissionChannel(db.Model):
    """نموذج قنوات القبول"""
    __tablename__ = 'admission_channels'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    students = db.relationship('Student', backref='admission_channel', lazy=True)

class AcademicYear(db.Model):
    """نموذج السنوات الدراسية"""
    __tablename__ = 'academic_years'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    is_current = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    students = db.relationship('Student', backref='academic_year', lazy=True)
    grades = db.relationship('Grade', backref='academic_year', lazy=True)

class Semester(db.Model):
    """نموذج الفصول الدراسية"""
    __tablename__ = 'semesters'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    grades = db.relationship('Grade', backref='semester', lazy=True)

class AcademicProgram(db.Model):
    """نموذج البرامج الأكاديمية"""
    __tablename__ = 'academic_programs'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    program_type = db.Column(db.String(50), nullable=False)
    duration = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    subjects = db.relationship('Subject', backref='program', lazy=True, cascade='all, delete-orphan')
    students = db.relationship('Student', backref='program', lazy=True)

class Subject(db.Model):
    """نموذج المواد الدراسية"""
    __tablename__ = 'subjects'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    units = db.Column(db.Integer, nullable=False)
    hours = db.Column(db.Integer, nullable=False)
    subject_type = db.Column(db.String(20), nullable=False)
    academic_year_type = db.Column(db.String(20), nullable=False)
    program_id = db.Column(db.Integer, db.ForeignKey('academic_programs.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    grades = db.relationship('Grade', backref='subject', lazy=True)

class Professor(db.Model):
    """نموذج الأساتذة الأكاديميين"""
    __tablename__ = 'professors'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    degree = db.Column(db.String(100))
    academic_title = db.Column(db.String(100))
    title_date = db.Column(db.Date)
    general_specialization = db.Column(db.String(200))
    specific_specialization = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    notes = db.Column(db.Text)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=False)
    specialization_id = db.Column(db.Integer, db.ForeignKey('specializations.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    grades = db.relationship('Grade', backref='professor', lazy=True)

class Student(db.Model):
    """نموذج الطلبة"""
    __tablename__ = 'students'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    gender = db.Column(db.String(10), nullable=False)
    status = db.Column(db.String(50), nullable=False, default='مستمر بالدراسة')
    program_id = db.Column(db.Integer, db.ForeignKey('academic_programs.id'), nullable=False)
    admission_channel_id = db.Column(db.Integer, db.ForeignKey('admission_channels.id'), nullable=False)
    specialization_id = db.Column(db.Integer, db.ForeignKey('specializations.id'), nullable=False)
    academic_year_id = db.Column(db.Integer, db.ForeignKey('academic_years.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    grades = db.relationship('Grade', backref='student', lazy=True)

class Grade(db.Model):
    """نموذج الدرجات والنتائج"""
    __tablename__ = 'grades'
    
    id = db.Column(db.Integer, primary_key=True)
    midterm_grade = db.Column(db.Float, default=0.0)
    final_grade = db.Column(db.Float, default=0.0)
    total_grade = db.Column(db.Float, default=0.0)
    
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'), nullable=False)
    professor_id = db.Column(db.Integer, db.ForeignKey('professors.id'), nullable=False)
    academic_year_id = db.Column(db.Integer, db.ForeignKey('academic_years.id'), nullable=False)
    semester_id = db.Column(db.Integer, db.ForeignKey('semesters.id'), nullable=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('student_id', 'subject_id', 'academic_year_id', 'semester_id'),)
    
    @property
    def grade_letter(self):
        """حساب التقدير الحرفي"""
        if self.total_grade >= 90:
            return 'امتياز'
        elif self.total_grade >= 80:
            return 'جيد جداً'
        elif self.total_grade >= 70:
            return 'جيد'
        elif self.total_grade >= 60:
            return 'مقبول'
        else:
            return 'ضعيف'
    
    @property
    def is_passed(self):
        """التحقق من النجاح"""
        return self.total_grade >= 60
    
    def calculate_total(self):
        """حساب الدرجة الكاملة"""
        self.total_grade = self.midterm_grade + self.final_grade
        return self.total_grade

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# ===== المسارات =====

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات المدير"""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('login'))
        if current_user.user_type != 'admin':
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# المسارات الأساسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = bool(request.form.get('remember'))

        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('login.html')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            if not user.is_active:
                flash('حسابك غير نشط. يرجى الاتصال بالمدير', 'error')
                return render_template('login.html')

            login_user(user, remember=remember)
            flash(f'مرحباً {user.full_name}', 'success')

            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    # إحصائيات عامة
    stats = {
        'universities': University.query.count(),
        'colleges': College.query.count(),
        'departments': Department.query.count(),
        'programs': AcademicProgram.query.count(),
        'subjects': Subject.query.count(),
        'professors': Professor.query.count(),
        'students': Student.query.count(),
        'grades': Grade.query.count()
    }

    # أحدث الطلبة المسجلين
    recent_students = Student.query.order_by(Student.created_at.desc()).limit(5).all()

    # أحدث الدرجات المدخلة
    recent_grades = Grade.query.order_by(Grade.created_at.desc()).limit(5).all()

    return render_template('dashboard.html',
                         stats=stats,
                         recent_students=recent_students,
                         recent_grades=recent_grades)

# مسارات إدارة الجامعات
@app.route('/admin/universities')
@login_required
@admin_required
def universities():
    """صفحة إدارة الجامعات"""
    universities = University.query.all()
    return render_template('universities.html', universities=universities)

@app.route('/admin/universities/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_university():
    """إضافة جامعة جديدة"""
    if request.method == 'POST':
        name_ar = request.form.get('name_ar')
        name_en = request.form.get('name_en')
        address = request.form.get('address')

        if not name_ar:
            flash('اسم الجامعة بالعربية مطلوب', 'error')
            return render_template('add_university.html')

        university = University(
            name_ar=name_ar,
            name_en=name_en,
            address=address
        )

        db.session.add(university)
        db.session.commit()
        flash('تم إضافة الجامعة بنجاح', 'success')
        return redirect(url_for('universities'))

    return render_template('add_university.html')

@app.route('/admin/universities/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_university(id):
    """تعديل جامعة"""
    university = University.query.get_or_404(id)

    if request.method == 'POST':
        university.name_ar = request.form.get('name_ar')
        university.name_en = request.form.get('name_en')
        university.address = request.form.get('address')

        db.session.commit()
        flash('تم تحديث الجامعة بنجاح', 'success')
        return redirect(url_for('universities'))

    return render_template('edit_university.html', university=university)

@app.route('/admin/universities/delete/<int:id>')
@login_required
@admin_required
def delete_university(id):
    """حذف جامعة"""
    university = University.query.get_or_404(id)

    # التحقق من وجود كليات مرتبطة
    if university.colleges:
        flash('لا يمكن حذف الجامعة لوجود كليات مرتبطة بها', 'error')
        return redirect(url_for('universities'))

    db.session.delete(university)
    db.session.commit()
    flash('تم حذف الجامعة بنجاح', 'success')
    return redirect(url_for('universities'))

# مسارات إدارة الكليات
@app.route('/admin/colleges')
@login_required
@admin_required
def colleges():
    """صفحة إدارة الكليات"""
    colleges = College.query.all()
    return render_template('colleges.html', colleges=colleges)

@app.route('/admin/colleges/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_college():
    """إضافة كلية جديدة"""
    universities = University.query.all()

    if request.method == 'POST':
        name_ar = request.form.get('name_ar')
        name_en = request.form.get('name_en')
        address = request.form.get('address')
        university_id = request.form.get('university_id')

        if not name_ar or not university_id:
            flash('اسم الكلية والجامعة مطلوبان', 'error')
            return render_template('add_college.html', universities=universities)

        college = College(
            name_ar=name_ar,
            name_en=name_en,
            address=address,
            university_id=university_id
        )

        db.session.add(college)
        db.session.commit()
        flash('تم إضافة الكلية بنجاح', 'success')
        return redirect(url_for('colleges'))

    return render_template('add_college.html', universities=universities)

@app.route('/admin/colleges/edit/<int:college_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_college(college_id):
    """تعديل كلية"""
    college = College.query.get_or_404(college_id)
    universities = University.query.all()

    if request.method == 'POST':
        college.name_ar = request.form.get('name_ar')
        college.name_en = request.form.get('name_en')
        college.address = request.form.get('address')
        college.university_id = request.form.get('university_id')

        db.session.commit()
        flash('تم تحديث الكلية بنجاح', 'success')
        return redirect(url_for('colleges'))

    return render_template('edit_college.html', college=college, universities=universities)

@app.route('/admin/colleges/delete/<int:college_id>', methods=['POST'])
@login_required
@admin_required
def delete_college(college_id):
    """حذف كلية"""
    college = College.query.get_or_404(college_id)

    # التحقق من وجود أقسام مرتبطة
    if college.departments:
        flash('لا يمكن حذف الكلية لوجود أقسام مرتبطة بها', 'error')
        return redirect(url_for('colleges'))

    db.session.delete(college)
    db.session.commit()
    flash('تم حذف الكلية بنجاح', 'success')
    return redirect(url_for('colleges'))

# مسارات إدارة الأقسام
@app.route('/admin/departments')
@login_required
@admin_required
def departments():
    """صفحة إدارة الأقسام"""
    departments = Department.query.all()
    return render_template('departments.html', departments=departments)

@app.route('/admin/departments/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_department():
    """إضافة قسم جديد"""
    colleges = College.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        head_name = request.form.get('head_name')
        secretary_name = request.form.get('secretary_name')
        description = request.form.get('description')
        college_id = request.form.get('college_id')

        if not name or not college_id:
            flash('اسم القسم والكلية مطلوبان', 'error')
            return render_template('add_department.html', colleges=colleges)

        department = Department(
            name=name,
            head_name=head_name,
            secretary_name=secretary_name,
            description=description,
            college_id=college_id
        )

        db.session.add(department)
        db.session.commit()
        flash('تم إضافة القسم بنجاح', 'success')
        return redirect(url_for('departments'))

    return render_template('add_department.html', colleges=colleges)

# مسارات إدارة الطلبة
@app.route('/admin/students')
@login_required
@admin_required
def students():
    """صفحة إدارة الطلبة"""
    students = Student.query.all()
    return render_template('students.html', students=students)

@app.route('/admin/students/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_student():
    """إضافة طالب جديد"""
    programs = AcademicProgram.query.all()
    admission_channels = AdmissionChannel.query.all()
    specializations = Specialization.query.all()
    academic_years = AcademicYear.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        gender = request.form.get('gender')
        status = request.form.get('status', 'مستمر بالدراسة')
        program_id = request.form.get('program_id')
        admission_channel_id = request.form.get('admission_channel_id')
        specialization_id = request.form.get('specialization_id')
        academic_year_id = request.form.get('academic_year_id')

        if not all([name, gender, program_id, admission_channel_id, specialization_id, academic_year_id]):
            flash('جميع الحقول مطلوبة', 'error')
            return render_template('add_student.html',
                                 programs=programs,
                                 admission_channels=admission_channels,
                                 specializations=specializations,
                                 academic_years=academic_years)

        try:
            student = Student(
                name=name,
                gender=gender,
                status=status,
                program_id=int(program_id),
                admission_channel_id=int(admission_channel_id),
                specialization_id=int(specialization_id),
                academic_year_id=int(academic_year_id)
            )

            db.session.add(student)
            db.session.commit()
            flash('تم إضافة الطالب بنجاح', 'success')
            return redirect(url_for('students'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة الطالب: {str(e)}', 'error')
            return render_template('add_student.html',
                                 programs=programs,
                                 admission_channels=admission_channels,
                                 specializations=specializations,
                                 academic_years=academic_years)

    return render_template('add_student.html',
                         programs=programs,
                         admission_channels=admission_channels,
                         specializations=specializations,
                         academic_years=academic_years)

@app.route('/admin/students/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_student(id):
    """تعديل طالب"""
    student = Student.query.get_or_404(id)
    programs = AcademicProgram.query.all()
    admission_channels = AdmissionChannel.query.all()
    specializations = Specialization.query.all()
    academic_years = AcademicYear.query.all()

    if request.method == 'POST':
        try:
            student.name = request.form.get('name')
            student.gender = request.form.get('gender')
            student.status = request.form.get('status', 'مستمر بالدراسة')
            student.program_id = int(request.form.get('program_id'))
            student.admission_channel_id = int(request.form.get('admission_channel_id'))
            student.specialization_id = int(request.form.get('specialization_id'))
            student.academic_year_id = int(request.form.get('academic_year_id'))

            db.session.commit()
            flash('تم تحديث بيانات الطالب بنجاح', 'success')
            return redirect(url_for('students'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الطالب: {str(e)}', 'error')

    return render_template('edit_student.html',
                         student=student,
                         programs=programs,
                         admission_channels=admission_channels,
                         specializations=specializations,
                         academic_years=academic_years)

@app.route('/admin/students/delete/<int:id>')
@login_required
@admin_required
def delete_student(id):
    """حذف طالب"""
    student = Student.query.get_or_404(id)

    # التحقق من وجود درجات مرتبطة
    if student.grades:
        flash('لا يمكن حذف الطالب لوجود درجات مرتبطة به', 'error')
        return redirect(url_for('students'))

    try:
        db.session.delete(student)
        db.session.commit()
        flash('تم حذف الطالب بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الطالب: {str(e)}', 'error')

    return redirect(url_for('students'))

# مسارات إدارة الدرجات
@app.route('/admin/grades')
@login_required
@admin_required
def grades():
    """صفحة إدارة الدرجات"""
    grades = Grade.query.all()
    return render_template('grades.html', grades=grades)

@app.route('/admin/grades/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_grades():
    """إضافة درجات للطلبة"""
    academic_years = AcademicYear.query.all()
    semesters = Semester.query.all()
    programs = AcademicProgram.query.all()
    subjects = Subject.query.all()
    professors = Professor.query.all()

    if request.method == 'POST':
        academic_year_id = request.form.get('academic_year_id')
        semester_id = request.form.get('semester_id')
        program_id = request.form.get('program_id')
        subject_id = request.form.get('subject_id')
        professor_id = request.form.get('professor_id')

        if not all([academic_year_id, semester_id, program_id, subject_id, professor_id]):
            flash('يرجى اختيار جميع المعايير المطلوبة', 'error')
            return render_template('add_grades.html',
                                 academic_years=academic_years,
                                 semesters=semesters,
                                 programs=programs,
                                 subjects=subjects,
                                 professors=professors)

        # الحصول على الطلبة المسجلين في البرنامج
        students = Student.query.filter_by(program_id=program_id).all()

        # معالجة درجات كل طالب
        success_count = 0
        try:
            for student in students:
                midterm_grade = request.form.get(f'midterm_{student.id}', type=float)
                final_grade = request.form.get(f'final_{student.id}', type=float)

                if midterm_grade is not None and final_grade is not None:
                    # التحقق من وجود درجة سابقة
                    existing_grade = Grade.query.filter_by(
                        student_id=student.id,
                        subject_id=subject_id,
                        academic_year_id=academic_year_id,
                        semester_id=semester_id
                    ).first()

                    if existing_grade:
                        # تحديث الدرجة الموجودة
                        existing_grade.midterm_grade = midterm_grade
                        existing_grade.final_grade = final_grade
                        existing_grade.calculate_total()
                        existing_grade.professor_id = professor_id
                    else:
                        # إضافة درجة جديدة
                        grade = Grade(
                            student_id=student.id,
                            subject_id=subject_id,
                            professor_id=professor_id,
                            academic_year_id=academic_year_id,
                            semester_id=semester_id,
                            midterm_grade=midterm_grade,
                            final_grade=final_grade
                        )
                        grade.calculate_total()
                        db.session.add(grade)

                    success_count += 1

            if success_count > 0:
                db.session.commit()
                flash(f'تم حفظ درجات {success_count} طالب بنجاح', 'success')
            else:
                flash('لم يتم إدخال أي درجات', 'warning')

            return redirect(url_for('grades'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء حفظ الدرجات: {str(e)}', 'error')
            return render_template('add_grades.html',
                                 academic_years=academic_years,
                                 semesters=semesters,
                                 programs=programs,
                                 subjects=subjects,
                                 professors=professors)

    return render_template('add_grades.html',
                         academic_years=academic_years,
                         semesters=semesters,
                         programs=programs,
                         subjects=subjects,
                         professors=professors)

# مسارات التقارير
@app.route('/reports/student_transcript')
@login_required
def student_transcript():
    """كشف درجات الطالب"""
    students = Student.query.all()
    selected_student = request.args.get('student_id')

    transcript_data = {}
    cumulative_gpa = 0
    preparatory_gpa = 0
    student = None

    if selected_student:
        student = Student.query.get(selected_student)

        # الحصول على جميع درجات الطالب مجمعة حسب السنة والفصل
        grades = Grade.query.filter_by(student_id=selected_student).all()

        # تجميع الدرجات حسب السنة والفصل
        for grade in grades:
            year_name = grade.academic_year.name
            semester_name = grade.semester.name

            if year_name not in transcript_data:
                transcript_data[year_name] = {}
            if semester_name not in transcript_data[year_name]:
                transcript_data[year_name][semester_name] = []

            transcript_data[year_name][semester_name].append(grade)

        # حساب المعدل التراكمي
        all_grades = Grade.query.filter_by(student_id=selected_student).all()
        if all_grades:
            total_points = sum(grade.total_grade * grade.subject.units for grade in all_grades)
            total_units = sum(grade.subject.units for grade in all_grades)
            cumulative_gpa = total_points / total_units if total_units > 0 else 0

        # حساب معدل السنة التحضيرية
        preparatory_grades = [g for g in all_grades if g.subject.academic_year_type == 'التحضيرية']
        if preparatory_grades:
            prep_points = sum(grade.total_grade * grade.subject.units for grade in preparatory_grades)
            prep_units = sum(grade.subject.units for grade in preparatory_grades)
            preparatory_gpa = prep_points / prep_units if prep_units > 0 else 0

    return render_template('student_transcript.html',
                         transcript_data=transcript_data,
                         student=student,
                         cumulative_gpa=cumulative_gpa,
                         preparatory_gpa=preparatory_gpa,
                         students=students,
                         selected_student=selected_student)

@app.route('/admin/departments/edit/<int:department_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_department(department_id):
    """تعديل قسم"""
    department = Department.query.get_or_404(department_id)
    colleges = College.query.all()

    if request.method == 'POST':
        department.name = request.form.get('name')
        department.head_name = request.form.get('head_name')
        department.secretary_name = request.form.get('secretary_name')
        department.description = request.form.get('description')
        department.college_id = request.form.get('college_id')

        db.session.commit()
        flash('تم تحديث القسم بنجاح', 'success')
        return redirect(url_for('departments'))

    return render_template('edit_department.html', department=department, colleges=colleges)

@app.route('/admin/departments/delete/<int:department_id>', methods=['POST'])
@login_required
@admin_required
def delete_department(department_id):
    """حذف قسم"""
    department = Department.query.get_or_404(department_id)

    # التحقق من وجود برامج أو أساتذة مرتبطة
    if department.programs or department.professors:
        flash('لا يمكن حذف القسم لوجود برامج أو أساتذة مرتبطة به', 'error')
        return redirect(url_for('departments'))

    db.session.delete(department)
    db.session.commit()
    flash('تم حذف القسم بنجاح', 'success')
    return redirect(url_for('departments'))

# مسارات إدارة البرامج الأكاديمية
@app.route('/admin/programs')
@login_required
@admin_required
def programs():
    """صفحة إدارة البرامج الأكاديمية"""
    programs = AcademicProgram.query.all()
    return render_template('programs.html', programs=programs)

@app.route('/admin/programs/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_program():
    """إضافة برنامج أكاديمي جديد"""
    departments = Department.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        program_type = request.form.get('program_type')
        duration = request.form.get('duration')
        description = request.form.get('description')
        department_id = request.form.get('department_id')

        # التحقق من الحقول المطلوبة
        if not name or not program_type or not department_id:
            flash('اسم البرنامج ونوعه والقسم مطلوبان', 'error')
            return render_template('add_program.html', departments=departments)

        # تحديد مدة افتراضية إذا لم يتم اختيارها
        if not duration:
            if program_type == 'ماجستير':
                duration = 'سنتان'
            elif program_type == 'دكتوراه':
                duration = 'ثلاث سنوات'
            elif program_type == 'دبلوم عالي':
                duration = 'سنة واحدة'
            else:
                duration = 'غير محدد'

        try:
            program = AcademicProgram(
                name=name,
                program_type=program_type,
                duration=duration,
                description=description,
                department_id=department_id
            )

            db.session.add(program)
            db.session.commit()
            flash('تم إضافة البرنامج الأكاديمي بنجاح', 'success')
            return redirect(url_for('programs'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة البرنامج: {str(e)}', 'error')
            return render_template('add_program.html', departments=departments)

    return render_template('add_program.html', departments=departments)

@app.route('/admin/programs/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_program(id):
    """تعديل برنامج أكاديمي"""
    program = AcademicProgram.query.get_or_404(id)
    departments = Department.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        program_type = request.form.get('program_type')
        duration = request.form.get('duration')
        department_id = request.form.get('department_id')

        # التحقق من الحقول المطلوبة
        if not name or not program_type or not department_id:
            flash('اسم البرنامج ونوعه والقسم مطلوبان', 'error')
            return render_template('edit_program.html', program=program, departments=departments)

        # تحديد مدة افتراضية إذا لم يتم اختيارها
        if not duration:
            if program_type == 'ماجستير':
                duration = 'سنتان'
            elif program_type == 'دكتوراه':
                duration = 'ثلاث سنوات'
            elif program_type == 'دبلوم عالي':
                duration = 'سنة واحدة'
            else:
                duration = 'غير محدد'

        try:
            program.name = name
            program.program_type = program_type
            program.duration = duration
            program.description = request.form.get('description')
            program.department_id = department_id

            db.session.commit()
            flash('تم تحديث البرنامج الأكاديمي بنجاح', 'success')
            return redirect(url_for('programs'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث البرنامج: {str(e)}', 'error')
            return render_template('edit_program.html', program=program, departments=departments)

    return render_template('edit_program.html', program=program, departments=departments)

@app.route('/admin/programs/delete/<int:id>')
@login_required
@admin_required
def delete_program(id):
    """حذف برنامج أكاديمي"""
    program = AcademicProgram.query.get_or_404(id)

    # التحقق من وجود طلبة أو مواد مرتبطة
    if program.students or program.subjects:
        flash('لا يمكن حذف البرنامج لوجود طلبة أو مواد مرتبطة به', 'error')
        return redirect(url_for('programs'))

    db.session.delete(program)
    db.session.commit()
    flash('تم حذف البرنامج الأكاديمي بنجاح', 'success')
    return redirect(url_for('programs'))

# مسارات إدارة المواد الدراسية
@app.route('/admin/subjects')
@login_required
@admin_required
def subjects():
    """صفحة إدارة المواد الدراسية"""
    subjects = Subject.query.all()
    return render_template('subjects.html', subjects=subjects)

@app.route('/admin/subjects/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_subject():
    """إضافة مادة دراسية جديدة"""
    programs = AcademicProgram.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        units = request.form.get('units')
        hours = request.form.get('hours')
        subject_type = request.form.get('subject_type')
        academic_year_type = request.form.get('academic_year_type')
        description = request.form.get('description')
        program_id = request.form.get('program_id')

        if not name or not units or not program_id:
            flash('اسم المادة والوحدات والبرنامج مطلوبان', 'error')
            return render_template('add_subject.html', programs=programs)

        subject = Subject(
            name=name,
            units=int(units),
            hours=int(hours) if hours else int(units),
            subject_type=subject_type,
            academic_year_type=academic_year_type,
            description=description,
            program_id=program_id
        )

        db.session.add(subject)
        db.session.commit()
        flash('تم إضافة المادة الدراسية بنجاح', 'success')
        return redirect(url_for('subjects'))

    return render_template('add_subject.html', programs=programs)

@app.route('/admin/subjects/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_subject(id):
    """تعديل مادة دراسية"""
    subject = Subject.query.get_or_404(id)
    programs = AcademicProgram.query.all()

    if request.method == 'POST':
        subject.name = request.form.get('name')
        subject.units = int(request.form.get('units'))
        subject.hours = int(request.form.get('hours')) if request.form.get('hours') else subject.units
        subject.subject_type = request.form.get('subject_type')
        subject.academic_year_type = request.form.get('academic_year_type')
        subject.description = request.form.get('description')
        subject.program_id = request.form.get('program_id')

        db.session.commit()
        flash('تم تحديث المادة الدراسية بنجاح', 'success')
        return redirect(url_for('subjects'))

    return render_template('edit_subject.html', subject=subject, programs=programs)

@app.route('/admin/subjects/delete/<int:id>')
@login_required
@admin_required
def delete_subject(id):
    """حذف مادة دراسية"""
    subject = Subject.query.get_or_404(id)

    # التحقق من وجود درجات مرتبطة
    if subject.grades:
        flash('لا يمكن حذف المادة لوجود درجات مرتبطة بها', 'error')
        return redirect(url_for('subjects'))

    db.session.delete(subject)
    db.session.commit()
    flash('تم حذف المادة الدراسية بنجاح', 'success')
    return redirect(url_for('subjects'))

# مسارات إدارة الأساتذة
@app.route('/admin/professors')
@login_required
@admin_required
def professors():
    """صفحة إدارة الأساتذة"""
    professors = Professor.query.all()
    return render_template('professors.html', professors=professors)

@app.route('/admin/professors/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_professor():
    """إضافة أستاذ جديد"""
    departments = Department.query.all()
    specializations = Specialization.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        degree = request.form.get('degree')
        academic_title = request.form.get('academic_title')
        title_date = request.form.get('title_date')
        general_specialization = request.form.get('general_specialization')
        specific_specialization = request.form.get('specific_specialization')
        phone = request.form.get('phone')
        email = request.form.get('email')
        notes = request.form.get('notes')
        department_id = request.form.get('department_id')
        specialization_id = request.form.get('specialization_id')

        if not name or not department_id:
            flash('اسم الأستاذ والقسم مطلوبان', 'error')
            return render_template('add_professor.html',
                                 departments=departments,
                                 specializations=specializations)

        # تحويل تاريخ الحصول على اللقب
        title_date_obj = None
        if title_date:
            try:
                title_date_obj = datetime.strptime(title_date, '%Y-%m-%d').date()
            except ValueError:
                flash('تاريخ غير صحيح', 'error')
                return render_template('add_professor.html',
                                     departments=departments,
                                     specializations=specializations)

        professor = Professor(
            name=name,
            degree=degree,
            academic_title=academic_title,
            title_date=title_date_obj,
            general_specialization=general_specialization,
            specific_specialization=specific_specialization,
            phone=phone,
            email=email,
            notes=notes,
            department_id=department_id,
            specialization_id=specialization_id if specialization_id else None
        )

        db.session.add(professor)
        db.session.commit()
        flash('تم إضافة الأستاذ بنجاح', 'success')
        return redirect(url_for('professors'))

    return render_template('add_professor.html',
                         departments=departments,
                         specializations=specializations)

@app.route('/admin/professors/edit/<int:professor_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_professor(professor_id):
    """تعديل أستاذ"""
    professor = Professor.query.get_or_404(professor_id)
    departments = Department.query.all()
    specializations = Specialization.query.all()

    if request.method == 'POST':
        professor.name = request.form.get('name')
        professor.degree = request.form.get('degree')
        professor.academic_title = request.form.get('academic_title')
        title_date = request.form.get('title_date')
        professor.general_specialization = request.form.get('general_specialization')
        professor.specific_specialization = request.form.get('specific_specialization')
        professor.phone = request.form.get('phone')
        professor.email = request.form.get('email')
        professor.notes = request.form.get('notes')
        professor.department_id = request.form.get('department_id')
        professor.specialization_id = request.form.get('specialization_id') if request.form.get('specialization_id') else None

        # تحويل تاريخ الحصول على اللقب
        if title_date:
            try:
                professor.title_date = datetime.strptime(title_date, '%Y-%m-%d').date()
            except ValueError:
                flash('تاريخ غير صحيح', 'error')
                return render_template('edit_professor.html',
                                     professor=professor,
                                     departments=departments,
                                     specializations=specializations)
        else:
            professor.title_date = None

        db.session.commit()
        flash('تم تحديث بيانات الأستاذ بنجاح', 'success')
        return redirect(url_for('professors'))

    return render_template('edit_professor.html',
                         professor=professor,
                         departments=departments,
                         specializations=specializations)

@app.route('/admin/professors/delete/<int:professor_id>', methods=['POST'])
@login_required
@admin_required
def delete_professor(professor_id):
    """حذف أستاذ"""
    professor = Professor.query.get_or_404(professor_id)

    # التحقق من وجود درجات مرتبطة
    if professor.grades:
        flash('لا يمكن حذف الأستاذ لوجود درجات مرتبطة به', 'error')
        return redirect(url_for('professors'))

    db.session.delete(professor)
    db.session.commit()
    flash('تم حذف الأستاذ بنجاح', 'success')
    return redirect(url_for('professors'))

# مسارات إدارة المستخدمين
@app.route('/admin/users')
@login_required
@admin_required
def users():
    """صفحة إدارة المستخدمين"""
    users = User.query.all()
    return render_template('users.html', users=users)

@app.route('/admin/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """إضافة مستخدم جديد"""
    if request.method == 'POST':
        username = request.form.get('username')
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        phone = request.form.get('phone')
        user_type = request.form.get('user_type')
        password = request.form.get('password')

        if not all([username, full_name, user_type, password]):
            flash('الحقول المطلوبة: اسم المستخدم، الاسم الكامل، نوع المستخدم، كلمة المرور', 'error')
            return render_template('add_user.html')

        # التحقق من عدم وجود اسم المستخدم
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود مسبقاً', 'error')
            return render_template('add_user.html')

        user = User(
            username=username,
            full_name=full_name,
            email=email,
            phone=phone,
            user_type=user_type,
            is_active=True
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()
        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users'))

    return render_template('add_user.html')

# مسارات AJAX للبيانات الديناميكية
@app.route('/api/students/<int:program_id>')
@login_required
def get_students_by_program(program_id):
    """الحصول على الطلبة حسب البرنامج"""
    try:
        students = Student.query.filter_by(program_id=program_id).all()
        return jsonify([{
            'id': student.id,
            'name': student.name,
            'gender': student.gender,
            'status': student.status
        } for student in students])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/subjects/<int:program_id>')
@login_required
def get_subjects_by_program(program_id):
    """الحصول على المواد حسب البرنامج"""
    subjects = Subject.query.filter_by(program_id=program_id).all()
    return jsonify([{
        'id': subject.id,
        'name': subject.name,
        'units': subject.units
    } for subject in subjects])

# تهيئة قاعدة البيانات
def init_database():
    """تهيئة قاعدة البيانات والبيانات الأولية"""
    with app.app_context():
        db.create_all()

        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                full_name='مدير النظام',
                email='<EMAIL>',
                phone='1234567890',
                user_type='admin',
                is_active=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)

        # إضافة التخصصات العلمية
        specializations = ['إدارة أعمال', 'هندسة مدنية', 'هندسة كهربائية', 'علوم حاسوب', 'طب']
        for spec_name in specializations:
            if not Specialization.query.filter_by(name=spec_name).first():
                spec = Specialization(name=spec_name)
                db.session.add(spec)

        # إضافة قنوات القبول
        channels = ['القناة العامة', 'قناة ذوي الشهداء', 'القناة الخاصة']
        for channel_name in channels:
            if not AdmissionChannel.query.filter_by(name=channel_name).first():
                channel = AdmissionChannel(name=channel_name)
                db.session.add(channel)

        # إضافة السنوات الدراسية
        years = ['2023-2024', '2024-2025', '2025-2026']
        for year_name in years:
            if not AcademicYear.query.filter_by(name=year_name).first():
                year = AcademicYear(
                    name=year_name,
                    is_current=(year_name == '2024-2025')
                )
                db.session.add(year)

        # إضافة الفصول الدراسية
        semesters = ['الفصل الدراسي الأول', 'الفصل الدراسي الثاني', 'الدور الثاني']
        for semester_name in semesters:
            if not Semester.query.filter_by(name=semester_name).first():
                semester = Semester(name=semester_name)
                db.session.add(semester)

        # إضافة جامعة نموذجية مع بيانات كاملة
        if not University.query.first():
            university = University(
                name_ar='جامعة بغداد',
                name_en='University of Baghdad',
                address='بغداد - الجادرية'
            )
            db.session.add(university)
            db.session.commit()

            # إضافة كلية نموذجية
            college = College(
                name_ar='كلية الإدارة والاقتصاد',
                name_en='College of Administration and Economics',
                address='بغداد - الجادرية',
                university_id=university.id
            )
            db.session.add(college)
            db.session.commit()

            # إضافة قسم نموذجي
            department = Department(
                name='قسم إدارة الأعمال',
                head_name='أ.د. محمد أحمد علي',
                secretary_name='م.م. فاطمة حسن',
                description='قسم متخصص في إدارة الأعمال والعلوم الإدارية',
                college_id=college.id
            )
            db.session.add(department)
            db.session.commit()

            # إضافة برنامج أكاديمي نموذجي
            program = AcademicProgram(
                name='ماجستير إدارة أعمال',
                program_type='ماجستير',
                duration='سنتان',
                department_id=department.id
            )
            db.session.add(program)
            db.session.commit()

            # إضافة مواد دراسية نموذجية
            subjects_data = [
                {
                    'name': 'الإدارة الاستراتيجية',
                    'units': 3,
                    'hours': 3,
                    'subject_type': 'اجباري',
                    'academic_year_type': 'التحضيرية'
                },
                {
                    'name': 'إدارة الموارد البشرية',
                    'units': 3,
                    'hours': 3,
                    'subject_type': 'اجباري',
                    'academic_year_type': 'التحضيرية'
                },
                {
                    'name': 'التسويق المتقدم',
                    'units': 2,
                    'hours': 2,
                    'subject_type': 'اختياري',
                    'academic_year_type': 'التحضيرية'
                },
                {
                    'name': 'منهجية البحث العلمي',
                    'units': 2,
                    'hours': 2,
                    'subject_type': 'اجباري',
                    'academic_year_type': 'البحثية'
                },
                {
                    'name': 'رسالة الماجستير',
                    'units': 6,
                    'hours': 6,
                    'subject_type': 'اجباري',
                    'academic_year_type': 'البحثية'
                }
            ]

            for subject_data in subjects_data:
                subject = Subject(
                    name=subject_data['name'],
                    units=subject_data['units'],
                    hours=subject_data['hours'],
                    subject_type=subject_data['subject_type'],
                    academic_year_type=subject_data['academic_year_type'],
                    program_id=program.id
                )
                db.session.add(subject)

            # إضافة أستاذ نموذجي
            specialization = Specialization.query.filter_by(name='إدارة أعمال').first()
            professor = Professor(
                name='أ.د. أحمد محمد علي',
                degree='دكتوراه',
                academic_title='أستاذ',
                title_date=date(2015, 9, 1),
                general_specialization='إدارة أعمال',
                specific_specialization='الإدارة الاستراتيجية',
                phone='07901234567',
                email='<EMAIL>',
                department_id=department.id,
                specialization_id=specialization.id
            )
            db.session.add(professor)

            # إضافة طلبة نموذجيين
            current_year = AcademicYear.query.filter_by(is_current=True).first()
            general_channel = AdmissionChannel.query.filter_by(name='القناة العامة').first()

            students_data = [
                {'name': 'محمد عبدالله أحمد', 'gender': 'ذكر'},
                {'name': 'فاطمة حسن محمود', 'gender': 'أنثى'},
                {'name': 'علي خالد محمد', 'gender': 'ذكر'},
                {'name': 'زينب علي حسن', 'gender': 'أنثى'},
                {'name': 'عمر أحمد علي', 'gender': 'ذكر'}
            ]

            for student_data in students_data:
                student = Student(
                    name=student_data['name'],
                    gender=student_data['gender'],
                    status='مستمر بالدراسة',
                    program_id=program.id,
                    admission_channel_id=general_channel.id,
                    specialization_id=specialization.id,
                    academic_year_id=current_year.id
                )
                db.session.add(student)

        db.session.commit()
        print("تم تهيئة قاعدة البيانات بنجاح")
        print("المستخدم الافتراضي: admin / admin123")

# ===== مسارات إدارة الأعوام والفصول الدراسية =====

@app.route('/admin/academic-years')
@login_required
@admin_required
def academic_years():
    """عرض الأعوام الدراسية"""
    years = AcademicYear.query.order_by(AcademicYear.created_at.desc()).all()
    return render_template('academic_years.html', years=years)

@app.route('/admin/academic-years/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_academic_year():
    """إضافة عام دراسي جديد"""
    if request.method == 'POST':
        try:
            name = request.form.get('name')
            is_current = request.form.get('is_current') == 'on'

            # إذا كان العام الحالي، قم بإلغاء تفعيل الأعوام الأخرى
            if is_current:
                AcademicYear.query.update({'is_current': False})

            # إنشاء العام الدراسي الجديد
            academic_year = AcademicYear(
                name=name,
                is_current=is_current
            )

            db.session.add(academic_year)
            db.session.commit()

            flash('تم إضافة العام الدراسي بنجاح', 'success')
            return redirect(url_for('academic_years'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('add_academic_year.html')

@app.route('/admin/academic-years/edit/<int:year_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_academic_year(year_id):
    """تعديل عام دراسي"""
    academic_year = AcademicYear.query.get_or_404(year_id)

    if request.method == 'POST':
        try:
            academic_year.name = request.form.get('name')
            is_current = request.form.get('is_current') == 'on'

            # إذا كان العام الحالي، قم بإلغاء تفعيل الأعوام الأخرى
            if is_current and not academic_year.is_current:
                AcademicYear.query.update({'is_current': False})

            academic_year.is_current = is_current

            db.session.commit()
            flash('تم تحديث العام الدراسي بنجاح', 'success')
            return redirect(url_for('academic_years'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('edit_academic_year.html', year=academic_year)

@app.route('/admin/academic-years/delete/<int:year_id>', methods=['POST'])
@login_required
@admin_required
def delete_academic_year(year_id):
    """حذف عام دراسي"""
    try:
        academic_year = AcademicYear.query.get_or_404(year_id)

        # التحقق من وجود بيانات مرتبطة
        if academic_year.students or academic_year.grades:
            flash('لا يمكن حذف العام الدراسي لوجود بيانات مرتبطة به', 'error')
            return redirect(url_for('academic_years'))

        db.session.delete(academic_year)
        db.session.commit()
        flash('تم حذف العام الدراسي بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('academic_years'))

@app.route('/admin/semesters')
@login_required
@admin_required
def semesters():
    """عرض الفصول الدراسية"""
    semesters = Semester.query.order_by(Semester.created_at.desc()).all()
    return render_template('semesters.html', semesters=semesters)

@app.route('/admin/semesters/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_semester():
    """إضافة فصل دراسي جديد"""
    academic_years = AcademicYear.query.all()

    if request.method == 'POST':
        try:
            name = request.form.get('name')

            # إنشاء الفصل الدراسي الجديد
            semester = Semester(name=name)

            db.session.add(semester)
            db.session.commit()

            flash('تم إضافة الفصل الدراسي بنجاح', 'success')
            return redirect(url_for('semesters'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('add_semester.html', academic_years=academic_years)

@app.route('/admin/semesters/delete/<int:semester_id>', methods=['POST'])
@login_required
@admin_required
def delete_semester(semester_id):
    """حذف فصل دراسي"""
    try:
        semester = Semester.query.get_or_404(semester_id)

        db.session.delete(semester)
        db.session.commit()
        flash('تم حذف الفصل الدراسي بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('semesters'))

if __name__ == '__main__':
    init_database()
    print("\n" + "="*60)
    print("🎓 نظام إدارة الدراسات العليا")
    print("   Graduate Studies Management System")
    print("="*60)
    print("✅ تم تهيئة قاعدة البيانات بنجاح")
    print("🌐 الخادم يعمل على: http://localhost:5001")
    print("🔐 المستخدم الافتراضي: admin / admin123")
    print("⚠️  للإيقاف: اضغط Ctrl+C")
    print("="*60)
    print("🚀 النظام جاهز للاستخدام!")
    print("="*60 + "\n")

    try:
        app.run(debug=False, host='0.0.0.0', port=5001)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
    finally:
        print("👋 شكراً لاستخدام نظام إدارة الدراسات العليا")
