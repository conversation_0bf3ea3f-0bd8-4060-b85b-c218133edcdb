#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات التطبيق
Application Configuration
"""

import os
from datetime import timedelta

class Config:
    # المفتاح السري للتطبيق
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///graduate_system.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    
    # إعدادات رفع الملفات
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # الملفات المسموحة للرفع
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    
    # إعدادات التطبيق
    APP_NAME = 'نظام إدارة الدراسات العليا'
    APP_VERSION = '1.0.0'
    
    # إعدادات اللغة والمنطقة الزمنية
    LANGUAGES = ['ar', 'en']
    BABEL_DEFAULT_LOCALE = 'ar'
    BABEL_DEFAULT_TIMEZONE = 'Asia/Baghdad'
