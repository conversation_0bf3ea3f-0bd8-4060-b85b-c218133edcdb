#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة البيانات الأولية للنظام
Initialize System Data
"""

from app import app, db
from models import *
from datetime import datetime, date

def init_basic_data():
    """إضافة البيانات الأساسية للنظام"""
    
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        
        # إضافة التخصصات العلمية
        specializations = [
            'إدارة أعمال',
            'هندسة مدنية',
            'هندسة كهربائية',
            'علوم حاسوب',
            'طب',
            'صيدلة',
            'قانون',
            'تربية',
            'آداب',
            'علوم'
        ]
        
        for spec_name in specializations:
            if not Specialization.query.filter_by(name=spec_name).first():
                spec = Specialization(name=spec_name)
                db.session.add(spec)
        
        # إضافة قنوات القبول
        admission_channels = [
            'القناة العامة',
            'قناة ذوي الشهداء',
            'قناة ذوي الإعاقة',
            'القناة الخاصة',
            'قناة النازحين'
        ]
        
        for channel_name in admission_channels:
            if not AdmissionChannel.query.filter_by(name=channel_name).first():
                channel = AdmissionChannel(name=channel_name)
                db.session.add(channel)
        
        # إضافة السنوات الدراسية
        academic_years = [
            '2023-2024',
            '2024-2025',
            '2025-2026'
        ]
        
        for year_name in academic_years:
            if not AcademicYear.query.filter_by(name=year_name).first():
                year = AcademicYear(
                    name=year_name,
                    is_current=(year_name == '2024-2025')
                )
                db.session.add(year)
        
        # إضافة الفصول الدراسية
        semesters = [
            'الفصل الدراسي الأول',
            'الفصل الدراسي الثاني',
            'الدور الثاني'
        ]
        
        for semester_name in semesters:
            if not Semester.query.filter_by(name=semester_name).first():
                semester = Semester(name=semester_name)
                db.session.add(semester)
        
        # حفظ البيانات الأساسية
        db.session.commit()
        
        # إضافة جامعة نموذجية
        if not University.query.first():
            university = University(
                name_ar='جامعة بغداد',
                name_en='University of Baghdad',
                address='بغداد - الجادرية'
            )
            db.session.add(university)
            db.session.commit()
            
            # إضافة كلية نموذجية
            college = College(
                name_ar='كلية الإدارة والاقتصاد',
                name_en='College of Administration and Economics',
                address='بغداد - الجادرية',
                university_id=university.id
            )
            db.session.add(college)
            db.session.commit()
            
            # إضافة قسم نموذجي
            department = Department(
                name='قسم إدارة الأعمال',
                head_name='أ.د. محمد أحمد علي',
                secretary_name='م.م. فاطمة حسن',
                description='قسم متخصص في إدارة الأعمال والعلوم الإدارية',
                college_id=college.id
            )
            db.session.add(department)
            db.session.commit()
            
            # إضافة برنامج أكاديمي نموذجي
            program = AcademicProgram(
                name='ماجستير إدارة أعمال',
                program_type='ماجستير',
                duration='سنتان',
                department_id=department.id
            )
            db.session.add(program)
            db.session.commit()
            
            # إضافة مواد دراسية نموذجية
            subjects = [
                {
                    'name': 'الإدارة الاستراتيجية',
                    'units': 3,
                    'hours': 3,
                    'subject_type': 'اجباري',
                    'academic_year_type': 'التحضيرية'
                },
                {
                    'name': 'إدارة الموارد البشرية',
                    'units': 3,
                    'hours': 3,
                    'subject_type': 'اجباري',
                    'academic_year_type': 'التحضيرية'
                },
                {
                    'name': 'التسويق المتقدم',
                    'units': 2,
                    'hours': 2,
                    'subject_type': 'اختياري',
                    'academic_year_type': 'التحضيرية'
                },
                {
                    'name': 'منهجية البحث العلمي',
                    'units': 2,
                    'hours': 2,
                    'subject_type': 'اجباري',
                    'academic_year_type': 'البحثية'
                },
                {
                    'name': 'رسالة الماجستير',
                    'units': 6,
                    'hours': 6,
                    'subject_type': 'اجباري',
                    'academic_year_type': 'البحثية'
                }
            ]
            
            for subject_data in subjects:
                subject = Subject(
                    name=subject_data['name'],
                    units=subject_data['units'],
                    hours=subject_data['hours'],
                    subject_type=subject_data['subject_type'],
                    academic_year_type=subject_data['academic_year_type'],
                    program_id=program.id
                )
                db.session.add(subject)
            
            # إضافة أستاذ نموذجي
            specialization = Specialization.query.filter_by(name='إدارة أعمال').first()
            professor = Professor(
                name='أ.د. أحمد محمد علي',
                degree='دكتوراه',
                academic_title='أستاذ',
                title_date=date(2015, 9, 1),
                general_specialization='إدارة أعمال',
                specific_specialization='الإدارة الاستراتيجية',
                phone='07901234567',
                email='<EMAIL>',
                department_id=department.id,
                specialization_id=specialization.id
            )
            db.session.add(professor)
            
            # إضافة طلبة نموذجيين
            current_year = AcademicYear.query.filter_by(is_current=True).first()
            general_channel = AdmissionChannel.query.filter_by(name='القناة العامة').first()
            
            students_data = [
                {
                    'name': 'محمد عبدالله أحمد',
                    'gender': 'ذكر'
                },
                {
                    'name': 'فاطمة حسن محمود',
                    'gender': 'أنثى'
                },
                {
                    'name': 'علي خالد محمد',
                    'gender': 'ذكر'
                },
                {
                    'name': 'زينب علي حسن',
                    'gender': 'أنثى'
                },
                {
                    'name': 'عمر أحمد علي',
                    'gender': 'ذكر'
                }
            ]
            
            for student_data in students_data:
                student = Student(
                    name=student_data['name'],
                    gender=student_data['gender'],
                    status='مستمر بالدراسة',
                    program_id=program.id,
                    admission_channel_id=general_channel.id,
                    specialization_id=specialization.id,
                    academic_year_id=current_year.id
                )
                db.session.add(student)
            
            db.session.commit()
            print("تم إضافة البيانات النموذجية بنجاح")
        
        print("تم تهيئة النظام بنجاح")

if __name__ == '__main__':
    init_basic_data()
