#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات
Database Models
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import uuid

# سيتم تهيئة db في app.py
db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    user_type = db.Column(db.String(20), nullable=False, default='user')  # admin, user
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

class University(db.Model):
    """نموذج الجامعات"""
    __tablename__ = 'universities'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    address = db.Column(db.Text)
    logo = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    colleges = db.relationship('College', backref='university', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<University {self.name_ar}>'

class College(db.Model):
    """نموذج الكليات"""
    __tablename__ = 'colleges'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    address = db.Column(db.Text)
    logo = db.Column(db.String(255))
    university_id = db.Column(db.Integer, db.ForeignKey('universities.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    departments = db.relationship('Department', backref='college', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<College {self.name_ar}>'

class Department(db.Model):
    """نموذج الأقسام"""
    __tablename__ = 'departments'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    head_name = db.Column(db.String(200))
    secretary_name = db.Column(db.String(200))
    description = db.Column(db.Text)
    logo = db.Column(db.String(255))
    college_id = db.Column(db.Integer, db.ForeignKey('colleges.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    programs = db.relationship('AcademicProgram', backref='department', lazy=True, cascade='all, delete-orphan')
    professors = db.relationship('Professor', backref='department', lazy=True)
    
    def __repr__(self):
        return f'<Department {self.name}>'

class Specialization(db.Model):
    """نموذج التخصصات العلمية"""
    __tablename__ = 'specializations'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    professors = db.relationship('Professor', backref='specialization', lazy=True)
    students = db.relationship('Student', backref='specialization', lazy=True)
    
    def __repr__(self):
        return f'<Specialization {self.name}>'

class AdmissionChannel(db.Model):
    """نموذج قنوات القبول"""
    __tablename__ = 'admission_channels'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    students = db.relationship('Student', backref='admission_channel', lazy=True)
    
    def __repr__(self):
        return f'<AdmissionChannel {self.name}>'

class AcademicYear(db.Model):
    """نموذج السنوات الدراسية"""
    __tablename__ = 'academic_years'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    is_current = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    students = db.relationship('Student', backref='academic_year', lazy=True)
    grades = db.relationship('Grade', backref='academic_year', lazy=True)
    
    def __repr__(self):
        return f'<AcademicYear {self.name}>'

class Semester(db.Model):
    """نموذج الفصول الدراسية"""
    __tablename__ = 'semesters'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    grades = db.relationship('Grade', backref='semester', lazy=True)

    def __repr__(self):
        return f'<Semester {self.name}>'

class AcademicProgram(db.Model):
    """نموذج البرامج الأكاديمية"""
    __tablename__ = 'academic_programs'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    program_type = db.Column(db.String(50), nullable=False)  # ماجستير، دكتوراه، دبلوم عالي
    duration = db.Column(db.String(50), nullable=False)  # سنة، سنتان، ثلاث سنوات، إلخ
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    subjects = db.relationship('Subject', backref='program', lazy=True, cascade='all, delete-orphan')
    students = db.relationship('Student', backref='program', lazy=True)

    def __repr__(self):
        return f'<AcademicProgram {self.name}>'

class Subject(db.Model):
    """نموذج المواد الدراسية"""
    __tablename__ = 'subjects'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    units = db.Column(db.Integer, nullable=False)
    hours = db.Column(db.Integer, nullable=False)
    subject_type = db.Column(db.String(20), nullable=False)  # اجباري، اختياري
    academic_year_type = db.Column(db.String(20), nullable=False)  # التحضيرية، البحثية
    program_id = db.Column(db.Integer, db.ForeignKey('academic_programs.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    grades = db.relationship('Grade', backref='subject', lazy=True)

    def __repr__(self):
        return f'<Subject {self.name}>'

class Professor(db.Model):
    """نموذج الأساتذة الأكاديميين"""
    __tablename__ = 'professors'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    degree = db.Column(db.String(100))
    academic_title = db.Column(db.String(100))
    title_date = db.Column(db.Date)
    general_specialization = db.Column(db.String(200))
    specific_specialization = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    notes = db.Column(db.Text)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=False)
    specialization_id = db.Column(db.Integer, db.ForeignKey('specializations.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    grades = db.relationship('Grade', backref='professor', lazy=True)

    def __repr__(self):
        return f'<Professor {self.name}>'

class Student(db.Model):
    """نموذج الطلبة"""
    __tablename__ = 'students'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    gender = db.Column(db.String(10), nullable=False)  # ذكر، أنثى
    status = db.Column(db.String(50), nullable=False, default='مستمر بالدراسة')
    program_id = db.Column(db.Integer, db.ForeignKey('academic_programs.id'), nullable=False)
    admission_channel_id = db.Column(db.Integer, db.ForeignKey('admission_channels.id'), nullable=False)
    specialization_id = db.Column(db.Integer, db.ForeignKey('specializations.id'), nullable=False)
    academic_year_id = db.Column(db.Integer, db.ForeignKey('academic_years.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    grades = db.relationship('Grade', backref='student', lazy=True)

    def __repr__(self):
        return f'<Student {self.name}>'

class Grade(db.Model):
    """نموذج الدرجات والنتائج"""
    __tablename__ = 'grades'

    id = db.Column(db.Integer, primary_key=True)
    midterm_grade = db.Column(db.Float, default=0.0)  # درجة السعي
    final_grade = db.Column(db.Float, default=0.0)    # درجة الامتحان النهائي
    total_grade = db.Column(db.Float, default=0.0)    # الدرجة الكاملة

    # المفاتيح الخارجية
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'), nullable=False)
    professor_id = db.Column(db.Integer, db.ForeignKey('professors.id'), nullable=False)
    academic_year_id = db.Column(db.Integer, db.ForeignKey('academic_years.id'), nullable=False)
    semester_id = db.Column(db.Integer, db.ForeignKey('semesters.id'), nullable=False)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # فهرس فريد لمنع تكرار الدرجات
    __table_args__ = (db.UniqueConstraint('student_id', 'subject_id', 'academic_year_id', 'semester_id'),)

    @property
    def grade_letter(self):
        """حساب التقدير الحرفي"""
        if self.total_grade >= 90:
            return 'امتياز'
        elif self.total_grade >= 80:
            return 'جيد جداً'
        elif self.total_grade >= 70:
            return 'جيد'
        elif self.total_grade >= 60:
            return 'مقبول'
        else:
            return 'ضعيف'

    @property
    def is_passed(self):
        """التحقق من النجاح"""
        return self.total_grade >= 60

    def calculate_total(self):
        """حساب الدرجة الكاملة"""
        self.total_grade = self.midterm_grade + self.final_grade
        return self.total_grade

    def __repr__(self):
        return f'<Grade {self.student.name} - {self.subject.name}: {self.total_grade}>'

class AcademicYear(db.Model):
    """نموذج العام الدراسي"""
    __tablename__ = 'academic_years'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # مثل: 2023-2024
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    is_current = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    semesters = db.relationship('Semester', backref='academic_year', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<AcademicYear {self.name}>'

class Semester(db.Model):
    """نموذج الفصل الدراسي"""
    __tablename__ = 'semesters'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # مثل: الفصل الأول، الفصل الثاني، الفصل الصيفي
    semester_type = db.Column(db.String(20), nullable=False)  # fall, spring, summer
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    registration_start = db.Column(db.Date)
    registration_end = db.Column(db.Date)
    is_current = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # المفاتيح الخارجية
    academic_year_id = db.Column(db.Integer, db.ForeignKey('academic_years.id'), nullable=False)

    def __repr__(self):
        return f'<Semester {self.name} - {self.academic_year.name}>'
