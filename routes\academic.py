#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات إدارة الأعوام والفصول الدراسية
Academic Years and Semesters Routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, AcademicYear, Semester
from datetime import datetime, date

academic_bp = Blueprint('academic', __name__)

@academic_bp.route('/academic-years')
@login_required
def academic_years():
    """عرض الأعوام الدراسية"""
    years = AcademicYear.query.order_by(AcademicYear.start_date.desc()).all()
    return render_template('academic_years.html', years=years)

@academic_bp.route('/add-academic-year', methods=['GET', 'POST'])
@login_required
def add_academic_year():
    """إضافة عام دراسي جديد"""
    if request.method == 'POST':
        try:
            name = request.form.get('name')
            start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date()
            end_date = datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date()
            is_current = request.form.get('is_current') == 'on'
            
            # التحقق من صحة البيانات
            if start_date >= end_date:
                flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error')
                return render_template('add_academic_year.html')
            
            # إذا كان العام الحالي، قم بإلغاء تفعيل الأعوام الأخرى
            if is_current:
                AcademicYear.query.update({'is_current': False})
            
            # إنشاء العام الدراسي الجديد
            academic_year = AcademicYear(
                name=name,
                start_date=start_date,
                end_date=end_date,
                is_current=is_current
            )
            
            db.session.add(academic_year)
            db.session.commit()
            
            flash('تم إضافة العام الدراسي بنجاح', 'success')
            return redirect(url_for('academic.academic_years'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('add_academic_year.html')

@academic_bp.route('/edit-academic-year/<int:year_id>', methods=['GET', 'POST'])
@login_required
def edit_academic_year(year_id):
    """تعديل عام دراسي"""
    academic_year = AcademicYear.query.get_or_404(year_id)
    
    if request.method == 'POST':
        try:
            academic_year.name = request.form.get('name')
            academic_year.start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date()
            academic_year.end_date = datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date()
            is_current = request.form.get('is_current') == 'on'
            
            # التحقق من صحة البيانات
            if academic_year.start_date >= academic_year.end_date:
                flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error')
                return render_template('edit_academic_year.html', year=academic_year)
            
            # إذا كان العام الحالي، قم بإلغاء تفعيل الأعوام الأخرى
            if is_current and not academic_year.is_current:
                AcademicYear.query.update({'is_current': False})
            
            academic_year.is_current = is_current
            
            db.session.commit()
            flash('تم تحديث العام الدراسي بنجاح', 'success')
            return redirect(url_for('academic.academic_years'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('edit_academic_year.html', year=academic_year)

@academic_bp.route('/delete-academic-year/<int:year_id>', methods=['POST'])
@login_required
def delete_academic_year(year_id):
    """حذف عام دراسي"""
    try:
        academic_year = AcademicYear.query.get_or_404(year_id)
        
        # التحقق من وجود فصول مرتبطة
        if academic_year.semesters:
            flash('لا يمكن حذف العام الدراسي لوجود فصول دراسية مرتبطة به', 'error')
            return redirect(url_for('academic.academic_years'))
        
        db.session.delete(academic_year)
        db.session.commit()
        flash('تم حذف العام الدراسي بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'error')
    
    return redirect(url_for('academic.academic_years'))

@academic_bp.route('/semesters')
@login_required
def semesters():
    """عرض الفصول الدراسية"""
    semesters = Semester.query.join(AcademicYear).order_by(
        AcademicYear.start_date.desc(), 
        Semester.start_date.asc()
    ).all()
    return render_template('semesters.html', semesters=semesters)

@academic_bp.route('/add-semester', methods=['GET', 'POST'])
@login_required
def add_semester():
    """إضافة فصل دراسي جديد"""
    academic_years = AcademicYear.query.filter_by(is_active=True).order_by(AcademicYear.start_date.desc()).all()
    
    if request.method == 'POST':
        try:
            name = request.form.get('name')
            semester_type = request.form.get('semester_type')
            academic_year_id = request.form.get('academic_year_id')
            start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date()
            end_date = datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date()
            registration_start = request.form.get('registration_start')
            registration_end = request.form.get('registration_end')
            is_current = request.form.get('is_current') == 'on'
            
            # تحويل تواريخ التسجيل إذا كانت موجودة
            if registration_start:
                registration_start = datetime.strptime(registration_start, '%Y-%m-%d').date()
            if registration_end:
                registration_end = datetime.strptime(registration_end, '%Y-%m-%d').date()
            
            # التحقق من صحة البيانات
            if start_date >= end_date:
                flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error')
                return render_template('add_semester.html', academic_years=academic_years)
            
            # إذا كان الفصل الحالي، قم بإلغاء تفعيل الفصول الأخرى
            if is_current:
                Semester.query.update({'is_current': False})
            
            # إنشاء الفصل الدراسي الجديد
            semester = Semester(
                name=name,
                semester_type=semester_type,
                academic_year_id=academic_year_id,
                start_date=start_date,
                end_date=end_date,
                registration_start=registration_start,
                registration_end=registration_end,
                is_current=is_current
            )
            
            db.session.add(semester)
            db.session.commit()
            
            flash('تم إضافة الفصل الدراسي بنجاح', 'success')
            return redirect(url_for('academic.semesters'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('add_semester.html', academic_years=academic_years)

@academic_bp.route('/edit-semester/<int:semester_id>', methods=['GET', 'POST'])
@login_required
def edit_semester(semester_id):
    """تعديل فصل دراسي"""
    semester = Semester.query.get_or_404(semester_id)
    academic_years = AcademicYear.query.filter_by(is_active=True).order_by(AcademicYear.start_date.desc()).all()
    
    if request.method == 'POST':
        try:
            semester.name = request.form.get('name')
            semester.semester_type = request.form.get('semester_type')
            semester.academic_year_id = request.form.get('academic_year_id')
            semester.start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date()
            semester.end_date = datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date()
            
            registration_start = request.form.get('registration_start')
            registration_end = request.form.get('registration_end')
            
            if registration_start:
                semester.registration_start = datetime.strptime(registration_start, '%Y-%m-%d').date()
            if registration_end:
                semester.registration_end = datetime.strptime(registration_end, '%Y-%m-%d').date()
            
            is_current = request.form.get('is_current') == 'on'
            
            # التحقق من صحة البيانات
            if semester.start_date >= semester.end_date:
                flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error')
                return render_template('edit_semester.html', semester=semester, academic_years=academic_years)
            
            # إذا كان الفصل الحالي، قم بإلغاء تفعيل الفصول الأخرى
            if is_current and not semester.is_current:
                Semester.query.update({'is_current': False})
            
            semester.is_current = is_current
            
            db.session.commit()
            flash('تم تحديث الفصل الدراسي بنجاح', 'success')
            return redirect(url_for('academic.semesters'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')
    
    return render_template('edit_semester.html', semester=semester, academic_years=academic_years)

@academic_bp.route('/delete-semester/<int:semester_id>', methods=['POST'])
@login_required
def delete_semester(semester_id):
    """حذف فصل دراسي"""
    try:
        semester = Semester.query.get_or_404(semester_id)
        
        db.session.delete(semester)
        db.session.commit()
        flash('تم حذف الفصل الدراسي بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'error')
    
    return redirect(url_for('academic.semesters'))

@academic_bp.route('/set-current-year/<int:year_id>', methods=['POST'])
@login_required
def set_current_year(year_id):
    """تعيين العام الدراسي الحالي"""
    try:
        # إلغاء تفعيل جميع الأعوام
        AcademicYear.query.update({'is_current': False})
        
        # تفعيل العام المحدد
        academic_year = AcademicYear.query.get_or_404(year_id)
        academic_year.is_current = True
        
        db.session.commit()
        flash('تم تعيين العام الدراسي الحالي بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'error')
    
    return redirect(url_for('academic.academic_years'))

@academic_bp.route('/set-current-semester/<int:semester_id>', methods=['POST'])
@login_required
def set_current_semester(semester_id):
    """تعيين الفصل الدراسي الحالي"""
    try:
        # إلغاء تفعيل جميع الفصول
        Semester.query.update({'is_current': False})
        
        # تفعيل الفصل المحدد
        semester = Semester.query.get_or_404(semester_id)
        semester.is_current = True
        
        db.session.commit()
        flash('تم تعيين الفصل الدراسي الحالي بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ: {str(e)}', 'error')
    
    return redirect(url_for('academic.semesters'))
