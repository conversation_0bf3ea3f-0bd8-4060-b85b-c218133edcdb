#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات الإدارة
Administration Routes
"""

from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from routes.auth import admin_required
from models import *
from werkzeug.utils import secure_filename
import os
from datetime import datetime

bp = Blueprint('admin', __name__, url_prefix='/admin')

# إدارة الجامعات
@bp.route('/universities')
@login_required
@admin_required
def universities():
    """صفحة إدارة الجامعات"""
    universities = University.query.all()
    return render_template('admin/universities.html', universities=universities)

@bp.route('/universities/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_university():
    """إضافة جامعة جديدة"""
    if request.method == 'POST':
        name_ar = request.form.get('name_ar')
        name_en = request.form.get('name_en')
        address = request.form.get('address')
        
        if not name_ar:
            flash('اسم الجامعة بالعربية مطلوب', 'error')
            return render_template('admin/add_university.html')
        
        university = University(
            name_ar=name_ar,
            name_en=name_en,
            address=address
        )
        
        db.session.add(university)
        db.session.commit()
        flash('تم إضافة الجامعة بنجاح', 'success')
        return redirect(url_for('admin.universities'))
    
    return render_template('admin/add_university.html')

@bp.route('/universities/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_university(id):
    """تعديل جامعة"""
    university = University.query.get_or_404(id)
    
    if request.method == 'POST':
        university.name_ar = request.form.get('name_ar')
        university.name_en = request.form.get('name_en')
        university.address = request.form.get('address')
        
        db.session.commit()
        flash('تم تحديث الجامعة بنجاح', 'success')
        return redirect(url_for('admin.universities'))
    
    return render_template('admin/edit_university.html', university=university)

@bp.route('/universities/delete/<int:id>')
@login_required
@admin_required
def delete_university(id):
    """حذف جامعة"""
    university = University.query.get_or_404(id)
    db.session.delete(university)
    db.session.commit()
    flash('تم حذف الجامعة بنجاح', 'success')
    return redirect(url_for('admin.universities'))

# إدارة الكليات
@bp.route('/colleges')
@login_required
@admin_required
def colleges():
    """صفحة إدارة الكليات"""
    colleges = College.query.all()
    return render_template('admin/colleges.html', colleges=colleges)

@bp.route('/colleges/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_college():
    """إضافة كلية جديدة"""
    universities = University.query.all()
    
    if request.method == 'POST':
        name_ar = request.form.get('name_ar')
        name_en = request.form.get('name_en')
        address = request.form.get('address')
        university_id = request.form.get('university_id')
        
        if not name_ar or not university_id:
            flash('اسم الكلية والجامعة مطلوبان', 'error')
            return render_template('admin/add_college.html', universities=universities)
        
        college = College(
            name_ar=name_ar,
            name_en=name_en,
            address=address,
            university_id=university_id
        )
        
        db.session.add(college)
        db.session.commit()
        flash('تم إضافة الكلية بنجاح', 'success')
        return redirect(url_for('admin.colleges'))
    
    return render_template('admin/add_college.html', universities=universities)

# إدارة الأقسام
@bp.route('/departments')
@login_required
@admin_required
def departments():
    """صفحة إدارة الأقسام"""
    departments = Department.query.all()
    return render_template('admin/departments.html', departments=departments)

@bp.route('/departments/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_department():
    """إضافة قسم جديد"""
    colleges = College.query.all()
    
    if request.method == 'POST':
        name = request.form.get('name')
        head_name = request.form.get('head_name')
        secretary_name = request.form.get('secretary_name')
        description = request.form.get('description')
        college_id = request.form.get('college_id')
        
        if not name or not college_id:
            flash('اسم القسم والكلية مطلوبان', 'error')
            return render_template('admin/add_department.html', colleges=colleges)
        
        department = Department(
            name=name,
            head_name=head_name,
            secretary_name=secretary_name,
            description=description,
            college_id=college_id
        )
        
        db.session.add(department)
        db.session.commit()
        flash('تم إضافة القسم بنجاح', 'success')
        return redirect(url_for('admin.departments'))
    
    return render_template('admin/add_department.html', colleges=colleges)

# إدارة البرامج الأكاديمية
@bp.route('/programs')
@login_required
@admin_required
def programs():
    """صفحة إدارة البرامج الأكاديمية"""
    programs = AcademicProgram.query.all()
    return render_template('admin/programs.html', programs=programs)

@bp.route('/programs/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_program():
    """إضافة برنامج أكاديمي جديد"""
    departments = Department.query.all()
    
    if request.method == 'POST':
        name = request.form.get('name')
        program_type = request.form.get('program_type')
        duration = request.form.get('duration')
        department_id = request.form.get('department_id')
        
        if not name or not program_type or not duration or not department_id:
            flash('جميع الحقول مطلوبة', 'error')
            return render_template('admin/add_program.html', departments=departments)
        
        program = AcademicProgram(
            name=name,
            program_type=program_type,
            duration=duration,
            department_id=department_id
        )
        
        db.session.add(program)
        db.session.commit()
        flash('تم إضافة البرنامج الأكاديمي بنجاح', 'success')
        return redirect(url_for('admin.programs'))
    
    return render_template('admin/add_program.html', departments=departments)

# إدارة المواد الدراسية
@bp.route('/subjects')
@login_required
@admin_required
def subjects():
    """صفحة إدارة المواد الدراسية"""
    subjects = Subject.query.all()
    return render_template('admin/subjects.html', subjects=subjects)

@bp.route('/subjects/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_subject():
    """إضافة مادة دراسية جديدة"""
    programs = AcademicProgram.query.all()
    
    if request.method == 'POST':
        name = request.form.get('name')
        units = request.form.get('units', type=int)
        hours = request.form.get('hours', type=int)
        subject_type = request.form.get('subject_type')
        academic_year_type = request.form.get('academic_year_type')
        program_id = request.form.get('program_id')
        
        if not all([name, units, hours, subject_type, academic_year_type, program_id]):
            flash('جميع الحقول مطلوبة', 'error')
            return render_template('admin/add_subject.html', programs=programs)
        
        subject = Subject(
            name=name,
            units=units,
            hours=hours,
            subject_type=subject_type,
            academic_year_type=academic_year_type,
            program_id=program_id
        )
        
        db.session.add(subject)
        db.session.commit()
        flash('تم إضافة المادة الدراسية بنجاح', 'success')
        return redirect(url_for('admin.subjects'))
    
    return render_template('admin/add_subject.html', programs=programs)

# إدارة الأساتذة
@bp.route('/professors')
@login_required
@admin_required
def professors():
    """صفحة إدارة الأساتذة"""
    professors = Professor.query.all()
    return render_template('admin/professors.html', professors=professors)

@bp.route('/professors/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_professor():
    """إضافة أستاذ جديد"""
    departments = Department.query.all()
    specializations = Specialization.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        degree = request.form.get('degree')
        academic_title = request.form.get('academic_title')
        title_date = request.form.get('title_date')
        general_specialization = request.form.get('general_specialization')
        specific_specialization = request.form.get('specific_specialization')
        phone = request.form.get('phone')
        email = request.form.get('email')
        notes = request.form.get('notes')
        department_id = request.form.get('department_id')
        specialization_id = request.form.get('specialization_id')

        if not name or not department_id:
            flash('اسم الأستاذ والقسم مطلوبان', 'error')
            return render_template('admin/add_professor.html',
                                 departments=departments,
                                 specializations=specializations)

        # تحويل تاريخ الحصول على اللقب
        title_date_obj = None
        if title_date:
            try:
                title_date_obj = datetime.strptime(title_date, '%Y-%m-%d').date()
            except ValueError:
                flash('تاريخ غير صحيح', 'error')
                return render_template('admin/add_professor.html',
                                     departments=departments,
                                     specializations=specializations)

        professor = Professor(
            name=name,
            degree=degree,
            academic_title=academic_title,
            title_date=title_date_obj,
            general_specialization=general_specialization,
            specific_specialization=specific_specialization,
            phone=phone,
            email=email,
            notes=notes,
            department_id=department_id,
            specialization_id=specialization_id if specialization_id else None
        )

        db.session.add(professor)
        db.session.commit()
        flash('تم إضافة الأستاذ بنجاح', 'success')
        return redirect(url_for('admin.professors'))

    return render_template('admin/add_professor.html',
                         departments=departments,
                         specializations=specializations)

# إدارة الطلبة
@bp.route('/students')
@login_required
@admin_required
def students():
    """صفحة إدارة الطلبة"""
    students = Student.query.all()
    return render_template('admin/students.html', students=students)

@bp.route('/students/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_student():
    """إضافة طالب جديد"""
    programs = AcademicProgram.query.all()
    admission_channels = AdmissionChannel.query.all()
    specializations = Specialization.query.all()
    academic_years = AcademicYear.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        gender = request.form.get('gender')
        status = request.form.get('status', 'مستمر بالدراسة')
        program_id = request.form.get('program_id')
        admission_channel_id = request.form.get('admission_channel_id')
        specialization_id = request.form.get('specialization_id')
        academic_year_id = request.form.get('academic_year_id')

        if not all([name, gender, program_id, admission_channel_id, specialization_id, academic_year_id]):
            flash('جميع الحقول مطلوبة', 'error')
            return render_template('admin/add_student.html',
                                 programs=programs,
                                 admission_channels=admission_channels,
                                 specializations=specializations,
                                 academic_years=academic_years)

        student = Student(
            name=name,
            gender=gender,
            status=status,
            program_id=program_id,
            admission_channel_id=admission_channel_id,
            specialization_id=specialization_id,
            academic_year_id=academic_year_id
        )

        db.session.add(student)
        db.session.commit()
        flash('تم إضافة الطالب بنجاح', 'success')
        return redirect(url_for('admin.students'))

    return render_template('admin/add_student.html',
                         programs=programs,
                         admission_channels=admission_channels,
                         specializations=specializations,
                         academic_years=academic_years)

# إدارة الدرجات
@bp.route('/grades')
@login_required
@admin_required
def grades():
    """صفحة إدارة الدرجات"""
    academic_years = AcademicYear.query.all()
    semesters = Semester.query.all()
    programs = AcademicProgram.query.all()
    subjects = Subject.query.all()

    # فلترة البيانات حسب المعايير المختارة
    selected_year = request.args.get('academic_year_id')
    selected_semester = request.args.get('semester_id')
    selected_program = request.args.get('program_id')
    selected_subject = request.args.get('subject_id')

    grades_query = Grade.query

    if selected_year:
        grades_query = grades_query.filter(Grade.academic_year_id == selected_year)
    if selected_semester:
        grades_query = grades_query.filter(Grade.semester_id == selected_semester)
    if selected_program:
        grades_query = grades_query.join(Student).filter(Student.program_id == selected_program)
    if selected_subject:
        grades_query = grades_query.filter(Grade.subject_id == selected_subject)

    grades = grades_query.all()

    return render_template('admin/grades.html',
                         grades=grades,
                         academic_years=academic_years,
                         semesters=semesters,
                         programs=programs,
                         subjects=subjects,
                         selected_year=selected_year,
                         selected_semester=selected_semester,
                         selected_program=selected_program,
                         selected_subject=selected_subject)

@bp.route('/grades/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_grades():
    """إضافة درجات للطلبة"""
    academic_years = AcademicYear.query.all()
    semesters = Semester.query.all()
    programs = AcademicProgram.query.all()
    subjects = Subject.query.all()
    professors = Professor.query.all()

    if request.method == 'POST':
        academic_year_id = request.form.get('academic_year_id')
        semester_id = request.form.get('semester_id')
        program_id = request.form.get('program_id')
        subject_id = request.form.get('subject_id')
        professor_id = request.form.get('professor_id')

        if not all([academic_year_id, semester_id, program_id, subject_id, professor_id]):
            flash('يرجى اختيار جميع المعايير المطلوبة', 'error')
            return render_template('admin/add_grades.html',
                                 academic_years=academic_years,
                                 semesters=semesters,
                                 programs=programs,
                                 subjects=subjects,
                                 professors=professors)

        # الحصول على الطلبة المسجلين في البرنامج
        students = Student.query.filter_by(program_id=program_id).all()

        # معالجة درجات كل طالب
        success_count = 0
        for student in students:
            midterm_grade = request.form.get(f'midterm_{student.id}', type=float)
            final_grade = request.form.get(f'final_{student.id}', type=float)

            if midterm_grade is not None and final_grade is not None:
                # التحقق من وجود درجة سابقة
                existing_grade = Grade.query.filter_by(
                    student_id=student.id,
                    subject_id=subject_id,
                    academic_year_id=academic_year_id,
                    semester_id=semester_id
                ).first()

                if existing_grade:
                    # تحديث الدرجة الموجودة
                    existing_grade.midterm_grade = midterm_grade
                    existing_grade.final_grade = final_grade
                    existing_grade.calculate_total()
                    existing_grade.professor_id = professor_id
                else:
                    # إضافة درجة جديدة
                    grade = Grade(
                        student_id=student.id,
                        subject_id=subject_id,
                        professor_id=professor_id,
                        academic_year_id=academic_year_id,
                        semester_id=semester_id,
                        midterm_grade=midterm_grade,
                        final_grade=final_grade
                    )
                    grade.calculate_total()
                    db.session.add(grade)

                success_count += 1

        if success_count > 0:
            db.session.commit()
            flash(f'تم حفظ درجات {success_count} طالب بنجاح', 'success')
        else:
            flash('لم يتم إدخال أي درجات', 'warning')

        return redirect(url_for('admin.grades'))

    return render_template('admin/add_grades.html',
                         academic_years=academic_years,
                         semesters=semesters,
                         programs=programs,
                         subjects=subjects,
                         professors=professors)

# إدارة المستخدمين
@bp.route('/users')
@login_required
@admin_required
def users():
    """صفحة إدارة المستخدمين"""
    users = User.query.all()
    return render_template('admin/users.html', users=users)

@bp.route('/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """إضافة مستخدم جديد"""
    if request.method == 'POST':
        username = request.form.get('username')
        full_name = request.form.get('full_name')
        email = request.form.get('email')
        phone = request.form.get('phone')
        user_type = request.form.get('user_type')
        password = request.form.get('password')

        if not all([username, full_name, user_type, password]):
            flash('الحقول المطلوبة: اسم المستخدم، الاسم الكامل، نوع المستخدم، كلمة المرور', 'error')
            return render_template('admin/add_user.html')

        # التحقق من عدم وجود اسم المستخدم
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود مسبقاً', 'error')
            return render_template('admin/add_user.html')

        user = User(
            username=username,
            full_name=full_name,
            email=email,
            phone=phone,
            user_type=user_type,
            is_active=True
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()
        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('admin.users'))

    return render_template('admin/add_user.html')
