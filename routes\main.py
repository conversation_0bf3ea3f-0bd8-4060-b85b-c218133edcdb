#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
المسارات الرئيسية
Main Routes
"""

from flask import Blueprint, render_template, request, flash, redirect, url_for
from flask_login import login_required, current_user
from models import *
from sqlalchemy import func

bp = Blueprint('main', __name__)

@bp.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return redirect(url_for('auth.login'))

@bp.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    # إحصائيات عامة
    stats = {
        'universities': University.query.count(),
        'colleges': College.query.count(),
        'departments': Department.query.count(),
        'programs': AcademicProgram.query.count(),
        'subjects': Subject.query.count(),
        'professors': Professor.query.count(),
        'students': Student.query.count(),
        'grades': Grade.query.count()
    }
    
    # إحصائيات الطلبة حسب الحالة
    student_status_stats = db.session.query(
        Student.status,
        func.count(Student.id).label('count')
    ).group_by(Student.status).all()
    
    # إحصائيات الطلبة حسب البرنامج
    program_stats = db.session.query(
        AcademicProgram.name,
        AcademicProgram.program_type,
        func.count(Student.id).label('count')
    ).join(Student).group_by(AcademicProgram.id).all()
    
    # أحدث الطلبة المسجلين
    recent_students = Student.query.order_by(Student.created_at.desc()).limit(5).all()
    
    # أحدث الدرجات المدخلة
    recent_grades = Grade.query.order_by(Grade.created_at.desc()).limit(5).all()
    
    return render_template('main/dashboard.html',
                         stats=stats,
                         student_status_stats=student_status_stats,
                         program_stats=program_stats,
                         recent_students=recent_students,
                         recent_grades=recent_grades)

@bp.route('/search')
@login_required
def search():
    """البحث العام"""
    query = request.args.get('q', '').strip()
    results = {
        'students': [],
        'professors': [],
        'subjects': [],
        'programs': []
    }
    
    if query:
        # البحث في الطلبة
        results['students'] = Student.query.filter(
            Student.name.contains(query)
        ).limit(10).all()
        
        # البحث في الأساتذة
        results['professors'] = Professor.query.filter(
            Professor.name.contains(query)
        ).limit(10).all()
        
        # البحث في المواد
        results['subjects'] = Subject.query.filter(
            Subject.name.contains(query)
        ).limit(10).all()
        
        # البحث في البرامج
        results['programs'] = AcademicProgram.query.filter(
            AcademicProgram.name.contains(query)
        ).limit(10).all()
    
    return render_template('main/search.html', query=query, results=results)

@bp.route('/help')
@login_required
def help():
    """صفحة المساعدة"""
    return render_template('main/help.html')

@bp.route('/about')
@login_required
def about():
    """حول النظام"""
    return render_template('main/about.html')
