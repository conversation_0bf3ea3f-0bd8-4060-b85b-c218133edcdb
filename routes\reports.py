#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات التقارير
Reports Routes
"""

from flask import Blueprint, render_template, request, flash, redirect, url_for, make_response
from flask_login import login_required, current_user
from models import *
from sqlalchemy import func, and_
from datetime import datetime

bp = Blueprint('reports', __name__, url_prefix='/reports')

@bp.route('/student_grades')
@login_required
def student_grades():
    """كشوف درجات الطلبة"""
    academic_years = AcademicYear.query.all()
    semesters = Semester.query.all()
    programs = AcademicProgram.query.all()
    students = Student.query.all()
    
    # فلترة البيانات
    selected_year = request.args.get('academic_year_id')
    selected_semester = request.args.get('semester_id')
    selected_program = request.args.get('program_id')
    selected_student = request.args.get('student_id')
    
    grades = []
    student_info = None
    semester_gpa = 0
    
    if selected_student and selected_year and selected_semester:
        student_info = Student.query.get(selected_student)
        grades = Grade.query.filter_by(
            student_id=selected_student,
            academic_year_id=selected_year,
            semester_id=selected_semester
        ).all()
        
        # حساب المعدل الفصلي
        if grades:
            total_points = sum(grade.total_grade * grade.subject.units for grade in grades)
            total_units = sum(grade.subject.units for grade in grades)
            semester_gpa = total_points / total_units if total_units > 0 else 0
    
    return render_template('reports/student_grades.html',
                         grades=grades,
                         student_info=student_info,
                         semester_gpa=semester_gpa,
                         academic_years=academic_years,
                         semesters=semesters,
                         programs=programs,
                         students=students,
                         selected_year=selected_year,
                         selected_semester=selected_semester,
                         selected_program=selected_program,
                         selected_student=selected_student)

@bp.route('/student_transcript')
@login_required
def student_transcript():
    """الكشف الكامل لدرجات الطالب"""
    students = Student.query.all()
    selected_student = request.args.get('student_id')
    
    transcript_data = {}
    cumulative_gpa = 0
    preparatory_gpa = 0
    
    if selected_student:
        student = Student.query.get(selected_student)
        
        # الحصول على جميع درجات الطالب مجمعة حسب السنة والفصل
        grades = Grade.query.filter_by(student_id=selected_student).all()
        
        # تجميع الدرجات حسب السنة والفصل
        for grade in grades:
            year_name = grade.academic_year.name
            semester_name = grade.semester.name
            
            if year_name not in transcript_data:
                transcript_data[year_name] = {}
            if semester_name not in transcript_data[year_name]:
                transcript_data[year_name][semester_name] = []
            
            transcript_data[year_name][semester_name].append(grade)
        
        # حساب المعدل التراكمي
        all_grades = Grade.query.filter_by(student_id=selected_student).all()
        if all_grades:
            total_points = sum(grade.total_grade * grade.subject.units for grade in all_grades)
            total_units = sum(grade.subject.units for grade in all_grades)
            cumulative_gpa = total_points / total_units if total_units > 0 else 0
        
        # حساب معدل السنة التحضيرية
        preparatory_grades = [g for g in all_grades if g.subject.academic_year_type == 'التحضيرية']
        if preparatory_grades:
            prep_points = sum(grade.total_grade * grade.subject.units for grade in preparatory_grades)
            prep_units = sum(grade.subject.units for grade in preparatory_grades)
            preparatory_gpa = prep_points / prep_units if prep_units > 0 else 0
    
    return render_template('reports/student_transcript.html',
                         transcript_data=transcript_data,
                         student=student if selected_student else None,
                         cumulative_gpa=cumulative_gpa,
                         preparatory_gpa=preparatory_gpa,
                         students=students,
                         selected_student=selected_student)

@bp.route('/subject_grades')
@login_required
def subject_grades():
    """تقرير درجات المادة"""
    academic_years = AcademicYear.query.all()
    semesters = Semester.query.all()
    programs = AcademicProgram.query.all()
    subjects = Subject.query.all()
    
    # فلترة البيانات
    selected_year = request.args.get('academic_year_id')
    selected_semester = request.args.get('semester_id')
    selected_program = request.args.get('program_id')
    selected_subject = request.args.get('subject_id')
    
    grades = []
    subject_info = None
    statistics = {}
    
    if selected_subject and selected_year and selected_semester:
        subject_info = Subject.query.get(selected_subject)
        
        grades_query = Grade.query.filter_by(
            subject_id=selected_subject,
            academic_year_id=selected_year,
            semester_id=selected_semester
        )
        
        if selected_program:
            grades_query = grades_query.join(Student).filter(Student.program_id == selected_program)
        
        grades = grades_query.all()
        
        # حساب الإحصائيات
        if grades:
            total_students = len(grades)
            passed_students = len([g for g in grades if g.is_passed])
            failed_students = total_students - passed_students
            
            total_grades = [g.total_grade for g in grades]
            average_grade = sum(total_grades) / len(total_grades)
            max_grade = max(total_grades)
            min_grade = min(total_grades)
            
            statistics = {
                'total_students': total_students,
                'passed_students': passed_students,
                'failed_students': failed_students,
                'pass_rate': (passed_students / total_students * 100) if total_students > 0 else 0,
                'average_grade': average_grade,
                'max_grade': max_grade,
                'min_grade': min_grade
            }
    
    return render_template('reports/subject_grades.html',
                         grades=grades,
                         subject_info=subject_info,
                         statistics=statistics,
                         academic_years=academic_years,
                         semesters=semesters,
                         programs=programs,
                         subjects=subjects,
                         selected_year=selected_year,
                         selected_semester=selected_semester,
                         selected_program=selected_program,
                         selected_subject=selected_subject)

@bp.route('/complete_transcript')
@login_required
def complete_transcript():
    """الكشف الكامل بجميع المواد والدرجات"""
    students = Student.query.all()
    selected_student = request.args.get('student_id')
    
    complete_data = {}
    final_gpa = 0
    
    if selected_student:
        student = Student.query.get(selected_student)
        
        # الحصول على جميع درجات الطالب
        all_grades = Grade.query.filter_by(student_id=selected_student).all()
        
        # تجميع البيانات
        complete_data = {
            'first_semester': [],
            'second_semester': [],
            'second_round': [],
            'seminar_grade': None,
            'thesis_grade': None
        }
        
        for grade in all_grades:
            semester_name = grade.semester.name
            if 'الأول' in semester_name:
                complete_data['first_semester'].append(grade)
            elif 'الثاني' in semester_name:
                complete_data['second_semester'].append(grade)
            elif 'الدور الثاني' in semester_name:
                complete_data['second_round'].append(grade)
            elif 'سمنار' in grade.subject.name.lower():
                complete_data['seminar_grade'] = grade
            elif 'رسالة' in grade.subject.name.lower() or 'أطروحة' in grade.subject.name.lower():
                complete_data['thesis_grade'] = grade
        
        # حساب المعدل النهائي
        if all_grades:
            total_points = sum(grade.total_grade * grade.subject.units for grade in all_grades)
            total_units = sum(grade.subject.units for grade in all_grades)
            final_gpa = total_points / total_units if total_units > 0 else 0
    
    return render_template('reports/complete_transcript.html',
                         complete_data=complete_data,
                         student=student if selected_student else None,
                         final_gpa=final_gpa,
                         students=students,
                         selected_student=selected_student)

@bp.route('/statistics')
@login_required
def statistics():
    """الإحصائيات العامة"""
    # إحصائيات عامة
    general_stats = {
        'total_students': Student.query.count(),
        'total_professors': Professor.query.count(),
        'total_subjects': Subject.query.count(),
        'total_grades': Grade.query.count()
    }
    
    # إحصائيات الطلبة حسب البرنامج
    program_stats = db.session.query(
        AcademicProgram.name,
        AcademicProgram.program_type,
        func.count(Student.id).label('count')
    ).join(Student).group_by(AcademicProgram.id).all()
    
    # إحصائيات الطلبة حسب الحالة
    status_stats = db.session.query(
        Student.status,
        func.count(Student.id).label('count')
    ).group_by(Student.status).all()
    
    # إحصائيات النجاح والرسوب
    success_stats = db.session.query(
        func.count(Grade.id).label('total'),
        func.sum(func.case([(Grade.total_grade >= 60, 1)], else_=0)).label('passed'),
        func.sum(func.case([(Grade.total_grade < 60, 1)], else_=0)).label('failed')
    ).first()
    
    return render_template('reports/statistics.html',
                         general_stats=general_stats,
                         program_stats=program_stats,
                         status_stats=status_stats,
                         success_stats=success_stats)
