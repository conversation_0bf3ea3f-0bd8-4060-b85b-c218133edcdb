#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل سريع لنظام إدارة الدراسات العليا
Quick Start for Graduate Studies Management System
"""

import os
import sys
import webbrowser
import time
import threading

def main():
    """تشغيل النظام مع فتح المتصفح تلقائياً"""

    print("\n" + "="*70)
    print("🎓 نظام إدارة الدراسات العليا - تشغيل سريع")
    print("   Graduate Studies Management System - Quick Start")
    print("="*70)

    # التحقق من وجود الملف الرئيسي
    if not os.path.exists("complete_app.py"):
        print("❌ ملف التطبيق الرئيسي غير موجود")
        print("تأكد من وجود ملف complete_app.py في نفس المجلد")
        input("اضغط Enter للخروج...")
        return

    print("📋 معلومات النظام:")
    print("   - الاسم: نظام إدارة الدراسات العليا")
    print("   - الإصدار: 1.0.0")
    print("   - التقنيات: Python, Flask, Bootstrap")
    print("   - قاعدة البيانات: SQLite")

    print("\n🔐 بيانات الدخول الافتراضية:")
    print("   - اسم المستخدم: admin")
    print("   - كلمة المرور: admin123")

    print("\n🌐 معلومات الخادم:")
    print("   - العنوان: http://localhost:5001")
    print("   - المنفذ: 5001")

    print("\n🚀 جاري تشغيل النظام...")
    print("🌐 سيتم فتح المتصفح تلقائياً خلال 3 ثوانٍ...")
    print("⚠️  للإيقاف: اضغط Ctrl+C")
    print("="*70)

    # فتح المتصفح تلقائياً بعد 3 ثوانٍ
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open('http://localhost:5001')
            print("✅ تم فتح المتصفح تلقائياً")
        except Exception as e:
            print(f"⚠️  لم يتم فتح المتصفح تلقائياً: {e}")
            print("يرجى فتح المتصفح يدوياً والانتقال إلى: http://localhost:5001")

    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

    try:
        # تشغيل التطبيق
        import complete_app
        complete_app.app.run(debug=False, host='0.0.0.0', port=5001)

    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
        print("👋 شكراً لاستخدام نظام إدارة الدراسات العليا")

    except ImportError as e:
        print(f"\n❌ خطأ في استيراد التطبيق: {e}")
        print("تأكد من تثبيت جميع المتطلبات:")
        print("pip install -r requirements.txt")
        input("اضغط Enter للخروج...")

    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
