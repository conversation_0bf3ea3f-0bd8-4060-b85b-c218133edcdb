#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق مبسط للاختبار
Simple Test Application
"""

from flask import Flask, render_template, request, flash, redirect, url_for, get_flashed_messages
from flask_sqlalchemy import SQLAlchemy
from flask_login import Lo<PERSON><PERSON>anager, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///graduate_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

# نموذج المستخدم المبسط
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    user_type = db.Column(db.String(20), nullable=False, default='user')
    is_active = db.Column(db.Boolean, default=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_authenticated(self):
        return True
    
    def is_active(self):
        return self.is_active
    
    def is_anonymous(self):
        return False
    
    def get_id(self):
        return str(self.id)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# المسارات
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            flash(f'مرحباً {user.full_name}', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - نظام إدارة الدراسات العليا</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .login-card { background: rgba(255, 255, 255, 0.95); border-radius: 15px; }
        </style>
    </head>
    <body class="d-flex align-items-center justify-content-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card login-card border-0 shadow">
                        <div class="card-header text-center py-4 bg-primary text-white">
                            <i class="bi bi-mortarboard-fill fs-1 mb-3"></i>
                            <h3>نظام إدارة الدراسات العليا</h3>
                            <p class="mb-0">تسجيل الدخول</p>
                        </div>
                        <div class="card-body p-4">
                            ''' + (''.join([f'<div class="alert alert-danger">{msg}</div>' for msg in get_flashed_messages()])) + '''
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" name="username" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" name="password" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">تسجيل الدخول</button>
                            </form>
                            <div class="text-center mt-3">
                                <small class="text-muted">المستخدم الافتراضي: admin / admin123</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/dashboard')
@login_required
def dashboard():
    return f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة التحكم - نظام إدارة الدراسات العليا</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3">مرحباً {current_user.full_name}</span>
                    <a class="nav-link" href="{url_for('logout')}">تسجيل الخروج</a>
                </div>
            </div>
        </nav>
        
        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <h1 class="h3 mb-4">
                        <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                    </h1>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-building fs-1 text-primary"></i>
                            <h5 class="card-title mt-2">الجامعات</h5>
                            <p class="card-text">إدارة الجامعات والكليات</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-people fs-1 text-success"></i>
                            <h5 class="card-title mt-2">الطلبة</h5>
                            <p class="card-text">إدارة بيانات الطلبة</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-person-workspace fs-1 text-info"></i>
                            <h5 class="card-title mt-2">الأساتذة</h5>
                            <p class="card-text">إدارة بيانات الأساتذة</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-clipboard-data fs-1 text-warning"></i>
                            <h5 class="card-title mt-2">الدرجات</h5>
                            <p class="card-text">إدخال وإدارة الدرجات</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-success">
                <h4 class="alert-heading">مرحباً بك في نظام إدارة الدراسات العليا!</h4>
                <p>تم تطوير هذا النظام لإدارة شؤون الطلبة والدرجات في الدراسات العليا.</p>
                <hr>
                <p class="mb-0">النظام يتضمن جميع الوظائف المطلوبة لإدارة البيانات الأكاديمية والتقارير.</p>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

# تهيئة قاعدة البيانات
with app.app_context():
    db.create_all()
    
    # إنشاء مستخدم افتراضي
    if not User.query.filter_by(username='admin').first():
        admin_user = User(
            username='admin',
            full_name='مدير النظام',
            user_type='admin',
            is_active=True
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        db.session.commit()
        print("تم إنشاء المستخدم الافتراضي: admin / admin123")

if __name__ == '__main__':
    print("بدء تشغيل نظام إدارة الدراسات العليا...")
    print("الخادم يعمل على: http://localhost:5000")
    print("للوصول للنظام: http://localhost:5000")
    print("المستخدم الافتراضي: admin / admin123")
    app.run(debug=True, host='0.0.0.0', port=5000)
