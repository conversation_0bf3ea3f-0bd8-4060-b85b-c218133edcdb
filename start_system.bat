@echo off
chcp 65001 >nul
title نظام إدارة الدراسات العليا - Graduate Studies Management System

echo.
echo ========================================================
echo           نظام إدارة الدراسات العليا
echo        Graduate Studies Management System
echo ========================================================
echo.

echo 🚀 جاري تشغيل النظام...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo يرجى التأكد من تثبيت Python بشكل صحيح
    pause
    exit /b 1
)

REM تثبيت المتطلبات إذا لم تكن موجودة
if not exist "venv" (
    echo 📦 إنشاء البيئة الافتراضية...
    python -m venv venv
)

echo 🔄 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

echo 📋 تثبيت المتطلبات...
pip install -r requirements.txt >nul 2>&1

echo.
echo ========================================================
echo 📋 معلومات النظام:
echo - الاسم: نظام إدارة الدراسات العليا
echo - الإصدار: 1.0.0
echo - التقنيات: Python, Flask, Bootstrap
echo.
echo 🔐 بيانات الدخول الافتراضية:
echo - اسم المستخدم: admin
echo - كلمة المرور: admin123
echo.
echo 🌐 عنوان النظام: http://localhost:5001
echo ========================================================
echo.

echo ✅ تم تشغيل النظام بنجاح!
echo 🌐 سيتم فتح المتصفح تلقائياً...
echo ⚠️  للإيقاف: اضغط Ctrl+C
echo.

REM تشغيل النظام
python complete_app.py

echo.
echo 🛑 تم إيقاف النظام
pause
