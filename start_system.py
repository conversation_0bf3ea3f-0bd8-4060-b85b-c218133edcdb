#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة الدراسات العليا
Graduate Studies Management System Launcher
"""

import os
import sys
import subprocess
import webbrowser
import time

def check_requirements():
    """التحقق من وجود المتطلبات"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_login
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ متطلب مفقود: {e}")
        print("يرجى تثبيت المتطلبات باستخدام: pip install -r requirements.txt")
        return False

def install_requirements():
    """تثبيت المتطلبات تلقائياً"""
    print("🔄 جاري تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def start_system():
    """تشغيل النظام"""
    print("🚀 بدء تشغيل نظام إدارة الدراسات العليا...")
    print("=" * 50)
    
    # التحقق من وجود الملف الرئيسي
    if not os.path.exists("complete_app.py"):
        print("❌ ملف التطبيق الرئيسي غير موجود")
        return False
    
    # التحقق من المتطلبات
    if not check_requirements():
        response = input("هل تريد تثبيت المتطلبات تلقائياً؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم']:
            if not install_requirements():
                return False
        else:
            return False
    
    print("\n📋 معلومات النظام:")
    print("- الاسم: نظام إدارة الدراسات العليا")
    print("- الإصدار: 1.0.0")
    print("- المطور: Augment Agent")
    print("- التقنيات: Python, Flask, Bootstrap")
    
    print("\n🔐 بيانات الدخول الافتراضية:")
    print("- اسم المستخدم: admin")
    print("- كلمة المرور: admin123")
    
    print("\n🌐 الخادم:")
    print("- العنوان: http://localhost:5001")
    print("- المنفذ: 5001")
    
    print("\n" + "=" * 50)
    print("🎯 جاري تشغيل النظام...")
    
    try:
        # تشغيل التطبيق
        import complete_app
        
        # فتح المتصفح تلقائياً بعد ثانيتين
        def open_browser():
            time.sleep(2)
            webbrowser.open('http://localhost:5001')
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("✅ تم تشغيل النظام بنجاح!")
        print("🌐 سيتم فتح المتصفح تلقائياً...")
        print("⚠️  للإيقاف: اضغط Ctrl+C")
        print("=" * 50)
        
        # تشغيل الخادم
        complete_app.app.run(debug=False, host='0.0.0.0', port=5001)
        
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
        return True
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎓 مرحباً بك في نظام إدارة الدراسات العليا")
    print("=" * 50)
    
    if start_system():
        print("\n✅ تم إغلاق النظام بنجاح")
    else:
        print("\n❌ حدث خطأ في تشغيل النظام")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
