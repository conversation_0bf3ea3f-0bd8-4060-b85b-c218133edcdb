/* Enhanced Styles for Graduate Management System */

/* Global Styles */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --warning-gradient: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    --info-gradient: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    --danger-gradient: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    --light-gradient: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    
    --shadow-sm: 0 2px 10px rgba(0,0,0,0.08);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 30px rgba(0,0,0,0.15);
    --shadow-xl: 0 15px 35px rgba(0,0,0,0.2);
    
    --border-radius-sm: 8px;
    --border-radius-md: 15px;
    --border-radius-lg: 20px;
    --border-radius-xl: 25px;
    
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Enhanced Body */
body {
    font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-gradient);
    min-height: 100vh;
    line-height: 1.6;
}

/* Enhanced Cards */
.enhanced-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: none;
    overflow: hidden;
    transition: var(--transition-normal);
    position: relative;
}

.enhanced-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.enhanced-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

/* Enhanced Buttons */
.btn-enhanced {
    border-radius: var(--border-radius-xl);
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: var(--transition-normal);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-fast);
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-primary-enhanced {
    background: var(--primary-gradient);
    color: white;
}

.btn-success-enhanced {
    background: var(--success-gradient);
    color: white;
}

.btn-warning-enhanced {
    background: var(--warning-gradient);
    color: white;
}

.btn-info-enhanced {
    background: var(--info-gradient);
    color: white;
}

/* Enhanced Form Controls */
.form-control-enhanced {
    border-radius: var(--border-radius-xl);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1.5rem;
    transition: var(--transition-normal);
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
}

.form-control-enhanced:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

/* Enhanced Tables */
.table-enhanced {
    background: white;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table-enhanced thead {
    background: var(--primary-gradient);
    color: white;
}

.table-enhanced tbody tr {
    transition: var(--transition-fast);
}

.table-enhanced tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* Enhanced Badges */
.badge-enhanced {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-lg);
    font-weight: 500;
    font-size: 0.85rem;
}

.badge-gradient-primary {
    background: var(--primary-gradient);
    color: white;
}

.badge-gradient-success {
    background: var(--success-gradient);
    color: white;
}

.badge-gradient-warning {
    background: var(--warning-gradient);
    color: white;
}

.badge-gradient-info {
    background: var(--info-gradient);
    color: white;
}

/* Enhanced Modals */
.modal-enhanced .modal-content {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(10px);
}

.modal-enhanced .modal-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

/* Enhanced Alerts */
.alert-enhanced {
    border-radius: var(--border-radius-md);
    border: none;
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
}

/* Enhanced Navigation */
.navbar-enhanced {
    background: rgba(255,255,255,0.95) !important;
    backdrop-filter: blur(15px);
    box-shadow: var(--shadow-md);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Enhanced Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Enhanced Tooltips */
.tooltip-enhanced {
    background: rgba(0,0,0,0.9);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-md);
}

/* Enhanced Dropdowns */
.dropdown-menu-enhanced {
    border-radius: var(--border-radius-md);
    border: none;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    background: rgba(255,255,255,0.95);
}

.dropdown-item-enhanced {
    transition: var(--transition-fast);
    border-radius: var(--border-radius-sm);
    margin: 0.2rem;
}

.dropdown-item-enhanced:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateX(5px);
}

/* Enhanced Progress Bars */
.progress-enhanced {
    border-radius: var(--border-radius-xl);
    background: rgba(0,0,0,0.1);
    overflow: hidden;
}

.progress-bar-enhanced {
    background: var(--primary-gradient);
    transition: var(--transition-normal);
}

/* Enhanced Scrollbars */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6b4c93 100%);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .enhanced-card {
        margin-bottom: 1rem;
    }
    
    .btn-enhanced {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .form-control-enhanced {
        padding: 0.5rem 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .enhanced-card {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        color: white;
    }
    
    .form-control-enhanced {
        background: rgba(255,255,255,0.1);
        color: white;
        border-color: rgba(255,255,255,0.2);
    }
    
    .table-enhanced {
        background: rgba(255,255,255,0.1);
        color: white;
    }
}
