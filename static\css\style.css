/* نمط مخصص لنظام إدارة الدراسات العليا */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

body {
    font-family: 'Tajawal', sans-serif;
    background-color: #f8f9fc;
}

/* تحسين الجداول */
.table {
    border-radius: 0.35rem;
    overflow: hidden;
}

.table thead th {
    background-color: #4e73df;
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fc;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* الأزرار */
.btn {
    border-radius: 0.35rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2e59d9;
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-success:hover {
    background-color: #17a673;
    border-color: #17a673;
}

.btn-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-info:hover {
    background-color: #2c9faf;
    border-color: #2c9faf;
}

.btn-warning {
    background-color: #f6c23e;
    border-color: #f6c23e;
    color: #3a3b45;
}

.btn-warning:hover {
    background-color: #f4b619;
    border-color: #f4b619;
    color: #3a3b45;
}

.btn-danger {
    background-color: #e74a3b;
    border-color: #e74a3b;
}

.btn-danger:hover {
    background-color: #e02d1b;
    border-color: #e02d1b;
}

/* النماذج */
.form-control {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-select {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
}

.form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* التنبيهات */
.alert {
    border-radius: 0.35rem;
    border: none;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* شريط التنقل */
.navbar-brand {
    font-weight: 700;
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.25rem;
}

/* القوائم المنسدلة */
.dropdown-menu {
    border-radius: 0.35rem;
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.dropdown-item {
    font-weight: 400;
    padding: 0.5rem 1.5rem;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
}

/* الصفحات */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0.35rem;
}

/* الإحصائيات */
.stat-card {
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}

/* الجداول المتجاوبة */
.table-responsive {
    border-radius: 0.35rem;
}

/* البحث */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* التحميل */
.loading {
    display: none;
}

.loading.show {
    display: block;
}

/* الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .table {
        border: 1px solid #dee2e6;
    }
    
    .table th,
    .table td {
        border: 1px solid #dee2e6 !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* تحسينات إضافية */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* تحسين الأيقونات */
.bi {
    vertical-align: -0.125em;
}

/* تحسين المسافات */
.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mt-4 {
    margin-top: 1.5rem !important;
}

.py-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
}

.px-4 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
}
