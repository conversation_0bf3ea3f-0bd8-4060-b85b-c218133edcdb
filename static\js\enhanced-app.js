/**
 * Enhanced JavaScript for Graduate Management System
 * Provides common functionality and interactions across all pages
 */

class EnhancedApp {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeAnimations();
        this.initializeTooltips();
        this.initializeModals();
        this.setupThemeToggle();
        this.initializeNotifications();
    }

    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.handlePageLoad();
        });

        // Enhanced form validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                this.handleFormSubmit(e);
            });
        });

        // Enhanced button interactions
        document.querySelectorAll('.btn-enhanced').forEach(btn => {
            this.enhanceButton(btn);
        });

        // Enhanced table interactions
        document.querySelectorAll('.table-enhanced tbody tr').forEach(row => {
            this.enhanceTableRow(row);
        });
    }

    handlePageLoad() {
        // Animate elements on page load
        this.animateOnLoad();
        
        // Show welcome message
        setTimeout(() => {
            this.showWelcomeToast();
        }, 1000);

        // Initialize lazy loading
        this.initializeLazyLoading();
    }

    animateOnLoad() {
        const elements = document.querySelectorAll('.animate-on-load');
        elements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.1}s`;
            element.classList.add('animate-fade-in-up');
        });
    }

    initializeAnimations() {
        // Intersection Observer for scroll animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    }

    initializeTooltips() {
        // Enhanced tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                customClass: 'tooltip-enhanced'
            });
        });
    }

    initializeModals() {
        // Enhanced modal behavior
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.add('modal-enhanced');
            
            modal.addEventListener('show.bs.modal', () => {
                document.body.style.overflow = 'hidden';
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.style.overflow = 'auto';
            });
        });
    }

    enhanceButton(button) {
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0)';
        });

        button.addEventListener('click', (e) => {
            this.createRippleEffect(e, button);
        });
    }

    enhanceTableRow(row) {
        row.addEventListener('mouseenter', () => {
            row.style.transform = 'scale(1.01)';
            row.style.zIndex = '10';
        });

        row.addEventListener('mouseleave', () => {
            row.style.transform = 'scale(1)';
            row.style.zIndex = 'auto';
        });
    }

    createRippleEffect(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Apply saved theme
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            document.body.setAttribute('data-theme', savedTheme);
        }
    }

    toggleTheme() {
        const currentTheme = document.body.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.body.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        this.showToast(`تم التبديل إلى الوضع ${newTheme === 'dark' ? 'الليلي' : 'النهاري'}`, 'info');
    }

    initializeNotifications() {
        // Check for notifications permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }

    showWelcomeToast() {
        this.showToast('مرحباً بك في نظام إدارة الدراسات العليا', 'success');
    }

    showToast(message, type = 'info', duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `toast position-fixed top-0 end-0 m-3`;
        toast.style.zIndex = '9999';
        
        const iconMap = {
            success: 'check-circle',
            error: 'x-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };

        const colorMap = {
            success: 'success',
            error: 'danger',
            warning: 'warning',
            info: 'primary'
        };

        toast.innerHTML = `
            <div class="toast-header">
                <i class="bi bi-${iconMap[type]} text-${colorMap[type]} me-2"></i>
                <strong class="me-auto">${type === 'success' ? 'نجح' : type === 'error' ? 'خطأ' : type === 'warning' ? 'تحذير' : 'معلومات'}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;

        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast, { delay: duration });
        bsToast.show();

        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    showNotification(title, message, type = 'info') {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                body: message,
                icon: '/static/images/logo.png',
                badge: '/static/images/badge.png'
            });
        } else {
            this.showToast(`${title}: ${message}`, type);
        }
    }

    initializeLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('loading-shimmer');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => {
            img.classList.add('loading-shimmer');
            imageObserver.observe(img);
        });
    }

    handleFormSubmit(event) {
        const form = event.target;
        const submitBtn = form.querySelector('button[type="submit"]');
        
        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise spinner-border spinner-border-sm me-2"></i>جاري المعالجة...';
            submitBtn.disabled = true;

            // Re-enable after 3 seconds (in case of no response)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        }
    }

    // Utility functions
    formatNumber(num) {
        return new Intl.NumberFormat('ar-SA').format(num);
    }

    formatDate(date) {
        return new Intl.DateTimeFormat('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    }

    formatDateTime(date) {
        return new Intl.DateTimeFormat('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }

    animateNumber(element, start, end, duration = 1000) {
        const startTime = performance.now();
        
        function update(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(start + (end - start) * progress);
            element.textContent = this.formatNumber(current);
            
            if (progress < 1) {
                requestAnimationFrame(update);
            }
        }
        
        requestAnimationFrame(update.bind(this));
    }

    // Export functions for global use
    static getInstance() {
        if (!EnhancedApp.instance) {
            EnhancedApp.instance = new EnhancedApp();
        }
        return EnhancedApp.instance;
    }
}

// Initialize the enhanced app
const enhancedApp = EnhancedApp.getInstance();

// Global utility functions
window.showToast = (message, type, duration) => enhancedApp.showToast(message, type, duration);
window.showNotification = (title, message, type) => enhancedApp.showNotification(title, message, type);
window.formatNumber = (num) => enhancedApp.formatNumber(num);
window.formatDate = (date) => enhancedApp.formatDate(date);
window.formatDateTime = (date) => enhancedApp.formatDateTime(date);
window.animateNumber = (element, start, end, duration) => enhancedApp.animateNumber(element, start, end, duration);

// Add ripple effect CSS
const rippleCSS = `
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);
