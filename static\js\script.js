// JavaScript مخصص لنظام إدارة الدراسات العليا

document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تفعيل popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // تأكيد الحذف
    var deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من الحذف؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                e.preventDefault();
            }
        });
    });

    // البحث المباشر في الجداول
    var searchInputs = document.querySelectorAll('.table-search');
    searchInputs.forEach(function(input) {
        input.addEventListener('keyup', function() {
            var filter = this.value.toLowerCase();
            var table = this.closest('.card').querySelector('table tbody');
            var rows = table.querySelectorAll('tr');
            
            rows.forEach(function(row) {
                var text = row.textContent.toLowerCase();
                if (text.includes(filter)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });

    // تحديد الكل في checkboxes
    var selectAllCheckboxes = document.querySelectorAll('.select-all');
    selectAllCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            var targetCheckboxes = document.querySelectorAll(this.dataset.target);
            targetCheckboxes.forEach(function(target) {
                target.checked = checkbox.checked;
            });
        });
    });

    // تحديث العداد عند تحديد العناصر
    var itemCheckboxes = document.querySelectorAll('.item-checkbox');
    itemCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    function updateSelectedCount() {
        var checked = document.querySelectorAll('.item-checkbox:checked').length;
        var counter = document.querySelector('.selected-count');
        if (counter) {
            counter.textContent = checked;
            counter.style.display = checked > 0 ? 'inline' : 'none';
        }
    }

    // تحميل البيانات بـ AJAX
    function loadData(url, targetElement) {
        fetch(url)
            .then(response => response.text())
            .then(data => {
                document.querySelector(targetElement).innerHTML = data;
            })
            .catch(error => {
                console.error('خطأ في تحميل البيانات:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'danger');
            });
    }

    // عرض التنبيهات
    function showAlert(message, type = 'info') {
        var alertContainer = document.querySelector('.alert-container') || document.body;
        var alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.insertBefore(alert, alertContainer.firstChild);
        
        // إخفاء التنبيه بعد 5 ثوان
        setTimeout(function() {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    }

    // تحديث الصفحة بدون إعادة تحميل كامل
    function updatePage(url, data = {}) {
        var formData = new FormData();
        for (var key in data) {
            formData.append(key, data[key]);
        }

        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                if (data.redirect) {
                    window.location.href = data.redirect;
                }
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('حدث خطأ غير متوقع', 'danger');
        });
    }

    // تحسين النماذج
    var forms = document.querySelectorAll('form.ajax-form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            var formData = new FormData(this);
            var submitButton = this.querySelector('button[type="submit"]');
            var originalText = submitButton.textContent;
            
            // تعطيل الزر وإظهار التحميل
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري المعالجة...';
            
            fetch(this.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else if (data.reset) {
                        form.reset();
                    }
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showAlert('حدث خطأ في إرسال البيانات', 'danger');
            })
            .finally(() => {
                // إعادة تفعيل الزر
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            });
        });
    });

    // تحسين رفع الملفات
    var fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            var file = this.files[0];
            if (file) {
                // التحقق من نوع الملف
                var allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    showAlert('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)', 'warning');
                    this.value = '';
                    return;
                }
                
                // التحقق من حجم الملف (16MB)
                if (file.size > 16 * 1024 * 1024) {
                    showAlert('حجم الملف كبير جداً. الحد الأقصى 16 ميجابايت', 'warning');
                    this.value = '';
                    return;
                }
                
                // معاينة الصورة
                var preview = this.parentElement.querySelector('.image-preview');
                if (preview) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                }
            }
        });
    });

    // تحسين الطباعة
    var printButtons = document.querySelectorAll('.btn-print');
    printButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            window.print();
        });
    });

    // تحسين التصدير
    var exportButtons = document.querySelectorAll('.btn-export');
    exportButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            var format = this.dataset.format || 'excel';
            var url = this.dataset.url;
            
            if (url) {
                window.open(url + '?format=' + format, '_blank');
            }
        });
    });

    // تحديث الوقت المباشر
    function updateTime() {
        var timeElements = document.querySelectorAll('.live-time');
        timeElements.forEach(function(element) {
            var now = new Date();
            element.textContent = now.toLocaleString('ar-IQ');
        });
    }
    
    // تحديث الوقت كل ثانية
    setInterval(updateTime, 1000);
    updateTime(); // تحديث فوري

    // تحسين التنقل بالكيبورد
    document.addEventListener('keydown', function(e) {
        // Ctrl+S للحفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            var saveButton = document.querySelector('.btn-save, button[type="submit"]');
            if (saveButton) {
                saveButton.click();
            }
        }
        
        // Escape لإغلاق النوافذ المنبثقة
        if (e.key === 'Escape') {
            var modals = document.querySelectorAll('.modal.show');
            modals.forEach(function(modal) {
                var bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            });
        }
    });
});
