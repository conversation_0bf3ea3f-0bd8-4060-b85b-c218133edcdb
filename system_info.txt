نظام إدارة الدراسات العليا
Graduate Studies Management System
=====================================

📋 معلومات النظام:
- الاسم: نظام إدارة الدراسات العليا
- الإصدار: 1.0.0
- تاريخ التطوير: 2024
- المطور: Augment Agent
- اللغة: Python
- إطار العمل: Flask
- قاعدة البيانات: SQLite
- واجهة المستخدم: Bootstrap 5.3

🔐 بيانات الدخول الافتراضية:
- اسم المستخدم: admin
- كلمة المرور: admin123
- نوع المستخدم: مدير النظام

🌐 معلومات الخادم:
- العنوان المحلي: http://localhost:5001
- المنفذ: 5001
- وضع التطوير: مفعل

📁 ملفات النظام الرئيسية:
- complete_app.py: التطبيق الرئيسي الكامل
- start_system.py: ملف تشغيل النظام
- start_system.bat: ملف تشغيل Windows
- requirements.txt: متطلبات Python
- README.md: دليل المستخدم

📊 قاعدة البيانات:
- النوع: SQLite
- الملف: graduate_system.db
- الجداول: 12 جدول رئيسي
- العلاقات: مترابطة بالكامل

🎯 الوظائف الرئيسية:
✅ إدارة الجامعات والكليات والأقسام
✅ إدارة البرامج الأكاديمية والمواد الدراسية
✅ إدارة الأساتذة والطلبة
✅ إدخال وإدارة الدرجات والنتائج
✅ حساب المعدلات والتقديرات
✅ إنتاج التقارير والكشوف
✅ نظام مستخدمين متعدد المستويات
✅ واجهة متجاوبة لجميع الأجهزة

🔧 متطلبات التشغيل:
- Python 3.8 أو أحدث
- Flask 2.3.3
- Flask-SQLAlchemy 3.0.5
- Flask-Login 0.6.3
- Bootstrap 5.3.0
- متصفح ويب حديث

📱 التوافق:
✅ Windows 10/11
✅ macOS
✅ Linux
✅ جميع المتصفحات الحديثة
✅ الأجهزة المحمولة والأجهزة اللوحية

🛡️ الأمان:
✅ تشفير كلمات المرور
✅ جلسات آمنة
✅ صلاحيات متدرجة
✅ التحقق من صحة البيانات
✅ حماية من الهجمات الشائعة

📞 الدعم الفني:
- راجع ملف README.md للتعليمات التفصيلية
- تحقق من ملفات السجل عند حدوث مشاكل
- تأكد من تثبيت جميع المتطلبات بشكل صحيح

🎓 الاستخدام التعليمي:
هذا النظام مطور خصيصاً للمؤسسات التعليمية
ويمكن تخصيصه ليناسب احتياجات أي جامعة أو كلية

=====================================
© 2024 نظام إدارة الدراسات العليا
جميع الحقوق محفوظة
