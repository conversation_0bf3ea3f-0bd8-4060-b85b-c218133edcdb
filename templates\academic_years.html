<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأعوام الدراسية - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding-top: 80px;
        }
        
        .main-content {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem auto;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .page-header {
            background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.7rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(13, 110, 253, 0.4);
        }
        
        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .table thead th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            font-weight: 600;
            color: #495057;
            padding: 1rem;
        }
        
        .table tbody td {
            padding: 1rem;
            vertical-align: middle;
            border-color: #f8f9fa;
        }
        
        .badge {
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .btn-sm {
            padding: 0.4rem 0.8rem;
            border-radius: 8px;
            margin: 0 0.2rem;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-content">
            <div class="page-header">
                <h1 class="mb-0">
                    <i class="bi bi-calendar-range me-3"></i>
                    إدارة الأعوام الدراسية
                </h1>
                <p class="mb-0 mt-2">إدارة وتنظيم الأعوام الدراسية في النظام</p>
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="text-primary">
                    <i class="bi bi-list-ul me-2"></i>
                    قائمة الأعوام الدراسية
                </h3>
                <a href="{{ url_for('add_academic_year') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة عام دراسي جديد
                </a>
            </div>

            {% if years %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العام الدراسي</th>
                                <th>الحالة</th>
                                <th>عدد الطلبة</th>
                                <th>عدد الدرجات</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for year in years %}
                                <tr>
                                    <td>
                                        <strong>{{ year.name }}</strong>
                                        {% if year.is_current %}
                                            <span class="badge bg-success ms-2">العام الحالي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if year.is_current %}
                                            <span class="badge bg-success">العام الحالي</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ year.students|length }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ year.grades|length }}</span>
                                    </td>
                                    <td>{{ year.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_academic_year', year_id=year.id) }}"
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-pencil"></i>
                                                تعديل
                                            </a>
                                            {% if year.students|length == 0 and year.grades|length == 0 %}
                                                <form method="POST" action="{{ url_for('delete_academic_year', year_id=year.id) }}" style="display: inline;">
                                                    <button type="submit" class="btn btn-outline-danger btn-sm"
                                                            onclick="return confirm('هل أنت متأكد من حذف هذا العام الدراسي؟')">
                                                        <i class="bi bi-trash"></i>
                                                        حذف
                                                    </button>
                                                </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-calendar-x display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">لا توجد أعوام دراسية</h4>
                    <p class="text-muted">ابدأ بإضافة عام دراسي جديد</p>
                    <a href="{{ url_for('add_academic_year') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة عام دراسي
                    </a>
                </div>
            {% endif %}

            <div class="mt-4 text-center">
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-right me-2"></i>
                    العودة للوحة التحكم
                </a>
                <a href="{{ url_for('semesters') }}" class="btn btn-outline-info ms-2">
                    <i class="bi bi-calendar3 me-2"></i>
                    إدارة الفصول الدراسية
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إخفاء الرسائل تلقائياً بعد 5 ثوانٍ
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
