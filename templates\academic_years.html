<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأعوام الدراسية - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap');

        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: 'Cairo', 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
            color: #1e293b;
            overflow-x: hidden;
        }

        /* خلفية ديناميكية للأعوام الدراسية */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 30%, #8b5cf6 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, #06b6d4 0%, transparent 50%),
                radial-gradient(circle at 30% 80%, #f59e0b 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, #ef4444 0%, transparent 50%),
                linear-gradient(135deg, #1e1b4b 0%, #0f172a 100%);
            z-index: -2;
            animation: backgroundShift 30s ease-in-out infinite;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse"><path d="M 8 0 L 0 0 0 8" fill="none" stroke="%23ffffff" stroke-width="0.2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
            opacity: 0.3;
        }

        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            25% { filter: hue-rotate(90deg) brightness(1.1); }
            50% { filter: hue-rotate(180deg) brightness(0.9); }
            75% { filter: hue-rotate(270deg) brightness(1.1); }
        }

        .main-content {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.95) 0%,
                rgba(30, 27, 75, 0.95) 100%);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 32px;
            padding: 3rem;
            margin: 2rem auto;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(24px) saturate(180%);
        }

        .page-header {
            background: linear-gradient(135deg,
                rgba(139, 92, 246, 0.9) 0%,
                rgba(6, 182, 212, 0.9) 50%,
                rgba(245, 158, 11, 0.9) 100%);
            color: white;
            border-radius: 24px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow:
                0 20px 40px rgba(139, 92, 246, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.15),
                transparent);
            animation: shine 4s ease-in-out infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }

        /* بطاقات الأعوام الدراسية */
        .years-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .year-card {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.8) 0%,
                rgba(30, 27, 75, 0.8) 100%);
            border: 1px solid rgba(139, 92, 246, 0.2);
            border-radius: 24px;
            padding: 2rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .year-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #8b5cf6, #06b6d4, #f59e0b, #ef4444);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .year-card:hover::before {
            transform: scaleX(1);
        }

        .year-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(139, 92, 246, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            border-color: rgba(139, 92, 246, 0.4);
        }

        .year-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
        }

        .status-badge {
            background: linear-gradient(135deg, #10b981, #06b6d4);
            color: white;
            border-radius: 12px;
            padding: 0.4rem 0.8rem;
            font-weight: 600;
            font-size: 0.85rem;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .action-btn {
            background: linear-gradient(135deg,
                rgba(139, 92, 246, 0.1) 0%,
                rgba(6, 182, 212, 0.1) 100%);
            border: 1px solid rgba(139, 92, 246, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.6rem 1.2rem;
            transition: all 0.3s ease;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
        }

        .action-btn:hover {
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(139, 92, 246, 0.4);
        }

        .stats-container {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.6) 0%,
                rgba(30, 27, 75, 0.6) 100%);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(139, 92, 246, 0.2);
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Cairo', sans-serif;
        }

        .stat-label {
            color: #ffffff;
            font-weight: 600;
            margin-top: 0.5rem;
            font-family: 'Cairo', sans-serif;
        }
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .table thead th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            font-weight: 600;
            color: #495057;
            padding: 1rem;
        }
        
        .table tbody td {
            padding: 1rem;
            vertical-align: middle;
            border-color: #f8f9fa;
        }
        
        .badge {
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .btn-sm {
            padding: 0.4rem 0.8rem;
            border-radius: 8px;
            margin: 0 0.2rem;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-content">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="page-header">
                <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 800; font-family: 'Cairo', sans-serif;">
                    <i class="bi bi-calendar-range me-3"></i>
                    الأعوام الدراسية
                </h1>
                <p class="mb-0 mt-3" style="font-size: 1.2rem; opacity: 0.9; font-family: 'Cairo', sans-serif;">
                    إدارة وتنظيم الأعوام الدراسية والفترات الزمنية
                </p>
            </div>

            <!-- إحصائيات الأعوام -->
            <div class="stats-container">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">{{ academic_years|length }}</div>
                            <div class="stat-label">عام دراسي</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">
                                {% set active_years = academic_years|selectattr('is_active', 'equalto', true)|list|length %}
                                {{ active_years }}
                            </div>
                            <div class="stat-label">عام نشط</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">
                                {% set current_year = academic_years|selectattr('is_current', 'equalto', true)|list|length %}
                                {{ current_year }}
                            </div>
                            <div class="stat-label">العام الحالي</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">
                                {% set total_semesters = 0 %}
                                {% for year in academic_years %}
                                    {% set total_semesters = total_semesters + (year.semesters|length if year.semesters else 0) %}
                                {% endfor %}
                                {{ total_semesters }}
                            </div>
                            <div class="stat-label">فصل دراسي</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: #ffffff; font-weight: 700; font-family: 'Cairo', sans-serif;">
                        <i class="bi bi-calendar3 me-2" style="color: #8b5cf6;"></i>
                        قائمة الأعوام الدراسية
                    </h2>
                    <p class="text-muted mb-0">جميع الأعوام الدراسية المسجلة في النظام</p>
                </div>
                <div class="d-flex gap-3">
                    <a href="{{ url_for('add_academic_year') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة عام دراسي جديد
                    </a>
                </div>
            </div>

            {% if academic_years %}
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="text-primary">
                    <i class="bi bi-list-ul me-2"></i>
                    قائمة الأعوام الدراسية
                </h3>
                <a href="{{ url_for('add_academic_year') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة عام دراسي جديد
                </a>
            </div>

                <div class="years-grid">
                    {% for year in academic_years %}
                        <div class="year-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="year-icon">
                                        <i class="bi bi-calendar-range"></i>
                                    </div>
                                    <div class="ms-3">
                                        <h5 class="mb-1 fw-bold text-white" style="font-family: 'Cairo', sans-serif;">{{ year.name }}</h5>
                                        <small class="text-muted">{{ year.description or 'لا يوجد وصف' }}</small>
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <button class="action-btn btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ url_for('edit_academic_year', id=year.id) }}">
                                            <i class="bi bi-pencil me-2"></i>تعديل
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ url_for('semesters') }}?year={{ year.id }}">
                                            <i class="bi bi-calendar3 me-2"></i>الفصول الدراسية
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeleteYear('{{ year.name }}', {{ year.id }})">
                                            <i class="bi bi-trash me-2"></i>حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted">تاريخ البداية:</small>
                                    <div class="text-white fw-bold" style="font-family: 'Cairo', sans-serif;">
                                        {{ year.start_date.strftime('%Y-%m-%d') if year.start_date else 'غير محدد' }}
                                    </div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">تاريخ النهاية:</small>
                                    <div class="text-white fw-bold" style="font-family: 'Cairo', sans-serif;">
                                        {{ year.end_date.strftime('%Y-%m-%d') if year.end_date else 'غير محدد' }}
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">الحالة:</small>
                                <div class="mt-1">
                                    {% if year.is_current %}
                                        <span class="status-badge" style="background: linear-gradient(135deg, #10b981, #059669);">العام الحالي</span>
                                    {% elif year.is_active %}
                                        <span class="status-badge" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">نشط</span>
                                    {% else %}
                                        <span class="status-badge" style="background: linear-gradient(135deg, #6b7280, #4b5563);">غير نشط</span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex gap-2">
                                    {% set semesters_count = year.semesters|length if year.semesters else 0 %}
                                    {% if semesters_count > 0 %}
                                        <span class="badge bg-info rounded-pill">{{ semesters_count }} فصل</span>
                                    {% else %}
                                        <span class="badge bg-secondary rounded-pill">لا يوجد فصول</span>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    <i class="bi bi-calendar3 me-1"></i>
                                    {{ year.created_at.strftime('%Y-%m-%d') if year.created_at else 'غير محدد' }}
                                </small>
                            </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <div class="year-icon mx-auto mb-4" style="width: 100px; height: 100px; font-size: 3rem; opacity: 0.5;">
                        <i class="bi bi-calendar-range"></i>
                    </div>
                    <h3 class="text-white mb-3" style="font-family: 'Cairo', sans-serif;">لا توجد أعوام دراسية مسجلة</h3>
                    <p class="text-muted mb-4">لم يتم تسجيل أي أعوام دراسية في النظام بعد</p>
                    <a href="{{ url_for('add_academic_year') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة عام دراسي جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        // تأكيد حذف العام الدراسي
        function confirmDeleteYear(yearName, yearId) {
            if (confirm(`هل أنت متأكد من حذف العام الدراسي "${yearName}"؟\nسيتم حذف جميع البيانات المرتبطة به.`)) {
                fetch(`/admin/academic_years/delete/${yearId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }

        // تحسين الرسوم المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.year-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        });

        // رسوم متحركة
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-load {
                animation: slideInUp 0.6s ease-out;
                animation-fill-mode: both;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-calendar-x display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">لا توجد أعوام دراسية</h4>
                    <p class="text-muted">ابدأ بإضافة عام دراسي جديد</p>
                    <a href="{{ url_for('add_academic_year') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة عام دراسي
                    </a>
                </div>
            {% endif %}

            <div class="mt-4 text-center">
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-right me-2"></i>
                    العودة للوحة التحكم
                </a>
                <a href="{{ url_for('semesters') }}" class="btn btn-outline-info ms-2">
                    <i class="bi bi-calendar3 me-2"></i>
                    إدارة الفصول الدراسية
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إخفاء الرسائل تلقائياً بعد 5 ثوانٍ
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
