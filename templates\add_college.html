<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة كلية جديدة - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('colleges') }}">الكليات</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-plus-circle me-2"></i>إضافة كلية جديدة
                    </h1>
                    <a href="{{ url_for('colleges') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-bank me-2"></i>بيانات الكلية
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name_ar" class="form-label">
                                        <i class="bi bi-bank me-2"></i>اسم الكلية بالعربية <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name_ar" name="name_ar" required 
                                           placeholder="أدخل اسم الكلية بالعربية">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="name_en" class="form-label">
                                        <i class="bi bi-bank me-2"></i>اسم الكلية بالإنجليزية
                                    </label>
                                    <input type="text" class="form-control" id="name_en" name="name_en"
                                           placeholder="College Name in English">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="university_id" class="form-label">
                                        <i class="bi bi-building me-2"></i>الجامعة <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="university_id" name="university_id" required>
                                        <option value="">اختر الجامعة</option>
                                        {% for university in universities %}
                                        <option value="{{ university.id }}">{{ university.name_ar }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="address" class="form-label">
                                        <i class="bi bi-geo-alt me-2"></i>العنوان
                                    </label>
                                    <input type="text" class="form-control" id="address" name="address"
                                           placeholder="عنوان الكلية">
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('colleges') }}" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>حفظ الكلية
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const nameAr = document.getElementById('name_ar').value.trim();
            const universityId = document.getElementById('university_id').value;
            
            if (!nameAr) {
                e.preventDefault();
                alert('يرجى إدخال اسم الكلية بالعربية');
                document.getElementById('name_ar').focus();
                return false;
            }
            
            if (!universityId) {
                e.preventDefault();
                alert('يرجى اختيار الجامعة');
                document.getElementById('university_id').focus();
                return false;
            }
        });
    </script>
</body>
</html>
