<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة قسم جديد - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('departments') }}">الأقسام</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-plus-circle me-2"></i>إضافة قسم جديد
                    </h1>
                    <a href="{{ url_for('departments') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-diagram-3 me-2"></i>بيانات القسم
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <!-- البيانات الأساسية -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="bi bi-info-circle me-2"></i>البيانات الأساسية
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        <i class="bi bi-diagram-3 me-2"></i>اسم القسم <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           placeholder="أدخل اسم القسم">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="college_id" class="form-label">
                                        <i class="bi bi-bank me-2"></i>الكلية <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="college_id" name="college_id" required>
                                        <option value="">اختر الكلية</option>
                                        {% for college in colleges %}
                                        <option value="{{ college.id }}">
                                            {{ college.name_ar }} - {{ college.university.name_ar if college.university else '' }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <!-- المسؤولون -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-people me-2"></i>المسؤولون في القسم
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="head_name" class="form-label">
                                        <i class="bi bi-person-badge me-2"></i>رئيس القسم
                                    </label>
                                    <input type="text" class="form-control" id="head_name" name="head_name"
                                           placeholder="اسم رئيس القسم">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="secretary_name" class="form-label">
                                        <i class="bi bi-person-check me-2"></i>سكرتير القسم
                                    </label>
                                    <input type="text" class="form-control" id="secretary_name" name="secretary_name"
                                           placeholder="اسم سكرتير القسم">
                                </div>
                            </div>
                            
                            <!-- الوصف -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-chat-text me-2"></i>وصف القسم
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="description" class="form-label">
                                        <i class="bi bi-file-text me-2"></i>وصف القسم
                                    </label>
                                    <textarea class="form-control" id="description" name="description" rows="4"
                                              placeholder="وصف مختصر عن القسم وأنشطته..."></textarea>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('departments') }}" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>حفظ القسم
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const collegeId = document.getElementById('college_id').value;
            
            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم القسم');
                document.getElementById('name').focus();
                return false;
            }
            
            if (!collegeId) {
                e.preventDefault();
                alert('يرجى اختيار الكلية');
                document.getElementById('college_id').focus();
                return false;
            }
        });
    </script>
</body>
</html>
