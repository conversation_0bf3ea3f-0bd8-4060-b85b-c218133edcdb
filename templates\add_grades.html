<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدخال الدرجات - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('grades') }}">الدرجات</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-clipboard-data me-2"></i>إدخال الدرجات
                    </h1>
                    <a href="{{ url_for('grades') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- معايير الاختيار -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-funnel me-2"></i>معايير الاختيار
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="gradesForm">
                            <div class="row g-3 mb-4">
                                <div class="col-md-3">
                                    <label for="academic_year_id" class="form-label">العام الدراسي <span class="text-danger">*</span></label>
                                    <select class="form-select" id="academic_year_id" name="academic_year_id" required>
                                        <option value="">اختر العام الدراسي</option>
                                        {% for year in academic_years %}
                                        <option value="{{ year.id }}">{{ year.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="semester_id" class="form-label">الفصل الدراسي <span class="text-danger">*</span></label>
                                    <select class="form-select" id="semester_id" name="semester_id" required>
                                        <option value="">اختر الفصل الدراسي</option>
                                        {% for semester in semesters %}
                                        <option value="{{ semester.id }}">{{ semester.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="program_id" class="form-label">البرنامج الأكاديمي <span class="text-danger">*</span></label>
                                    <select class="form-select" id="program_id" name="program_id" required>
                                        <option value="">اختر البرنامج الأكاديمي</option>
                                        {% for program in programs %}
                                        <option value="{{ program.id }}">{{ program.name }} ({{ program.program_type }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="subject_id" class="form-label">المادة الدراسية <span class="text-danger">*</span></label>
                                    <select class="form-select" id="subject_id" name="subject_id" required>
                                        <option value="">اختر المادة الدراسية</option>
                                        {% for subject in subjects %}
                                        <option value="{{ subject.id }}" data-program="{{ subject.program_id }}">
                                            {{ subject.name }} ({{ subject.units }} وحدة)
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <label for="professor_id" class="form-label">أستاذ المادة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="professor_id" name="professor_id" required>
                                        <option value="">اختر أستاذ المادة</option>
                                        {% for professor in professors %}
                                        <option value="{{ professor.id }}">
                                            {{ professor.academic_title or '' }} {{ professor.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-md-6 d-flex align-items-end">
                                    <button type="button" class="btn btn-info w-100" id="loadStudents">
                                        <i class="bi bi-search me-2"></i>تحميل قائمة الطلبة
                                    </button>
                                </div>
                            </div>
                            
                            <!-- قائمة الطلبة والدرجات -->
                            <div id="studentsGrades" style="display: none;">
                                <div class="card">
                                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                        <h6 class="m-0 font-weight-bold text-success">
                                            <i class="bi bi-people me-2"></i>قائمة الطلبة والدرجات
                                        </h6>
                                        <div>
                                            <span class="badge bg-info" id="studentsCount">0 طالب</span>
                                            <span class="badge bg-success ms-2" id="passRate">نسبة النجاح: 0%</span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <strong>ملاحظة:</strong> درجة السعي من 40 درجة، ودرجة الامتحان النهائي من 60 درجة، والمجموع الكلي من 100 درجة.
                                        </div>
                                        
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover">
                                                <thead class="table-primary">
                                                    <tr>
                                                        <th width="5%">#</th>
                                                        <th width="30%">اسم الطالب</th>
                                                        <th width="15%">درجة السعي<br><small class="text-muted">(من 40)</small></th>
                                                        <th width="15%">درجة الامتحان النهائي<br><small class="text-muted">(من 60)</small></th>
                                                        <th width="15%">الدرجة الكاملة<br><small class="text-muted">(من 100)</small></th>
                                                        <th width="20%">التقدير</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="studentsTableBody">
                                                    <!-- سيتم ملؤها بـ JavaScript -->
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                            <button type="button" class="btn btn-secondary me-md-2" onclick="clearGrades()">
                                                <i class="bi bi-arrow-clockwise me-2"></i>مسح الدرجات
                                            </button>
                                            <button type="submit" class="btn btn-success">
                                                <i class="bi bi-check-circle me-2"></i>حفظ الدرجات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const programSelect = document.getElementById('program_id');
            const subjectSelect = document.getElementById('subject_id');
            const loadStudentsBtn = document.getElementById('loadStudents');
            const studentsGrades = document.getElementById('studentsGrades');
            const studentsTableBody = document.getElementById('studentsTableBody');
            const studentsCount = document.getElementById('studentsCount');
            const passRate = document.getElementById('passRate');
            
            // فلترة المواد حسب البرنامج المختار
            programSelect.addEventListener('change', function() {
                const selectedProgram = this.value;
                const subjectOptions = subjectSelect.querySelectorAll('option');
                
                subjectOptions.forEach(option => {
                    if (option.value === '') {
                        option.style.display = 'block';
                        return;
                    }
                    
                    const subjectProgram = option.dataset.program;
                    if (selectedProgram === '' || subjectProgram === selectedProgram) {
                        option.style.display = 'block';
                    } else {
                        option.style.display = 'none';
                    }
                });
                
                subjectSelect.value = '';
            });
            
            // تحميل قائمة الطلبة (محاكاة)
            loadStudentsBtn.addEventListener('click', function() {
                const programId = programSelect.value;
                
                if (!programId) {
                    alert('يرجى اختيار البرنامج الأكاديمي أولاً');
                    return;
                }
                
                // محاكاة تحميل البيانات
                const students = [
                    {id: 1, name: 'أحمد محمد علي'},
                    {id: 2, name: 'فاطمة حسن محمود'},
                    {id: 3, name: 'محمد عبدالله أحمد'},
                    {id: 4, name: 'زينب علي حسن'},
                    {id: 5, name: 'عمر خالد محمد'}
                ];
                
                studentsTableBody.innerHTML = '';
                
                students.forEach((student, index) => {
                    const row = `
                        <tr>
                            <td>${index + 1}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title bg-primary text-white rounded-circle">
                                            ${student.name[0]}
                                        </div>
                                    </div>
                                    <strong>${student.name}</strong>
                                </div>
                            </td>
                            <td>
                                <input type="number" class="form-control midterm-grade" 
                                       name="midterm_${student.id}" 
                                       min="0" max="40" step="0.5" 
                                       placeholder="0.0">
                            </td>
                            <td>
                                <input type="number" class="form-control final-grade" 
                                       name="final_${student.id}" 
                                       min="0" max="60" step="0.5" 
                                       placeholder="0.0">
                            </td>
                            <td>
                                <input type="number" class="form-control total-grade" 
                                       readonly 
                                       placeholder="0.0">
                            </td>
                            <td>
                                <span class="badge grade-badge bg-secondary">-</span>
                            </td>
                        </tr>
                    `;
                    studentsTableBody.innerHTML += row;
                });
                
                studentsCount.textContent = `${students.length} طالب`;
                studentsGrades.style.display = 'block';
                
                // ربط أحداث حساب الدرجات
                bindGradeCalculation();
            });
            
            // ربط أحداث حساب الدرجات
            function bindGradeCalculation() {
                const midtermInputs = document.querySelectorAll('.midterm-grade');
                const finalInputs = document.querySelectorAll('.final-grade');
                
                function calculateGrade(row) {
                    const midterm = parseFloat(row.querySelector('.midterm-grade').value) || 0;
                    const final = parseFloat(row.querySelector('.final-grade').value) || 0;
                    const total = midterm + final;
                    
                    row.querySelector('.total-grade').value = total.toFixed(1);
                    
                    const badge = row.querySelector('.grade-badge');
                    let gradeText = '';
                    let badgeClass = '';
                    
                    if (total >= 90) {
                        gradeText = 'امتياز';
                        badgeClass = 'bg-success';
                    } else if (total >= 80) {
                        gradeText = 'جيد جداً';
                        badgeClass = 'bg-info';
                    } else if (total >= 70) {
                        gradeText = 'جيد';
                        badgeClass = 'bg-primary';
                    } else if (total >= 60) {
                        gradeText = 'مقبول';
                        badgeClass = 'bg-warning';
                    } else {
                        gradeText = 'ضعيف';
                        badgeClass = 'bg-danger';
                    }
                    
                    badge.textContent = gradeText;
                    badge.className = `badge grade-badge ${badgeClass}`;
                    
                    updatePassRate();
                }
                
                [...midtermInputs, ...finalInputs].forEach(input => {
                    input.addEventListener('input', function() {
                        calculateGrade(this.closest('tr'));
                    });
                });
            }
            
            // تحديث نسبة النجاح
            function updatePassRate() {
                const totalGrades = document.querySelectorAll('.total-grade');
                let passedCount = 0;
                let totalCount = 0;
                
                totalGrades.forEach(input => {
                    const value = parseFloat(input.value);
                    if (!isNaN(value)) {
                        totalCount++;
                        if (value >= 60) {
                            passedCount++;
                        }
                    }
                });
                
                const rate = totalCount > 0 ? ((passedCount / totalCount) * 100).toFixed(1) : 0;
                passRate.textContent = `نسبة النجاح: ${rate}%`;
            }
            
            // مسح الدرجات
            window.clearGrades = function() {
                if (confirm('هل أنت متأكد من مسح جميع الدرجات؟')) {
                    document.querySelectorAll('.midterm-grade, .final-grade, .total-grade').forEach(input => {
                        input.value = '';
                    });
                    document.querySelectorAll('.grade-badge').forEach(badge => {
                        badge.textContent = '-';
                        badge.className = 'badge grade-badge bg-secondary';
                    });
                    updatePassRate();
                }
            };
        });
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
