<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة أستاذ جديد - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('professors') }}">الأساتذة</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-plus me-2"></i>إضافة أستاذ جديد
                    </h1>
                    <a href="{{ url_for('professors') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-person-workspace me-2"></i>بيانات الأستاذ
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <!-- البيانات الأساسية -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="bi bi-person-fill me-2"></i>البيانات الأساسية
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        <i class="bi bi-person-fill me-2"></i>اسم الأستاذ <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           placeholder="أدخل اسم الأستاذ الكامل">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="degree" class="form-label">
                                        <i class="bi bi-mortarboard me-2"></i>الدرجة العلمية
                                    </label>
                                    <select class="form-select" id="degree" name="degree">
                                        <option value="">اختر الدرجة العلمية</option>
                                        <option value="دكتوراه">دكتوراه</option>
                                        <option value="ماجستير">ماجستير</option>
                                        <option value="بكالوريوس">بكالوريوس</option>
                                        <option value="دبلوم عالي">دبلوم عالي</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="academic_title" class="form-label">
                                        <i class="bi bi-award me-2"></i>اللقب العلمي
                                    </label>
                                    <select class="form-select" id="academic_title" name="academic_title">
                                        <option value="">اختر اللقب العلمي</option>
                                        <option value="أستاذ">أستاذ</option>
                                        <option value="أستاذ مشارك">أستاذ مشارك</option>
                                        <option value="أستاذ مساعد">أستاذ مساعد</option>
                                        <option value="مدرس">مدرس</option>
                                        <option value="مدرس مساعد">مدرس مساعد</option>
                                        <option value="معيد">معيد</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="title_date" class="form-label">
                                        <i class="bi bi-calendar3 me-2"></i>تاريخ الحصول على اللقب
                                    </label>
                                    <input type="date" class="form-control" id="title_date" name="title_date">
                                </div>
                            </div>
                            
                            <!-- التخصص -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-bookmark-fill me-2"></i>التخصص
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="general_specialization" class="form-label">
                                        <i class="bi bi-bookmark me-2"></i>التخصص العام
                                    </label>
                                    <input type="text" class="form-control" id="general_specialization" name="general_specialization"
                                           placeholder="مثال: إدارة أعمال، هندسة، طب">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="specific_specialization" class="form-label">
                                        <i class="bi bi-bookmark-star me-2"></i>التخصص الدقيق
                                    </label>
                                    <input type="text" class="form-control" id="specific_specialization" name="specific_specialization"
                                           placeholder="مثال: إدارة مالية، هندسة مدنية">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="specialization_id" class="form-label">
                                        <i class="bi bi-tags me-2"></i>التخصص المسجل
                                    </label>
                                    <select class="form-select" id="specialization_id" name="specialization_id">
                                        <option value="">اختر التخصص</option>
                                        {% for specialization in specializations %}
                                        <option value="{{ specialization.id }}">{{ specialization.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="department_id" class="form-label">
                                        <i class="bi bi-building me-2"></i>القسم <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="department_id" name="department_id" required>
                                        <option value="">اختر القسم</option>
                                        {% for department in departments %}
                                        <option value="{{ department.id }}">
                                            {{ department.name }} - {{ department.college.name_ar if department.college else '' }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <!-- معلومات الاتصال -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-telephone-fill me-2"></i>معلومات الاتصال
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">
                                        <i class="bi bi-telephone me-2"></i>رقم الهاتف
                                    </label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           placeholder="07901234567">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        <i class="bi bi-envelope me-2"></i>البريد الإلكتروني
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           placeholder="<EMAIL>">
                                </div>
                            </div>
                            
                            <!-- ملاحظات -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-chat-text-fill me-2"></i>ملاحظات إضافية
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="notes" class="form-label">
                                        <i class="bi bi-chat-text me-2"></i>ملاحظات
                                    </label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4"
                                              placeholder="أي ملاحظات إضافية حول الأستاذ..."></textarea>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('professors') }}" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>حفظ الأستاذ
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const department_id = document.getElementById('department_id').value;
            
            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم الأستاذ');
                document.getElementById('name').focus();
                return false;
            }
            
            if (!department_id) {
                e.preventDefault();
                alert('يرجى اختيار القسم');
                document.getElementById('department_id').focus();
                return false;
            }
        });

        // تحسين تجربة المستخدم
        document.getElementById('academic_title').addEventListener('change', function() {
            const titleDate = document.getElementById('title_date');
            if (this.value && !titleDate.value) {
                // اقتراح تاريخ افتراضي (اليوم)
                titleDate.value = new Date().toISOString().split('T')[0];
            }
        });
    </script>
</body>
</html>
