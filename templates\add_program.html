<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة برنامج أكاديمي جديد - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('programs') }}">البرامج الأكاديمية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-plus-circle me-2"></i>إضافة برنامج أكاديمي جديد
                    </h1>
                    <a href="{{ url_for('programs') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-journal-bookmark me-2"></i>بيانات البرنامج الأكاديمي
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <!-- البيانات الأساسية -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="bi bi-info-circle me-2"></i>البيانات الأساسية
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        <i class="bi bi-journal-bookmark me-2"></i>اسم البرنامج <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           placeholder="أدخل اسم البرنامج الأكاديمي">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="program_type" class="form-label">
                                        <i class="bi bi-award me-2"></i>نوع البرنامج <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="program_type" name="program_type" required>
                                        <option value="">اختر نوع البرنامج</option>
                                        <option value="ماجستير">ماجستير</option>
                                        <option value="دكتوراه">دكتوراه</option>
                                        <option value="دبلوم عالي">دبلوم عالي</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="duration" class="form-label">
                                        <i class="bi bi-clock me-2"></i>مدة البرنامج
                                    </label>
                                    <select class="form-select" id="duration" name="duration">
                                        <option value="">اختر مدة البرنامج</option>
                                        <option value="سنة واحدة">سنة واحدة</option>
                                        <option value="سنتان">سنتان</option>
                                        <option value="ثلاث سنوات">ثلاث سنوات</option>
                                        <option value="أربع سنوات">أربع سنوات</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="department_id" class="form-label">
                                        <i class="bi bi-diagram-3 me-2"></i>القسم <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="department_id" name="department_id" required>
                                        <option value="">اختر القسم</option>
                                        {% for department in departments %}
                                        <option value="{{ department.id }}">
                                            {{ department.name }} - {{ department.college.name_ar if department.college else '' }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <!-- الوصف -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-file-text me-2"></i>وصف البرنامج
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="description" class="form-label">
                                        <i class="bi bi-chat-text me-2"></i>وصف البرنامج
                                    </label>
                                    <textarea class="form-control" id="description" name="description" rows="4"
                                              placeholder="وصف مفصل عن البرنامج الأكاديمي وأهدافه..."></textarea>
                                </div>
                            </div>
                            
                            <!-- معلومات إضافية -->
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>ملاحظة:</strong> بعد إضافة البرنامج، يمكنك إضافة المواد الدراسية الخاصة به من قسم إدارة المواد الدراسية.
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('programs') }}" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>حفظ البرنامج
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const programType = document.getElementById('program_type').value;
            const departmentId = document.getElementById('department_id').value;
            
            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم البرنامج');
                document.getElementById('name').focus();
                return false;
            }
            
            if (!programType) {
                e.preventDefault();
                alert('يرجى اختيار نوع البرنامج');
                document.getElementById('program_type').focus();
                return false;
            }
            
            if (!departmentId) {
                e.preventDefault();
                alert('يرجى اختيار القسم');
                document.getElementById('department_id').focus();
                return false;
            }
        });

        // تحديد المدة الافتراضية حسب نوع البرنامج
        document.getElementById('program_type').addEventListener('change', function() {
            const durationSelect = document.getElementById('duration');
            const programType = this.value;
            
            // مسح الاختيار الحالي
            durationSelect.value = '';
            
            // تحديد المدة الافتراضية
            if (programType === 'ماجستير') {
                durationSelect.value = 'سنتان';
            } else if (programType === 'دكتوراه') {
                durationSelect.value = 'ثلاث سنوات';
            } else if (programType === 'دبلوم عالي') {
                durationSelect.value = 'سنة واحدة';
            }
        });
    </script>
</body>
</html>
