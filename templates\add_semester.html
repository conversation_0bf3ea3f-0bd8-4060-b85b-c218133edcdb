<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة فصل دراسي - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding-top: 80px;
        }
        
        .main-content {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem auto;
            max-width: 900px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .page-header {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.8rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #20c997;
            box-shadow: 0 0 0 0.2rem rgba(32, 201, 151, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
            border: none;
            border-radius: 10px;
            padding: 0.8rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(32, 201, 151, 0.4);
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
        }
        
        .form-check-input:checked {
            background-color: #20c997;
            border-color: #20c997;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: none;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-content">
            <div class="page-header">
                <h1 class="mb-0">
                    <i class="bi bi-calendar-plus me-3"></i>
                    إضافة فصل دراسي جديد
                </h1>
                <p class="mb-0 mt-2">إضافة فصل دراسي جديد إلى النظام</p>
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0 text-info">
                        <i class="bi bi-info-circle me-2"></i>
                        بيانات الفصل الدراسي
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="POST" id="semesterForm">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="name" class="form-label">
                                    <i class="bi bi-calendar3 me-2"></i>
                                    اسم الفصل الدراسي *
                                </label>
                                <input type="text" class="form-control" id="name" name="name"
                                       placeholder="مثال: الفصل الأول" required>
                                <div class="form-text">أدخل اسم الفصل الدراسي</div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('semesters') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="bi bi-arrow-right me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ الفصل الدراسي
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="mt-4">
                <div class="alert alert-info">
                    <i class="bi bi-lightbulb me-2"></i>
                    <strong>نصائح:</strong>
                    <ul class="mb-0 mt-2">
                        <li>تأكد من أن تاريخ البداية قبل تاريخ النهاية</li>
                        <li>يمكن أن يكون هناك فصل دراسي حالي واحد فقط</li>
                        <li>فترة التسجيل اختيارية ويمكن تركها فارغة</li>
                        <li>إذا تم تحديد فترة التسجيل، تأكد من أنها ضمن فترة الفصل الدراسي</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من صحة التواريخ
        document.getElementById('semesterForm').addEventListener('submit', function(e) {
            const startDate = new Date(document.getElementById('start_date').value);
            const endDate = new Date(document.getElementById('end_date').value);
            const regStart = document.getElementById('registration_start').value;
            const regEnd = document.getElementById('registration_end').value;
            
            if (startDate >= endDate) {
                e.preventDefault();
                alert('تاريخ بداية الفصل يجب أن يكون قبل تاريخ النهاية');
                return false;
            }
            
            if (regStart && regEnd) {
                const regStartDate = new Date(regStart);
                const regEndDate = new Date(regEnd);
                
                if (regStartDate >= regEndDate) {
                    e.preventDefault();
                    alert('تاريخ بداية التسجيل يجب أن يكون قبل تاريخ النهاية');
                    return false;
                }
                
                if (regStartDate < startDate || regEndDate > endDate) {
                    e.preventDefault();
                    alert('فترة التسجيل يجب أن تكون ضمن فترة الفصل الدراسي');
                    return false;
                }
            }
        });

        // تحديث اسم الفصل تلقائياً عند تغيير النوع
        document.getElementById('semester_type').addEventListener('change', function() {
            const nameField = document.getElementById('name');
            const type = this.value;
            
            if (type === 'fall') {
                nameField.value = 'الفصل الأول';
            } else if (type === 'spring') {
                nameField.value = 'الفصل الثاني';
            } else if (type === 'summer') {
                nameField.value = 'الفصل الصيفي';
            }
        });

        // إخفاء الرسائل تلقائياً بعد 5 ثوانٍ
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
