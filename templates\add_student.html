<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة طالب جديد - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('students') }}">الطلبة</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-plus me-2"></i>إضافة طالب جديد
                    </h1>
                    <a href="{{ url_for('students') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-person me-2"></i>بيانات الطالب
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        <i class="bi bi-person-fill me-2"></i>اسم الطالب <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           placeholder="أدخل اسم الطالب الكامل">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="gender" class="form-label">
                                        <i class="bi bi-gender-ambiguous me-2"></i>الجنس <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="gender" name="gender" required>
                                        <option value="">اختر الجنس</option>
                                        <option value="ذكر">ذكر</option>
                                        <option value="أنثى">أنثى</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="program_id" class="form-label">
                                        <i class="bi bi-mortarboard me-2"></i>البرنامج الأكاديمي <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="program_id" name="program_id" required>
                                        <option value="">اختر البرنامج الأكاديمي</option>
                                        {% for program in programs %}
                                        <option value="{{ program.id }}">{{ program.name }} ({{ program.program_type }})</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="specialization_id" class="form-label">
                                        <i class="bi bi-bookmark me-2"></i>التخصص <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="specialization_id" name="specialization_id" required>
                                        <option value="">اختر التخصص</option>
                                        {% for specialization in specializations %}
                                        <option value="{{ specialization.id }}">{{ specialization.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="admission_channel_id" class="form-label">
                                        <i class="bi bi-door-open me-2"></i>قناة القبول <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="admission_channel_id" name="admission_channel_id" required>
                                        <option value="">اختر قناة القبول</option>
                                        {% for channel in admission_channels %}
                                        <option value="{{ channel.id }}">{{ channel.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="academic_year_id" class="form-label">
                                        <i class="bi bi-calendar3 me-2"></i>العام الدراسي <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="academic_year_id" name="academic_year_id" required>
                                        <option value="">اختر العام الدراسي</option>
                                        {% for year in academic_years %}
                                        <option value="{{ year.id }}" {% if year.is_current %}selected{% endif %}>{{ year.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">
                                        <i class="bi bi-check-circle me-2"></i>حالة الطالب
                                    </label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="مستمر بالدراسة" selected>مستمر بالدراسة</option>
                                        <option value="خريج">خريج</option>
                                        <option value="ترقين قيد">ترقين قيد أو إنهاء علاقة</option>
                                        <option value="منقول">منقول</option>
                                        <option value="منسحب">منسحب</option>
                                        <option value="تأجيل">تأجيل</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('students') }}" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>حفظ الطالب
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const gender = document.getElementById('gender').value;
            const program_id = document.getElementById('program_id').value;
            const specialization_id = document.getElementById('specialization_id').value;
            const admission_channel_id = document.getElementById('admission_channel_id').value;
            const academic_year_id = document.getElementById('academic_year_id').value;
            
            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم الطالب');
                document.getElementById('name').focus();
                return false;
            }
            
            if (!gender) {
                e.preventDefault();
                alert('يرجى اختيار الجنس');
                document.getElementById('gender').focus();
                return false;
            }
            
            if (!program_id) {
                e.preventDefault();
                alert('يرجى اختيار البرنامج الأكاديمي');
                document.getElementById('program_id').focus();
                return false;
            }
            
            if (!specialization_id) {
                e.preventDefault();
                alert('يرجى اختيار التخصص');
                document.getElementById('specialization_id').focus();
                return false;
            }
            
            if (!admission_channel_id) {
                e.preventDefault();
                alert('يرجى اختيار قناة القبول');
                document.getElementById('admission_channel_id').focus();
                return false;
            }
            
            if (!academic_year_id) {
                e.preventDefault();
                alert('يرجى اختيار العام الدراسي');
                document.getElementById('academic_year_id').focus();
                return false;
            }
        });
    </script>
</body>
</html>
