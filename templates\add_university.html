<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة جامعة جديدة - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('universities') }}">الجامعات</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-plus-circle me-2"></i>إضافة جامعة جديدة
                    </h1>
                    <a href="{{ url_for('universities') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-building me-2"></i>بيانات الجامعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name_ar" class="form-label">
                                        <i class="bi bi-translate me-2"></i>اسم الجامعة (عربي) <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name_ar" name="name_ar" required 
                                           placeholder="أدخل اسم الجامعة بالعربية">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="name_en" class="form-label">
                                        <i class="bi bi-globe me-2"></i>اسم الجامعة (إنجليزي)
                                    </label>
                                    <input type="text" class="form-control" id="name_en" name="name_en"
                                           placeholder="Enter university name in English">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">
                                    <i class="bi bi-geo-alt me-2"></i>العنوان
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="3"
                                          placeholder="أدخل عنوان الجامعة"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="logo" class="form-label">
                                    <i class="bi bi-image me-2"></i>شعار الجامعة
                                </label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                <div class="form-text">الملفات المدعومة: JPG, PNG, GIF (الحد الأقصى: 16MB)</div>
                                <img id="logo-preview" class="mt-2" style="display: none; max-width: 200px; max-height: 200px; border-radius: 8px;">
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('universities') }}" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>حفظ الجامعة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // معاينة الصورة
        document.getElementById('logo').addEventListener('change', function() {
            const file = this.files[0];
            const preview = document.getElementById('logo-preview');
            
            if (file) {
                // التحقق من نوع الملف
                const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    alert('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)');
                    this.value = '';
                    preview.style.display = 'none';
                    return;
                }
                
                // التحقق من حجم الملف (16MB)
                if (file.size > 16 * 1024 * 1024) {
                    alert('حجم الملف كبير جداً. الحد الأقصى 16 ميجابايت');
                    this.value = '';
                    preview.style.display = 'none';
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        });

        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const nameAr = document.getElementById('name_ar').value.trim();
            
            if (!nameAr) {
                e.preventDefault();
                alert('يرجى إدخال اسم الجامعة بالعربية');
                document.getElementById('name_ar').focus();
                return false;
            }
        });
    </script>
</body>
</html>
