<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مستخدم جديد - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('users') }}">المستخدمون</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-plus me-2"></i>إضافة مستخدم جديد
                    </h1>
                    <a href="{{ url_for('users') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-person-fill me-2"></i>بيانات المستخدم
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="userForm">
                            <!-- بيانات تسجيل الدخول -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="bi bi-key-fill me-2"></i>بيانات تسجيل الدخول
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">
                                        <i class="bi bi-person-circle me-2"></i>اسم المستخدم <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" required 
                                           placeholder="أدخل اسم المستخدم"
                                           pattern="[a-zA-Z0-9_]+" 
                                           title="يجب أن يحتوي على أحرف إنجليزية وأرقام فقط">
                                    <div class="form-text">يجب أن يحتوي على أحرف إنجليزية وأرقام فقط</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="user_type" class="form-label">
                                        <i class="bi bi-shield-check me-2"></i>نوع المستخدم <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="user_type" name="user_type" required>
                                        <option value="">اختر نوع المستخدم</option>
                                        <option value="admin">مدير النظام</option>
                                        <option value="user">مستخدم عادي</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">
                                        <i class="bi bi-lock me-2"></i>كلمة المرور <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" required 
                                               placeholder="أدخل كلمة المرور"
                                               minlength="6">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label">
                                        <i class="bi bi-lock-fill me-2"></i>تأكيد كلمة المرور <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required 
                                           placeholder="أعد إدخال كلمة المرور">
                                    <div class="invalid-feedback" id="passwordMismatch" style="display: none;">
                                        كلمات المرور غير متطابقة
                                    </div>
                                </div>
                            </div>
                            
                            <!-- البيانات الشخصية -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-person-fill me-2"></i>البيانات الشخصية
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label">
                                        <i class="bi bi-person me-2"></i>الاسم الكامل <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" required 
                                           placeholder="أدخل الاسم الكامل">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        <i class="bi bi-envelope me-2"></i>البريد الإلكتروني
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           placeholder="<EMAIL>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">
                                        <i class="bi bi-telephone me-2"></i>رقم الهاتف
                                    </label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           placeholder="07901234567">
                                </div>
                            </div>
                            
                            <!-- تحذير للمديرين -->
                            <div class="alert alert-warning" id="adminWarning" style="display: none;">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <strong>تحذير:</strong> مدير النظام له صلاحيات كاملة على جميع البيانات والوظائف.
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('users') }}" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>حفظ المستخدم
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const userTypeSelect = document.getElementById('user_type');
            const adminWarning = document.getElementById('adminWarning');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const togglePasswordBtn = document.getElementById('togglePassword');
            const passwordMismatch = document.getElementById('passwordMismatch');
            const form = document.getElementById('userForm');

            // إظهار تحذير للمديرين
            userTypeSelect.addEventListener('change', function() {
                if (this.value === 'admin') {
                    adminWarning.style.display = 'block';
                } else {
                    adminWarning.style.display = 'none';
                }
            });

            // إظهار/إخفاء كلمة المرور
            togglePasswordBtn.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.className = type === 'password' ? 'bi bi-eye' : 'bi bi-eye-slash';
            });

            // التحقق من تطابق كلمات المرور
            function checkPasswordMatch() {
                if (confirmPasswordInput.value && passwordInput.value !== confirmPasswordInput.value) {
                    confirmPasswordInput.classList.add('is-invalid');
                    passwordMismatch.style.display = 'block';
                    return false;
                } else {
                    confirmPasswordInput.classList.remove('is-invalid');
                    passwordMismatch.style.display = 'none';
                    return true;
                }
            }

            confirmPasswordInput.addEventListener('input', checkPasswordMatch);
            passwordInput.addEventListener('input', checkPasswordMatch);

            // التحقق من صحة النموذج
            form.addEventListener('submit', function(e) {
                const username = document.getElementById('username').value.trim();
                const fullName = document.getElementById('full_name').value.trim();
                const userType = document.getElementById('user_type').value;
                const password = document.getElementById('password').value;
                
                if (!username) {
                    e.preventDefault();
                    alert('يرجى إدخال اسم المستخدم');
                    document.getElementById('username').focus();
                    return false;
                }
                
                if (!fullName) {
                    e.preventDefault();
                    alert('يرجى إدخال الاسم الكامل');
                    document.getElementById('full_name').focus();
                    return false;
                }
                
                if (!userType) {
                    e.preventDefault();
                    alert('يرجى اختيار نوع المستخدم');
                    document.getElementById('user_type').focus();
                    return false;
                }
                
                if (!password) {
                    e.preventDefault();
                    alert('يرجى إدخال كلمة المرور');
                    document.getElementById('password').focus();
                    return false;
                }
                
                if (password.length < 6) {
                    e.preventDefault();
                    alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                    document.getElementById('password').focus();
                    return false;
                }
                
                if (!checkPasswordMatch()) {
                    e.preventDefault();
                    alert('كلمات المرور غير متطابقة');
                    document.getElementById('confirm_password').focus();
                    return false;
                }
            });

            // التحقق من اسم المستخدم (أحرف إنجليزية وأرقام فقط)
            document.getElementById('username').addEventListener('input', function() {
                const value = this.value;
                const regex = /^[a-zA-Z0-9_]*$/;
                
                if (!regex.test(value)) {
                    this.setCustomValidity('يجب أن يحتوي على أحرف إنجليزية وأرقام فقط');
                } else {
                    this.setCustomValidity('');
                }
            });
        });
    </script>
</body>
</html>
