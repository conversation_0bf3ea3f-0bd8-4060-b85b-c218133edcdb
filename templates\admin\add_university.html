{% extends "base/layout.html" %}

{% block title %}إضافة جامعة جديدة - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-plus-circle me-2"></i>إضافة جامعة جديدة
            </h1>
            <a href="{{ url_for('admin.universities') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-building me-2"></i>بيانات الجامعة
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name_ar" class="form-label">
                                <i class="bi bi-translate me-2"></i>اسم الجامعة (عربي) <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name_ar" name="name_ar" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name_en" class="form-label">
                                <i class="bi bi-globe me-2"></i>اسم الجامعة (إنجليزي)
                            </label>
                            <input type="text" class="form-control" id="name_en" name="name_en">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">
                            <i class="bi bi-geo-alt me-2"></i>العنوان
                        </label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="logo" class="form-label">
                            <i class="bi bi-image me-2"></i>شعار الجامعة
                        </label>
                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                        <div class="form-text">الملفات المدعومة: JPG, PNG, GIF (الحد الأقصى: 16MB)</div>
                        <img id="logo-preview" class="image-preview mt-2" style="display: none; max-width: 200px; max-height: 200px;">
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.universities') }}" class="btn btn-secondary me-md-2">
                            <i class="bi bi-x-circle me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>حفظ الجامعة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('logo').addEventListener('change', function() {
    const file = this.files[0];
    const preview = document.getElementById('logo-preview');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});
</script>
{% endblock %}
