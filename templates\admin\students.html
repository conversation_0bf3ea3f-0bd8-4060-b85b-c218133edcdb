{% extends "base/layout.html" %}

{% block title %}إدارة الطلبة - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-people me-2"></i>إدارة الطلبة
            </h1>
            <a href="{{ url_for('admin.add_student') }}" class="btn btn-primary">
                <i class="bi bi-person-plus me-2"></i>إضافة طالب جديد
            </a>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control table-search" id="search" placeholder="البحث في الطلبة...">
                    </div>
                    <div class="col-md-3">
                        <label for="program_filter" class="form-label">البرنامج الأكاديمي</label>
                        <select class="form-select" id="program_filter">
                            <option value="">جميع البرامج</option>
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status_filter" class="form-label">حالة الطالب</label>
                        <select class="form-select" id="status_filter">
                            <option value="">جميع الحالات</option>
                            <option value="مستمر بالدراسة">مستمر بالدراسة</option>
                            <option value="خريج">خريج</option>
                            <option value="ترقين قيد">ترقين قيد</option>
                            <option value="منقول">منقول</option>
                            <option value="منسحب">منسحب</option>
                            <option value="تأجيل">تأجيل</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="gender_filter" class="form-label">الجنس</label>
                        <select class="form-select" id="gender_filter">
                            <option value="">الكل</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-list-ul me-2"></i>قائمة الطلبة
                    <span class="badge bg-primary ms-2">{{ students|length }}</span>
                </h6>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-success btn-export" data-format="excel">
                        <i class="bi bi-file-earmark-excel me-1"></i>Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger btn-print">
                        <i class="bi bi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if students %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="studentsTable">
                        <thead>
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" class="form-check-input select-all" data-target=".student-checkbox">
                                </th>
                                <th width="5%">#</th>
                                <th width="20%">اسم الطالب</th>
                                <th width="8%">الجنس</th>
                                <th width="20%">البرنامج الأكاديمي</th>
                                <th width="12%">التخصص</th>
                                <th width="10%">قناة القبول</th>
                                <th width="10%">الحالة</th>
                                <th width="10%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in students %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input student-checkbox item-checkbox" value="{{ student.id }}">
                                </td>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-2">
                                            <div class="avatar-title bg-{{ 'primary' if student.gender == 'ذكر' else 'pink' }} text-white rounded-circle">
                                                {{ student.name[0] }}
                                            </div>
                                        </div>
                                        <div>
                                            <strong>{{ student.name }}</strong><br>
                                            <small class="text-muted">{{ student.academic_year.name }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if student.gender == 'ذكر' else 'pink' }}">
                                        {{ student.gender }}
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ student.program.name }}</strong><br>
                                        <small class="text-muted">{{ student.program.program_type }}</small>
                                    </div>
                                </td>
                                <td>{{ student.specialization.name }}</td>
                                <td>{{ student.admission_channel.name }}</td>
                                <td>
                                    {% set status_colors = {
                                        'مستمر بالدراسة': 'success',
                                        'خريج': 'primary',
                                        'ترقين قيد': 'warning',
                                        'منقول': 'info',
                                        'منسحب': 'danger',
                                        'تأجيل': 'secondary'
                                    } %}
                                    <span class="badge bg-{{ status_colors.get(student.status, 'secondary') }}">
                                        {{ student.status }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('reports.student_transcript', student_id=student.id) }}" 
                                           class="btn btn-sm btn-outline-info" 
                                           data-bs-toggle="tooltip" 
                                           title="كشف الدرجات">
                                            <i class="bi bi-file-earmark-text"></i>
                                        </a>
                                        <a href="{{ url_for('admin.edit_student', id=student.id) }}" 
                                           class="btn btn-sm btn-outline-primary" 
                                           data-bs-toggle="tooltip" 
                                           title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{{ url_for('admin.delete_student', id=student.id) }}" 
                                           class="btn btn-sm btn-outline-danger btn-delete" 
                                           data-bs-toggle="tooltip" 
                                           title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- إجراءات مجمعة -->
                <div class="mt-3 d-none" id="bulkActions">
                    <div class="alert alert-info">
                        تم تحديد <span class="selected-count">0</span> طالب
                        <div class="btn-group ms-3">
                            <button type="button" class="btn btn-sm btn-outline-primary">تصدير المحدد</button>
                            <button type="button" class="btn btn-sm btn-outline-warning">تغيير الحالة</button>
                            <button type="button" class="btn btn-sm btn-outline-danger">حذف المحدد</button>
                        </div>
                    </div>
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">لا يوجد طلبة مسجلون</h4>
                    <p class="text-muted">ابدأ بإضافة طالب جديد</p>
                    <a href="{{ url_for('admin.add_student') }}" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>إضافة طالب جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-sm {
    width: 2rem;
    height: 2rem;
}
.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}
.bg-pink {
    background-color: #e91e63 !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// تفعيل الفلاتر
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const programFilter = document.getElementById('program_filter');
    const statusFilter = document.getElementById('status_filter');
    const genderFilter = document.getElementById('gender_filter');
    const table = document.getElementById('studentsTable');
    const bulkActions = document.getElementById('bulkActions');
    
    // فلترة الجدول
    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const programValue = programFilter.value;
        const statusValue = statusFilter.value;
        const genderValue = genderFilter.value;
        
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const name = row.cells[2].textContent.toLowerCase();
            const gender = row.cells[3].textContent.trim();
            const program = row.cells[4].textContent.toLowerCase();
            const status = row.cells[7].textContent.trim();
            
            const matchesSearch = name.includes(searchTerm);
            const matchesProgram = !programValue || program.includes(programValue.toLowerCase());
            const matchesStatus = !statusValue || status.includes(statusValue);
            const matchesGender = !genderValue || gender.includes(genderValue);
            
            if (matchesSearch && matchesProgram && matchesStatus && matchesGender) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }
    
    // ربط الأحداث
    searchInput.addEventListener('keyup', filterTable);
    programFilter.addEventListener('change', filterTable);
    statusFilter.addEventListener('change', filterTable);
    genderFilter.addEventListener('change', filterTable);
    
    // إدارة التحديد المجمع
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.student-checkbox:checked').length;
            document.querySelector('.selected-count').textContent = checkedCount;
            
            if (checkedCount > 0) {
                bulkActions.classList.remove('d-none');
            } else {
                bulkActions.classList.add('d-none');
            }
        });
    });
});
</script>
{% endblock %}
