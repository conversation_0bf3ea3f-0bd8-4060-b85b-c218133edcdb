{% extends "base/layout.html" %}

{% block title %}إدارة الجامعات - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-building me-2"></i>إدارة الجامعات
            </h1>
            <a href="{{ url_for('admin.add_university') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>إضافة جامعة جديدة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-list-ul me-2"></i>قائمة الجامعات
                </h6>
            </div>
            <div class="card-body">
                {% if universities %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="25%">اسم الجامعة (عربي)</th>
                                <th width="25%">اسم الجامعة (إنجليزي)</th>
                                <th width="25%">العنوان</th>
                                <th width="10%">عدد الكليات</th>
                                <th width="10%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for university in universities %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ university.name_ar }}</td>
                                <td>{{ university.name_en or '-' }}</td>
                                <td>{{ university.address or '-' }}</td>
                                <td>
                                    <span class="badge bg-info">{{ university.colleges|length }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('admin.edit_university', id=university.id) }}" 
                                           class="btn btn-sm btn-outline-primary" 
                                           data-bs-toggle="tooltip" 
                                           title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{{ url_for('admin.delete_university', id=university.id) }}" 
                                           class="btn btn-sm btn-outline-danger btn-delete" 
                                           data-bs-toggle="tooltip" 
                                           title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">لا توجد جامعات مسجلة</h4>
                    <p class="text-muted">ابدأ بإضافة جامعة جديدة</p>
                    <a href="{{ url_for('admin.add_university') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة جامعة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
