<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="نظام إدارة الدراسات العليا - نظام شامل ومتطور لإدارة جميع جوانب الدراسات العليا">
    <meta name="keywords" content="دراسات عليا, إدارة, جامعة, طلبة, أساتذة, درجات">
    <meta name="author" content="نظام إدارة الدراسات العليا">
    
    <title>{% block title %}نظام إدارة الدراسات العليا{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/enhanced-styles.css') }}" rel="stylesheet">
    
    <!-- Additional CSS -->
    {% block extra_css %}{% endblock %}
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        /* Page-specific enhancements */
        .page-wrapper {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .content-wrapper {
            padding: 2rem 0;
        }
        
        .navbar-enhanced {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(15px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-radius: 0 0 20px 20px;
            margin-bottom: 2rem;
        }
        
        .navbar-brand-enhanced {
            font-weight: 700;
            font-size: 1.4rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-link-enhanced {
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 10px;
            margin: 0 0.2rem;
        }
        
        .nav-link-enhanced:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white !important;
            transform: translateY(-2px);
        }
        
        .nav-link-enhanced.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white !important;
        }
        
        .footer-enhanced {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px 20px 0 0;
            margin-top: 3rem;
            padding: 2rem 0;
            color: white;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(102, 126, 234, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(10px);
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body data-theme="light">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p class="text-white mt-3">جاري التحميل...</p>
        </div>
    </div>

    <div class="page-wrapper">
        <!-- Enhanced Navigation -->
        <nav class="navbar navbar-expand-lg navbar-enhanced">
            <div class="container-fluid">
                <a class="navbar-brand navbar-brand-enhanced" href="{{ url_for('dashboard') }}">
                    <i class="bi bi-mortarboard-fill me-2"></i>
                    نظام إدارة الدراسات العليا
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link nav-link-enhanced {{ 'active' if request.endpoint == 'dashboard' }}" 
                               href="{{ url_for('dashboard') }}">
                                <i class="bi bi-house-fill me-1"></i>الرئيسية
                            </a>
                        </li>
                        
                        {% if current_user.user_type == 'admin' %}
                        <li class="nav-item dropdown">
                            <a class="nav-link nav-link-enhanced dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-gear-fill me-1"></i>الإدارة
                            </a>
                            <ul class="dropdown-menu dropdown-menu-enhanced">
                                <li><h6 class="dropdown-header">المؤسسات التعليمية</h6></li>
                                <li><a class="dropdown-item dropdown-item-enhanced" href="{{ url_for('universities') }}">
                                    <i class="bi bi-building me-2"></i>الجامعات
                                </a></li>
                                <li><a class="dropdown-item dropdown-item-enhanced" href="{{ url_for('colleges') }}">
                                    <i class="bi bi-bank me-2"></i>الكليات
                                </a></li>
                                <li><a class="dropdown-item dropdown-item-enhanced" href="{{ url_for('departments') }}">
                                    <i class="bi bi-diagram-3 me-2"></i>الأقسام
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header">إدارة المستخدمين</h6></li>
                                <li><a class="dropdown-item dropdown-item-enhanced" href="{{ url_for('users') }}">
                                    <i class="bi bi-people me-2"></i>المستخدمين
                                </a></li>
                            </ul>
                        </li>
                        {% endif %}
                        
                        <li class="nav-item">
                            <a class="nav-link nav-link-enhanced {{ 'active' if request.endpoint == 'students' }}" 
                               href="{{ url_for('students') }}">
                                <i class="bi bi-people me-1"></i>الطلبة
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link nav-link-enhanced {{ 'active' if request.endpoint == 'professors' }}" 
                               href="{{ url_for('professors') }}">
                                <i class="bi bi-person-workspace me-1"></i>الأساتذة
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link nav-link-enhanced {{ 'active' if request.endpoint == 'programs' }}" 
                               href="{{ url_for('programs') }}">
                                <i class="bi bi-journal-bookmark me-1"></i>البرامج
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link nav-link-enhanced {{ 'active' if request.endpoint == 'subjects' }}" 
                               href="{{ url_for('subjects') }}">
                                <i class="bi bi-book me-1"></i>المواد
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link nav-link-enhanced {{ 'active' if request.endpoint == 'grades' }}" 
                               href="{{ url_for('grades') }}">
                                <i class="bi bi-clipboard-data me-1"></i>الدرجات
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link nav-link-enhanced {{ 'active' if request.endpoint == 'reports' }}" 
                               href="{{ url_for('reports') }}">
                                <i class="bi bi-graph-up me-1"></i>التقارير
                            </a>
                        </li>
                    </ul>
                    
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link nav-link-enhanced dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>{{ current_user.full_name }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-enhanced">
                                <li><a class="dropdown-item dropdown-item-enhanced" href="{{ url_for('profile') }}">
                                    <i class="bi bi-person me-2"></i>الملف الشخصي
                                </a></li>
                                <li><a class="dropdown-item dropdown-item-enhanced" href="{{ url_for('settings') }}">
                                    <i class="bi bi-gear me-2"></i>الإعدادات
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><button class="dropdown-item dropdown-item-enhanced" id="themeToggle">
                                    <i class="bi bi-moon me-2"></i>الوضع الليلي
                                </button></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item dropdown-item-enhanced text-danger" href="{{ url_for('logout') }}">
                                    <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="container-fluid">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-enhanced alert-dismissible fade show animate-fade-in-up" role="alert">
                            <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Main Content -->
        <div class="content-wrapper">
            {% block content %}{% endblock %}
        </div>

        <!-- Enhanced Footer -->
        <footer class="footer-enhanced">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا</h5>
                        <p class="mb-0">نظام شامل ومتطور لإدارة جميع جوانب الدراسات العليا</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <p class="mb-0">© 2024 جميع الحقوق محفوظة</p>
                        <small>الإصدار 2.0 - محسن ومطور</small>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/enhanced-app.js') }}"></script>
    
    <!-- Additional JavaScript -->
    {% block extra_js %}{% endblock %}
    
    <script>
        // Show loading overlay for navigation
        document.querySelectorAll('a[href]').forEach(link => {
            link.addEventListener('click', function(e) {
                if (this.href && !this.href.includes('#') && !this.target) {
                    document.getElementById('loadingOverlay').style.display = 'flex';
                }
            });
        });

        // Hide loading overlay when page loads
        window.addEventListener('load', () => {
            document.getElementById('loadingOverlay').style.display = 'none';
        });
    </script>
</body>
</html>
