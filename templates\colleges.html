<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الكليات - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: #0a0a0a;
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* خلفية ديناميكية */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 25% 25%, #f59e0b 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #ef4444 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, #8b5cf6 0%, transparent 50%),
                linear-gradient(135deg, #1e1b4b 0%, #0f172a 100%);
            z-index: -2;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse"><path d="M 8 0 L 0 0 0 8" fill="none" stroke="%23ffffff" stroke-width="0.3" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
            opacity: 0.4;
        }

        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            50% { filter: hue-rotate(30deg) brightness(1.1); }
        }

        .main-container {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.9) 0%,
                rgba(30, 27, 75, 0.9) 100%);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 32px;
            padding: 3rem;
            margin: 2rem auto;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(24px) saturate(180%);
        }

        .page-header {
            background: linear-gradient(135deg,
                rgba(245, 158, 11, 0.9) 0%,
                rgba(239, 68, 68, 0.9) 100%);
            color: white;
            border-radius: 24px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow:
                0 20px 40px rgba(245, 158, 11, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.1),
                transparent);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }

        /* تحسين الـ Accordion للكليات */
        .college-accordion .accordion-item {
            border: 1px solid rgba(245, 158, 11, 0.2);
            border-radius: 20px !important;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.4s ease;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.8) 0%,
                rgba(30, 27, 75, 0.8) 100%);
        }

        .college-accordion .accordion-item:hover {
            box-shadow: 0 15px 40px rgba(245, 158, 11, 0.2);
            transform: translateY(-5px);
            border-color: rgba(245, 158, 11, 0.4);
        }

        .college-accordion .accordion-button {
            background: linear-gradient(135deg,
                rgba(245, 158, 11, 0.1) 0%,
                rgba(239, 68, 68, 0.1) 100%);
            border: none;
            padding: 2rem;
            font-weight: 700;
            border-radius: 20px !important;
            color: #ffffff;
            font-size: 1.1rem;
        }

        .college-accordion .accordion-button:not(.collapsed) {
            background: linear-gradient(135deg,
                rgba(245, 158, 11, 0.9) 0%,
                rgba(239, 68, 68, 0.9) 100%);
            color: white;
            box-shadow: none;
        }

        .college-accordion .accordion-button:focus {
            box-shadow: 0 0 0 0.25rem rgba(245, 158, 11, 0.25);
        }

        .college-accordion .accordion-button::after {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
            transition: transform 0.3s ease;
        }

        .college-accordion .accordion-button:not(.collapsed)::after {
            transform: rotate(180deg);
        }

        .college-accordion .accordion-body {
            border-top: 1px solid rgba(245, 158, 11, 0.2);
            background: rgba(15, 23, 42, 0.5);
            padding: 0;
        }

        .department-card {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.05) 0%,
                rgba(255, 255, 255, 0.02) 100%);
            border: 1px solid rgba(245, 158, 11, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
            margin: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .department-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #f59e0b, #ef4444);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .department-card:hover::before {
            transform: scaleX(1);
        }

        .department-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(245, 158, 11, 0.2);
            border-color: rgba(245, 158, 11, 0.3);
        }

        .stats-badge {
            background: linear-gradient(135deg, #f59e0b, #ef4444);
            color: white;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .action-btn {
            background: linear-gradient(135deg,
                rgba(245, 158, 11, 0.1) 0%,
                rgba(239, 68, 68, 0.1) 100%);
            border: 1px solid rgba(245, 158, 11, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: linear-gradient(135deg, #f59e0b, #ef4444);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(245, 158, 11, 0.4);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

            <div class="page-header">
                <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 800;">
                    <i class="bi bi-bank me-3"></i>
                    إدارة الكليات
                </h1>
                <p class="mb-0 mt-3" style="font-size: 1.2rem; opacity: 0.9;">
                    إدارة شاملة للكليات والأقسام الأكاديمية
                </p>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: #ffffff; font-weight: 700;">
                        <i class="bi bi-list-ul me-2" style="color: #f59e0b;"></i>
                        قائمة الكليات
                    </h2>
                    <p class="text-muted mb-0">{{ colleges|length }} كلية مسجلة في النظام</p>
                </div>
                <div class="d-flex gap-3">
                    <button class="action-btn" onclick="toggleAllColleges()">
                        <i class="bi bi-arrows-expand me-2"></i>فتح جميع الكليات
                    </button>
                    <a href="{{ url_for('add_college') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة كلية جديدة
                    </a>
                </div>
            </div>

            {% if colleges %}
                <!-- تجميع الكليات حسب الجامعة -->
                {% set colleges_by_university = {} %}
                {% for college in colleges %}
                    {% set university_name = college.university.name_ar if college.university else 'غير محدد' %}
                    {% if university_name not in colleges_by_university %}
                        {% set _ = colleges_by_university.update({university_name: []}) %}
                    {% endif %}
                    {% set _ = colleges_by_university[university_name].append(college) %}
                {% endfor %}

                <div class="accordion college-accordion" id="collegesAccordion">
                    {% for university_name, university_colleges in colleges_by_university.items() %}
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading{{ loop.index }}">
                                <button class="accordion-button collapsed" type="button"
                                        data-bs-toggle="collapse"
                                        data-bs-target="#collapse{{ loop.index }}"
                                        aria-expanded="false"
                                        aria-controls="collapse{{ loop.index }}">
                                    <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-building me-3 fs-4"></i>
                                            <div>
                                                <h5 class="mb-0 fw-bold">{{ university_name }}</h5>
                                                <small class="opacity-75">{{ university_colleges|length }} كلية</small>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center gap-3">
                                            <span class="stats-badge">{{ university_colleges|length }}</span>
                                            {% set total_departments = university_colleges|map(attribute='departments')|map('length')|sum %}
                                            {% if total_departments > 0 %}
                                                <span class="badge bg-info rounded-pill">{{ total_departments }} قسم</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </button>
                            </h2>
                            <div id="collapse{{ loop.index }}"
                                 class="accordion-collapse collapse"
                                 aria-labelledby="heading{{ loop.index }}"
                                 data-bs-parent="#collegesAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        {% for college in university_colleges %}
                                            <div class="col-lg-6 col-xl-4 mb-3">
                                                <div class="department-card">
                                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-sm me-3" style="width: 50px; height: 50px; background: linear-gradient(135deg, #f59e0b, #ef4444); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.2rem;">
                                                                {{ college.name_ar[0] if college.name_ar else 'ك' }}
                                                            </div>
                                                            <div>
                                                                <h6 class="mb-1 fw-bold text-white">{{ college.name_ar }}</h6>
                                                                <small class="text-muted">{{ college.name_en or 'غير محدد' }}</small>
                                                            </div>
                                                        </div>
                                                        <div class="dropdown">
                                                            <button class="action-btn btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="{{ url_for('edit_college', college_id=college.id) }}">
                                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                                </a></li>
                                                                <li><a class="dropdown-item" href="{{ url_for('departments') }}?college_id={{ college.id }}">
                                                                    <i class="bi bi-diagram-3 me-2"></i>عرض الأقسام
                                                                </a></li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete('{{ college.name_ar }}', {{ college.id }})">
                                                                    <i class="bi bi-trash me-2"></i>حذف
                                                                </a></li>
                                                            </ul>
                                                        </div>
                                                    </div>

                                                    {% if college.address %}
                                                        <div class="mb-2">
                                                            <small class="text-muted">العنوان:</small>
                                                            <div class="text-white">{{ college.address }}</div>
                                                        </div>
                                                    {% endif %}

                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div class="d-flex gap-2">
                                                            {% if college.departments %}
                                                                <span class="badge bg-success rounded-pill">{{ college.departments|length }} قسم</span>
                                                            {% else %}
                                                                <span class="badge bg-secondary rounded-pill">لا توجد أقسام</span>
                                                            {% endif %}
                                                        </div>
                                                        <small class="text-muted">
                                                            <i class="bi bi-calendar3 me-1"></i>
                                                            {{ college.created_at.strftime('%Y-%m-%d') if college.created_at else 'غير محدد' }}
                                                        </small>
                                                    </div>

                                                    {% if college.departments %}
                                                        <div class="mt-3">
                                                            <small class="text-muted">الأقسام:</small>
                                                            <div class="mt-1">
                                                                {% for dept in college.departments[:3] %}
                                                                    <span class="badge bg-outline-light me-1 mb-1" style="border: 1px solid rgba(245, 158, 11, 0.3); color: #f59e0b;">{{ dept.name_ar }}</span>
                                                                {% endfor %}
                                                                {% if college.departments|length > 3 %}
                                                                    <span class="badge bg-outline-light" style="border: 1px solid rgba(245, 158, 11, 0.3); color: #f59e0b;">+{{ college.departments|length - 3 }} أخرى</span>
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <div class="college-icon mx-auto mb-4" style="width: 100px; height: 100px; font-size: 3rem; opacity: 0.5;">
                        <i class="bi bi-bank"></i>
                    </div>
                    <h3 class="text-white mb-3">لا توجد كليات مسجلة</h3>
                    <p class="text-muted mb-4">لم يتم تسجيل أي كليات في النظام بعد</p>
                    <a href="{{ url_for('add_college') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة كلية جديدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        // فلترة الكليات حسب الجامعة
        function filterByUniversity(universityId) {
            const sections = document.querySelectorAll('.university-section');
            const tabs = document.querySelectorAll('.filter-tab');

            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));

            // إضافة الفئة النشطة للتبويب المحدد
            event.target.classList.add('active');

            sections.forEach(section => {
                if (universityId === 'all' || section.dataset.university === universityId) {
                    section.style.display = 'block';
                    section.style.animation = 'slideInUp 0.5s ease-out';
                } else {
                    section.style.display = 'none';
                }
            });
        }

        // فلترة عامة للكليات
        function filterColleges(type) {
            const cards = document.querySelectorAll('.college-card');

            cards.forEach((card, index) => {
                card.style.display = 'block';
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        }

        // تأكيد حذف الكلية
        function confirmDeleteCollege(collegeName, collegeId) {
            if (confirm(`هل أنت متأكد من حذف كلية "${collegeName}"؟\nسيتم حذف جميع البيانات المرتبطة بها.`)) {
                fetch(`/admin/colleges/delete/${collegeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }

        // تحسين الرسوم المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.college-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        });

        // رسوم متحركة
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-load {
                animation: slideInUp 0.6s ease-out;
                animation-fill-mode: both;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-success text-white rounded-circle">
                                                        {{ college.name_ar[0] }}
                                                    </div>
                                                </div>
                                                <strong>{{ college.name_ar }}</strong>
                                            </div>
                                        </td>
                                        <td>{{ college.name_en or 'غير محدد' }}</td>
                                        <td>
                                            <div>
                                                <strong>{{ college.university.name_ar if college.university else 'غير محدد' }}</strong>
                                                {% if college.university and college.university.name_en %}
                                                <br><small class="text-muted">{{ college.university.name_en }}</small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>{{ college.address or 'غير محدد' }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#collegeModal{{ college.id }}"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <a href="{{ url_for('edit_college', college_id=college.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   title="تعديل">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        title="حذف"
                                                        onclick="confirmDelete('{{ college.name_ar }}', {{ college.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Modal لعرض تفاصيل الكلية -->
                                    <div class="modal fade" id="collegeModal{{ college.id }}" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تفاصيل الكلية: {{ college.name_ar }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">الاسم بالعربية:</td>
                                                                    <td>{{ college.name_ar }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">الاسم بالإنجليزية:</td>
                                                                    <td>{{ college.name_en or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">العنوان:</td>
                                                                    <td>{{ college.address or 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">الجامعة:</td>
                                                                    <td>{{ college.university.name_ar if college.university else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الأقسام:</td>
                                                                    <td>{{ college.departments|length }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    
                                                    {% if college.departments %}
                                                    <div class="row mt-3">
                                                        <div class="col-12">
                                                            <h6>الأقسام التابعة:</h6>
                                                            <div class="row">
                                                                {% for department in college.departments %}
                                                                <div class="col-md-6 mb-2">
                                                                    <div class="card border-primary">
                                                                        <div class="card-body p-2">
                                                                            <h6 class="card-title mb-1">{{ department.name }}</h6>
                                                                            {% if department.head_name %}
                                                                            <small class="text-muted">رئيس القسم: {{ department.head_name }}</small>
                                                                            {% endif %}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                {% endfor %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-bank text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا توجد كليات مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة كلية جديدة</p>
                            <a href="{{ url_for('add_college') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إضافة كلية جديدة
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(name, id) {
            if (confirm('هل أنت متأكد من حذف الكلية: ' + name + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                window.location.href = '/admin/colleges/delete/' + id;
            }
        }
    </script>
        </div>
    </div>

    <script>
        // تحسين التفاعل مع الـ accordion
        function toggleAllColleges() {
            const button = event.target;
            const allCollapses = document.querySelectorAll('.accordion-collapse');
            const isExpanded = button.innerHTML.includes('فتح');

            allCollapses.forEach(collapse => {
                if (isExpanded) {
                    new bootstrap.Collapse(collapse, {show: true});
                } else {
                    new bootstrap.Collapse(collapse, {hide: true});
                }
            });

            button.innerHTML = isExpanded ?
                '<i class="bi bi-arrows-collapse me-2"></i>إغلاق جميع الكليات' :
                '<i class="bi bi-arrows-expand me-2"></i>فتح جميع الكليات';
        }

        // تأكيد الحذف
        function confirmDelete(collegeName, collegeId) {
            if (confirm(`هل أنت متأكد من حذف كلية "${collegeName}"؟\nسيتم حذف جميع الأقسام المرتبطة بها أيضاً.`)) {
                // إرسال طلب الحذف
                fetch(`/admin/colleges/delete/${collegeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }

        // تحسين الرسوم المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات للبطاقات
            const cards = document.querySelectorAll('.department-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });

            // تحسين الـ accordion
            const accordionButtons = document.querySelectorAll('.accordion-button');
            accordionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });

        // رسوم متحركة للتحميل
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-load {
                animation: slideInUp 0.6s ease-out;
                animation-fill-mode: both;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
