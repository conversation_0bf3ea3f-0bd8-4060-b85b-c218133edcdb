<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الكليات - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: 'Cairo', 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
            color: #1e293b;
            overflow-x: hidden;
        }

        /* خلفية فاتحة ومريحة للكليات */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 40% 20%, rgba(245, 158, 11, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 60% 80%, rgba(220, 38, 38, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 20% 60%, rgba(124, 58, 237, 0.08) 0%, transparent 50%),
                linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(226, 232, 240, 0.9) 100%);
            z-index: -2;
            animation: backgroundShift 60s ease-in-out infinite;
        }
        
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse"><path d="M 8 0 L 0 0 0 8" fill="none" stroke="%23ffffff" stroke-width="0.3" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
            opacity: 0.4;
        }
        
        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            50% { filter: hue-rotate(30deg) brightness(1.1); }
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 24px;
            padding: 3rem;
            margin: 2rem auto;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px) saturate(180%);
        }
        
        .page-header {
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.1) 0%,
                rgba(16, 185, 129, 0.1) 100%);
            color: #1e293b;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow:
                0 10px 25px rgba(59, 130, 246, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 255, 255, 0.1), 
                transparent);
            animation: shine 3s ease-in-out infinite;
        }
        
        @keyframes shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }
        
        /* بطاقات الكليات */
        .colleges-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .college-card {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        .college-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #10b981, #8b5cf6);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .college-card:hover::before {
            transform: scaleX(1);
        }

        .college-card:hover {
            transform: translateY(-5px);
            box-shadow:
                0 15px 35px rgba(59, 130, 246, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 1);
            border-color: rgba(59, 130, 246, 0.3);
        }
        
        .college-icon {
            width: 80px;
            height: 80px;
            border-radius: 18px;
            background: linear-gradient(135deg, #3b82f6, #10b981);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.25);
        }

        .stats-badge {
            background: linear-gradient(135deg, #3b82f6, #10b981);
            color: white;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 600;
            font-size: 0.85rem;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
        }

        .action-btn {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #3b82f6;
            border-radius: 12px;
            padding: 0.6rem 1.2rem;
            transition: all 0.3s ease;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
        }
        
        .action-btn:hover {
            background: linear-gradient(135deg, #3b82f6, #10b981);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
        }
        
        .filter-tabs {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 16px;
            padding: 1rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }
        
        .filter-tab {
            background: transparent;
            border: 1px solid rgba(245, 158, 11, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: linear-gradient(135deg, #f59e0b, #dc2626);
            color: white;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }
        
        .filter-tab:hover {
            background: rgba(245, 158, 11, 0.2);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="page-header">
                <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 800;">
                    <i class="bi bi-bank me-3"></i>
                    إدارة الكليات الأكاديمية
                </h1>
                <p class="mb-0 mt-3" style="font-size: 1.2rem; opacity: 0.9;">
                    إدارة شاملة للكليات والمؤسسات التعليمية
                </p>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: #ffffff; font-weight: 700;">
                        <i class="bi bi-building me-2" style="color: #f59e0b;"></i>
                        الكليات الأكاديمية
                    </h2>
                    <p class="text-muted mb-0">{{ colleges|length }} كلية مسجلة في النظام</p>
                </div>
                <div class="d-flex gap-3">
                    <button class="action-btn" onclick="showAllColleges()">
                        <i class="bi bi-grid me-2"></i>عرض الكل
                    </button>
                    <a href="{{ url_for('add_college') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة كلية جديدة
                    </a>
                </div>
            </div>

            <!-- فلاتر الكليات -->
            <div class="filter-tabs">
                <div class="d-flex flex-wrap align-items-center">
                    <span class="me-3 text-white fw-bold">تصفية حسب الجامعة:</span>
                    <button class="filter-tab active" onclick="filterByUniversity('all')">جميع الجامعات</button>
                    {% set universities = colleges|map(attribute='university')|unique|list %}
                    {% for university in universities %}
                        {% if university %}
                            <button class="filter-tab" onclick="filterByUniversity('{{ university.id }}')">{{ university.name_ar }}</button>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>

            {% if colleges %}
                <!-- تجميع الكليات حسب الجامعة -->
                {% set colleges_by_university = {} %}
                {% for college in colleges %}
                    {% set university_name = college.university.name_ar if college.university else 'غير محدد' %}
                    {% if university_name not in colleges_by_university %}
                        {% set _ = colleges_by_university.update({university_name: []}) %}
                    {% endif %}
                    {% set _ = colleges_by_university[university_name].append(college) %}
                {% endfor %}

                {% for university_name, university_colleges in colleges_by_university.items() %}
                    <div class="university-section mb-5" data-university="{{ university_colleges[0].university.id if university_colleges[0].university else 'none' }}">
                        <h3 class="mb-4" style="color: #3b82f6; font-weight: 700; border-bottom: 2px solid rgba(59, 130, 246, 0.3); padding-bottom: 0.5rem; font-family: 'Cairo', sans-serif;">
                            <i class="bi bi-building me-2"></i>{{ university_name }}
                            <span class="badge bg-primary ms-2">{{ university_colleges|length }} كلية</span>
                        </h3>
                        
                        <div class="colleges-grid">
                            {% for college in university_colleges %}
                                <div class="college-card" data-university="{{ college.university.id if college.university else 'none' }}">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="college-icon">
                                                {{ college.name_ar[0] if college.name_ar else 'ك' }}
                                            </div>
                                            <div class="ms-3">
                                                <h5 class="mb-1 fw-bold" style="color: #1e293b; font-family: 'Cairo', sans-serif;">{{ college.name_ar }}</h5>
                                                <small class="text-muted">{{ college.name_en or 'لا يوجد اسم إنجليزي' }}</small>
                                            </div>
                                        </div>
                                        <div class="dropdown">
                                            <button class="action-btn btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url_for('edit_college', college_id=college.id) }}">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('departments') }}?college_id={{ college.id }}">
                                                    <i class="bi bi-diagram-3 me-2"></i>عرض الأقسام
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeleteCollege('{{ college.name_ar }}', {{ college.id }})">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    {% if college.address %}
                                        <div class="mb-3">
                                            <small class="text-muted">العنوان:</small>
                                            <div class="text-white">{{ college.address }}</div>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex gap-2">
                                            {% set departments_count = college.departments|length if college.departments else 0 %}
                                            {% if departments_count > 0 %}
                                                <span class="stats-badge">{{ departments_count }} قسم</span>
                                            {% else %}
                                                <span class="badge bg-secondary rounded-pill">لا توجد أقسام</span>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar3 me-1"></i>
                                            {{ college.created_at.strftime('%Y-%m-%d') if college.created_at else 'غير محدد' }}
                                        </small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <div class="college-icon mx-auto mb-4" style="width: 100px; height: 100px; font-size: 3rem; opacity: 0.5;">
                        <i class="bi bi-bank"></i>
                    </div>
                    <h3 class="mb-3" style="color: #1e293b; font-family: 'Cairo', sans-serif;">لا توجد كليات مسجلة</h3>
                    <p class="text-muted mb-4">لم يتم تسجيل أي كليات في النظام بعد</p>
                    <a href="{{ url_for('add_college') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة كلية جديدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        // فلترة الكليات حسب الجامعة
        function filterByUniversity(universityId) {
            const sections = document.querySelectorAll('.university-section');
            const tabs = document.querySelectorAll('.filter-tab');
            
            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // إضافة الفئة النشطة للتبويب المحدد
            event.target.classList.add('active');
            
            sections.forEach(section => {
                if (universityId === 'all' || section.dataset.university === universityId) {
                    section.style.display = 'block';
                    section.style.animation = 'slideInUp 0.5s ease-out';
                } else {
                    section.style.display = 'none';
                }
            });
        }

        // فلترة عامة للكليات
        function showAllColleges() {
            const cards = document.querySelectorAll('.college-card');
            const tabs = document.querySelectorAll('.filter-tab');
            
            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));
            tabs[0].classList.add('active'); // تفعيل تبويب "جميع الجامعات"
            
            cards.forEach((card, index) => {
                card.style.display = 'block';
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        }

        // تأكيد حذف الكلية
        function confirmDeleteCollege(collegeName, collegeId) {
            if (confirm(`هل أنت متأكد من حذف كلية "${collegeName}"؟\nسيتم حذف جميع البيانات المرتبطة بها.`)) {
                fetch(`/admin/colleges/delete/${collegeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }

        // تحسين الرسوم المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.college-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        });

        // رسوم متحركة
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-load {
                animation: slideInUp 0.6s ease-out;
                animation-fill-mode: both;
            }
            
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
