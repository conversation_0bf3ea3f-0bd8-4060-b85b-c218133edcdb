<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الكليات - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-bank me-2"></i>إدارة الكليات
                    </h1>
                    <a href="{{ url_for('add_college') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة كلية جديدة
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-list-ul me-2"></i>قائمة الكليات
                            <span class="badge bg-primary ms-2">{{ colleges|length }}</span>
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if colleges %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="25%">اسم الكلية</th>
                                        <th width="25%">الاسم بالإنجليزية</th>
                                        <th width="20%">الجامعة</th>
                                        <th width="15%">العنوان</th>
                                        <th width="10%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for college in colleges %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-success text-white rounded-circle">
                                                        {{ college.name_ar[0] }}
                                                    </div>
                                                </div>
                                                <strong>{{ college.name_ar }}</strong>
                                            </div>
                                        </td>
                                        <td>{{ college.name_en or 'غير محدد' }}</td>
                                        <td>
                                            <div>
                                                <strong>{{ college.university.name_ar if college.university else 'غير محدد' }}</strong>
                                                {% if college.university and college.university.name_en %}
                                                <br><small class="text-muted">{{ college.university.name_en }}</small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>{{ college.address or 'غير محدد' }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#collegeModal{{ college.id }}"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <a href="{{ url_for('edit_college', id=college.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   title="تعديل">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        title="حذف"
                                                        onclick="confirmDelete('{{ college.name_ar }}', {{ college.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Modal لعرض تفاصيل الكلية -->
                                    <div class="modal fade" id="collegeModal{{ college.id }}" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تفاصيل الكلية: {{ college.name_ar }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">الاسم بالعربية:</td>
                                                                    <td>{{ college.name_ar }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">الاسم بالإنجليزية:</td>
                                                                    <td>{{ college.name_en or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">العنوان:</td>
                                                                    <td>{{ college.address or 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">الجامعة:</td>
                                                                    <td>{{ college.university.name_ar if college.university else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الأقسام:</td>
                                                                    <td>{{ college.departments|length }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    
                                                    {% if college.departments %}
                                                    <div class="row mt-3">
                                                        <div class="col-12">
                                                            <h6>الأقسام التابعة:</h6>
                                                            <div class="row">
                                                                {% for department in college.departments %}
                                                                <div class="col-md-6 mb-2">
                                                                    <div class="card border-primary">
                                                                        <div class="card-body p-2">
                                                                            <h6 class="card-title mb-1">{{ department.name }}</h6>
                                                                            {% if department.head_name %}
                                                                            <small class="text-muted">رئيس القسم: {{ department.head_name }}</small>
                                                                            {% endif %}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                {% endfor %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-bank text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا توجد كليات مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة كلية جديدة</p>
                            <a href="{{ url_for('add_college') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إضافة كلية جديدة
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(name, id) {
            if (confirm('هل أنت متأكد من حذف الكلية: ' + name + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                window.location.href = '/admin/colleges/delete/' + id;
            }
        }
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
