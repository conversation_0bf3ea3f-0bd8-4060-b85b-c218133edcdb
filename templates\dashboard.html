<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding-top: 80px;
            position: relative;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.3rem;
            color: white !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            text-shadow: 0 4px 8px rgba(0,0,0,0.4);
        }

        .nav-link {
            color: rgba(255,255,255,0.95) !important;
            font-weight: 600;
            transition: all 0.3s ease;
            padding: 0.8rem 1.2rem !important;
            margin: 0 0.2rem;
            border-radius: 10px;
            position: relative;
            text-decoration: none;
        }

        .nav-link:hover {
            color: white !important;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-link.active {
            color: white !important;
            background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.2) 100%);
            box-shadow: 0 3px 10px rgba(0,0,0,0.15);
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: white;
            border-radius: 2px;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            border-radius: 15px;
            padding: 1rem 0;
            margin-top: 0.5rem;
            background: white;
            backdrop-filter: blur(10px);
            min-width: 280px;
            z-index: 1060;
            position: absolute;
        }

        .dropdown-item {
            padding: 0.8rem 1.5rem;
            transition: all 0.3s ease;
            border-radius: 0;
            font-weight: 500;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #0d6efd;
            transform: translateX(5px);
            padding-right: 2rem;
        }

        .dropdown-header {
            color: #6c757d;
            font-weight: 700;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0.5rem 1.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .dropdown-divider {
            margin: 0.8rem 1.5rem;
            border-color: #e9ecef;
        }

        /* تحسينات إضافية للتبويبات */
        .navbar-nav .nav-item {
            margin: 0 0.3rem;
            position: relative;
        }

        .navbar-nav .dropdown {
            position: static;
        }

        .navbar-nav .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: auto;
        }

        .navbar-toggler {
            border: none;
            padding: 0.5rem;
            border-radius: 10px;
            background: rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }

        .navbar-toggler:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.1);
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
        }

        /* تأثيرات الانتقال للقوائم */
        .dropdown-menu {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* إصلاح ترتيب العناصر */
        .navbar {
            z-index: 1050 !important;
        }

        .dropdown-menu {
            z-index: 1060 !important;
        }

        .main-content {
            z-index: 1 !important;
        }

        .welcome-section {
            z-index: 1 !important;
        }

        .stat-card {
            z-index: 1 !important;
        }
        .navbar {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%) !important;
            backdrop-filter: blur(15px);
            border-bottom: 3px solid rgba(255,255,255,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1050;
            transition: all 0.3s ease;
        }

        .stat-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        }
        .stat-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        .stat-card.students::before {
            background: linear-gradient(90deg, #28a745, #20c997);
        }
        .stat-card.programs::before {
            background: linear-gradient(90deg, #007bff, #6610f2);
        }
        .stat-card.subjects::before {
            background: linear-gradient(90deg, #fd7e14, #e83e8c);
        }
        .stat-card.grades::before {
            background: linear-gradient(90deg, #20c997, #17a2b8);
        }
        .stats-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
        }
        .stats-icon.students { background: linear-gradient(135deg, #28a745, #20c997); }
        .stats-icon.programs { background: linear-gradient(135deg, #007bff, #6610f2); }
        .stats-icon.subjects { background: linear-gradient(135deg, #fd7e14, #e83e8c); }
        .stats-icon.grades { background: linear-gradient(135deg, #20c997, #17a2b8); }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
        }
        .stat-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }
        .welcome-section {
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
            color: #2c3e50;
            border-radius: 20px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            margin-top: 1rem;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            z-index: 1;
        }

        .welcome-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        .section-title {
            color: white;
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 3px solid rgba(255,255,255,0.3);
            display: inline-block;
            font-size: 1.3rem;
        }

        .navbar-toggler {
            border: 2px solid rgba(255,255,255,0.3);
            padding: 0.5rem;
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
        .quick-action-btn {
            border-radius: 15px;
            padding: 1.2rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            font-weight: 500;
            text-decoration: none;
            display: block;
            text-align: center;
        }
        .quick-action-btn:hover {
            transform: scale(1.05);
            text-decoration: none;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        .dashboard-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            background: white;
        }
        .dashboard-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }
        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 2px solid #dee2e6;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        .animate-on-load {
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .icon-large {
            font-size: 3rem;
            opacity: 0.1;
            position: absolute;
            top: 10px;
            right: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>
                نظام إدارة الدراسات العليا
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('dashboard') }}">
                            <i class="bi bi-house-fill me-1"></i>الرئيسية
                        </a>
                    </li>
                    
                    {% if current_user.user_type == 'admin' %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear-fill me-1"></i>الإدارة
                        </a>
                        <ul class="dropdown-menu">
                            <li><h6 class="dropdown-header">المؤسسات التعليمية</h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('universities') }}">
                                <i class="bi bi-building me-2"></i>الجامعات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('colleges') }}">
                                <i class="bi bi-bank me-2"></i>الكليات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('departments') }}">
                                <i class="bi bi-diagram-3 me-2"></i>الأقسام
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">الأشخاص</h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('students') }}">
                                <i class="bi bi-people me-2"></i>الطلبة
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('professors') }}">
                                <i class="bi bi-person-workspace me-2"></i>الأساتذة
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('users') }}">
                                <i class="bi bi-person-gear me-2"></i>المستخدمون
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">الأكاديمي</h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('programs') }}">
                                <i class="bi bi-journal-bookmark me-2"></i>البرامج الأكاديمية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('subjects') }}">
                                <i class="bi bi-book me-2"></i>المواد الدراسية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('grades') }}">
                                <i class="bi bi-clipboard-data me-2"></i>الدرجات
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('student_transcript') }}">
                            <i class="bi bi-file-earmark-text-fill me-1"></i>التقارير
                        </a>
                    </li>
                </ul>
                
                <!-- User Menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>{{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container-fluid mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main Content -->
    <div class="container-fluid" style="padding-top: 2rem; position: relative; z-index: 1;">
        <!-- ترحيب محسن -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="welcome-section animate-on-load">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-5 mb-3 fw-bold">
                                <i class="bi bi-speedometer2 me-3"></i>
                                لوحة التحكم الرئيسية
                            </h1>
                            <p class="lead mb-3">مرحباً بك {{ current_user.full_name }} - نظام شامل ومتطور لإدارة جميع جوانب الدراسات العليا</p>
                            <div class="d-flex gap-3">
                                <span class="badge bg-light text-dark px-3 py-2">
                                    <i class="bi bi-calendar3 me-1"></i>
                                    اليوم
                                </span>
                                <span class="badge bg-light text-dark px-3 py-2">
                                    <i class="bi bi-person-badge me-1"></i>
                                    {{ current_user.user_type|title }}
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="bi bi-graph-up-arrow" style="font-size: 5rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة محسنة -->
        <div class="row mb-4">
            <div class="col-12 mb-3">
                <h3 class="section-title animate-on-load text-white">
                    <i class="bi bi-bar-chart-fill me-2"></i>الإحصائيات العامة
                </h3>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card students animate-on-load">
                    <div class="card-body text-center p-4">
                        <div class="stats-icon students">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stat-number">{{ stats.students or 0 }}</div>
                        <div class="stat-label">الطلبة المسجلين</div>
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="bi bi-arrow-up"></i> +5% من الشهر الماضي
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card programs animate-on-load">
                    <div class="card-body text-center p-4">
                        <div class="stats-icon programs">
                            <i class="bi bi-journal-bookmark"></i>
                        </div>
                        <div class="stat-number">{{ stats.programs or 0 }}</div>
                        <div class="stat-label">البرامج الأكاديمية</div>
                        <div class="mt-2">
                            <small class="text-primary">
                                <i class="bi bi-dash"></i> مستقر
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card subjects animate-on-load">
                    <div class="card-body text-center p-4">
                        <div class="stats-icon subjects">
                            <i class="bi bi-book"></i>
                        </div>
                        <div class="stat-number">{{ stats.subjects or 0 }}</div>
                        <div class="stat-label">المواد الدراسية</div>
                        <div class="mt-2">
                            <small class="text-warning">
                                <i class="bi bi-arrow-up"></i> +2% من الشهر الماضي
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card grades animate-on-load">
                    <div class="card-body text-center p-4">
                        <div class="stats-icon grades">
                            <i class="bi bi-clipboard-data"></i>
                        </div>
                        <div class="stat-number">{{ stats.grades or 0 }}</div>
                        <div class="stat-label">الدرجات المدخلة</div>
                        <div class="mt-2">
                            <small class="text-info">
                                <i class="bi bi-arrow-up"></i> +15% من الشهر الماضي
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card warning animate-on-load">
                    <div class="card-body position-relative">
                        <i class="bi bi-clipboard-data icon-large"></i>
                        <div class="row no-gutters align-items-center">
                            <div class="col">
                                <div class="stat-number">{{ stats.grades }}</div>
                                <div class="stat-label">الدرجات المدخلة</div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-clipboard-data fs-1 text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الروابط السريعة المحسنة -->
        {% if current_user.user_type == 'admin' %}
        <div class="row mb-4">
            <div class="col-12 mb-3">
                <h3 class="section-title animate-on-load">
                    <i class="bi bi-lightning-fill me-2"></i>الإجراءات السريعة
                </h3>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card animate-on-load">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-plus-circle-fill me-2"></i>إضافة جديد
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="{{ url_for('add_student') }}" class="quick-action-btn btn-outline-primary">
                                    <i class="bi bi-person-plus-fill me-2 fs-4"></i>
                                    <div class="fw-bold">إضافة طالب جديد</div>
                                    <small class="text-muted">تسجيل طالب في النظام</small>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="{{ url_for('add_professor') }}" class="quick-action-btn btn-outline-success">
                                    <i class="bi bi-person-workspace me-2 fs-4"></i>
                                    <div class="fw-bold">إضافة أستاذ جديد</div>
                                    <small class="text-muted">إضافة عضو هيئة تدريس</small>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="{{ url_for('add_grades') }}" class="quick-action-btn btn-outline-info">
                                    <i class="bi bi-clipboard-data-fill me-2 fs-4"></i>
                                    <div class="fw-bold">إدخال الدرجات</div>
                                    <small class="text-muted">تسجيل درجات الطلبة</small>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="{{ url_for('student_transcript') }}" class="quick-action-btn btn-outline-warning">
                                    <i class="bi bi-file-earmark-text-fill me-2 fs-4"></i>
                                    <div class="fw-bold">كشوف الدرجات</div>
                                    <small class="text-muted">عرض وطباعة الكشوف</small>
                                </a>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="{{ url_for('programs') }}" class="quick-action-btn btn-outline-success">
                                    <i class="bi bi-journal-bookmark me-2 fs-4"></i>
                                    <div class="fw-bold">البرامج الأكاديمية</div>
                                    <small class="text-muted">إدارة برامج الدراسات العليا</small>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="{{ url_for('subjects') }}" class="quick-action-btn btn-outline-warning">
                                    <i class="bi bi-book me-2 fs-4"></i>
                                    <div class="fw-bold">المواد الدراسية</div>
                                    <small class="text-muted">إدارة المناهج والمقررات</small>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="{{ url_for('universities') }}" class="quick-action-btn btn-outline-primary">
                                    <i class="bi bi-building me-2 fs-4"></i>
                                    <div class="fw-bold">إدارة الجامعات</div>
                                    <small class="text-muted">الجامعات والكليات والأقسام</small>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="{{ url_for('professors') }}" class="quick-action-btn btn-outline-info">
                                    <i class="bi bi-people me-2 fs-4"></i>
                                    <div class="fw-bold">إدارة الأساتذة</div>
                                    <small class="text-muted">أعضاء هيئة التدريس</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- أحدث النشاطات -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-clock-history me-2"></i>أحدث الطلبة المسجلين
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if recent_students %}
                            <div class="list-group list-group-flush">
                                {% for student in recent_students %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ student.name }}</strong><br>
                                        <small class="text-muted">{{ student.program.name if student.program else 'غير محدد' }}</small>
                                    </div>
                                    <small class="text-muted">{{ student.created_at.strftime('%Y-%m-%d') }}</small>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted text-center">لا توجد بيانات</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="bi bi-graph-up me-2"></i>أحدث الدرجات المدخلة
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if recent_grades %}
                            <div class="list-group list-group-flush">
                                {% for grade in recent_grades %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ grade.student.name }}</strong><br>
                                        <small class="text-muted">{{ grade.subject.name if grade.subject else 'غير محدد' }}</small>
                                    </div>
                                    <span class="badge bg-{{ 'success' if grade.is_passed else 'danger' }} rounded-pill">
                                        {{ grade.total_grade }}
                                    </span>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted text-center">لا توجد بيانات</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسينات تفاعلية للوحة التحكم
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل المتدرج للعناصر
            const animatedElements = document.querySelectorAll('.animate-on-load');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });

            // تأثير hover للبطاقات الإحصائية
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تأثير النقر للأزرار السريعة
            const quickButtons = document.querySelectorAll('.quick-action-btn');
            quickButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // تأثير الضغط
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1.05)';
                    }, 100);
                });
            });

            // تحديث الوقت الحالي
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleString('ar-EG', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });

                const timeElements = document.querySelectorAll('.current-time');
                timeElements.forEach(element => {
                    element.textContent = timeString;
                });
            }

            // تحديث الوقت كل دقيقة
            updateTime();
            setInterval(updateTime, 60000);

            // تأثير التحميل للإحصائيات
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(number => {
                const finalValue = parseInt(number.textContent);
                let currentValue = 0;
                const increment = finalValue / 50;

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(currentValue);
                }, 30);
            });

            // تفعيل tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        // دالة لإظهار رسالة ترحيب
        function showWelcomeMessage() {
            const toast = document.createElement('div');
            toast.className = 'toast position-fixed top-0 end-0 m-3';
            toast.innerHTML = `
                <div class="toast-header">
                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                    <strong class="me-auto">مرحباً بك</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    تم تحميل لوحة التحكم بنجاح
                </div>
            `;
            document.body.appendChild(toast);

            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }

        // تحسينات تفاعلية للوحة التحكم
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل المتدرج
            const animatedElements = document.querySelectorAll('.animate-on-load');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });

            // تأثيرات hover للبطاقات
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تحديث الوقت كل ثانية
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // تحديث الإحصائيات كل 30 ثانية
            setInterval(updateStats, 30000);
        });

        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            const dateTimeString = now.toLocaleDateString('ar-SA', options);

            const dateTimeBadges = document.querySelectorAll('.badge:contains("اليوم")');
            dateTimeBadges.forEach(badge => {
                if (badge.textContent.includes('اليوم')) {
                    badge.innerHTML = `<i class="bi bi-calendar3 me-1"></i>${dateTimeString}`;
                }
            });
        }

        // تحديث الإحصائيات
        function updateStats() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const currentValue = parseInt(stat.textContent);
                const newValue = currentValue + Math.floor(Math.random() * 3);
                animateNumber(stat, currentValue, newValue);
            });
        }

        // تحريك الأرقام
        function animateNumber(element, start, end) {
            const duration = 1000;
            const startTime = performance.now();

            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                const current = Math.floor(start + (end - start) * progress);
                element.textContent = current;

                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }

            requestAnimationFrame(update);
        }

        // تحسين التفاعل مع التبويبات
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات للتبويبات
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                link.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'translateY(0)';
                    }
                });
            });

            // تحسين القوائم المنسدلة
            const dropdowns = document.querySelectorAll('.dropdown-toggle');
            dropdowns.forEach(dropdown => {
                dropdown.addEventListener('click', function(e) {
                    e.preventDefault();
                    const menu = this.nextElementSibling;
                    if (menu) {
                        menu.classList.toggle('show');
                    }
                });
            });

            // إغلاق القوائم عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                        menu.classList.remove('show');
                    });
                }
            });
        });

        // عرض رسالة الترحيب بعد التحميل
        window.addEventListener('load', () => {
            setTimeout(showWelcomeMessage, 1000);
        });
    </script>
</body>
</html>
