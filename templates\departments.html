<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأقسام - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-diagram-3 me-2"></i>إدارة الأقسام
                    </h1>
                    <a href="{{ url_for('add_department') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة قسم جديد
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-list-ul me-2"></i>قائمة الأقسام
                            <span class="badge bg-primary ms-2">{{ departments|length }}</span>
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if departments %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="25%">اسم القسم</th>
                                        <th width="20%">رئيس القسم</th>
                                        <th width="15%">سكرتير القسم</th>
                                        <th width="20%">الكلية</th>
                                        <th width="15%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for department in departments %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-warning text-dark rounded-circle">
                                                        {{ department.name[0] }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong>{{ department.name }}</strong>
                                                    {% if department.description %}
                                                    <br><small class="text-muted">{{ department.description[:50] }}{% if department.description|length > 50 %}...{% endif %}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ department.head_name or 'غير محدد' }}</td>
                                        <td>{{ department.secretary_name or 'غير محدد' }}</td>
                                        <td>
                                            <div>
                                                <strong>{{ department.college.name_ar if department.college else 'غير محدد' }}</strong>
                                                {% if department.college and department.college.university %}
                                                <br><small class="text-muted">{{ department.college.university.name_ar }}</small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#departmentModal{{ department.id }}"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <a href="{{ url_for('edit_department', id=department.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   title="تعديل">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        title="حذف"
                                                        onclick="confirmDelete('{{ department.name }}', {{ department.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Modal لعرض تفاصيل القسم -->
                                    <div class="modal fade" id="departmentModal{{ department.id }}" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تفاصيل القسم: {{ department.name }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">اسم القسم:</td>
                                                                    <td>{{ department.name }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">رئيس القسم:</td>
                                                                    <td>{{ department.head_name or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">سكرتير القسم:</td>
                                                                    <td>{{ department.secretary_name or 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">الكلية:</td>
                                                                    <td>{{ department.college.name_ar if department.college else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">الجامعة:</td>
                                                                    <td>{{ department.college.university.name_ar if department.college and department.college.university else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الأساتذة:</td>
                                                                    <td>{{ department.professors|length }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    
                                                    {% if department.description %}
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <h6>وصف القسم:</h6>
                                                            <p class="text-muted">{{ department.description }}</p>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                    
                                                    {% if department.professors %}
                                                    <div class="row mt-3">
                                                        <div class="col-12">
                                                            <h6>أساتذة القسم:</h6>
                                                            <div class="row">
                                                                {% for professor in department.professors %}
                                                                <div class="col-md-6 mb-2">
                                                                    <div class="card border-info">
                                                                        <div class="card-body p-2">
                                                                            <h6 class="card-title mb-1">{{ professor.name }}</h6>
                                                                            <small class="text-muted">
                                                                                {{ professor.academic_title or 'غير محدد' }}
                                                                                {% if professor.degree %} - {{ professor.degree }}{% endif %}
                                                                            </small>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                {% endfor %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-diagram-3 text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا توجد أقسام مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة قسم جديد</p>
                            <a href="{{ url_for('add_department') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إضافة قسم جديد
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(name, id) {
            if (confirm('هل أنت متأكد من حذف القسم: ' + name + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                window.location.href = '/admin/departments/delete/' + id;
            }
        }
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
