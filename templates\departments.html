<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأقسام - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            font-family: 'Cairo', 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
            color: #1e293b;
            overflow-x: hidden;
        }

        /* خلفية فاتحة ومريحة للأقسام */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 30% 20%, rgba(139, 92, 246, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(6, 182, 212, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 20% 70%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);
            z-index: -2;
            animation: backgroundShift 60s ease-in-out infinite;
        }
        
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse"><path d="M 8 0 L 0 0 0 8" fill="none" stroke="%23ffffff" stroke-width="0.3" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
            opacity: 0.4;
        }
        
        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            50% { filter: hue-rotate(45deg) brightness(1.1); }
        }
        
        .main-container {
            background: linear-gradient(135deg, 
                rgba(15, 23, 42, 0.9) 0%, 
                rgba(30, 27, 75, 0.9) 100%);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 32px;
            padding: 3rem;
            margin: 2rem auto;
            box-shadow: 
                0 32px 64px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(24px) saturate(180%);
        }
        
        .page-header {
            background: linear-gradient(135deg, 
                rgba(139, 92, 246, 0.9) 0%, 
                rgba(6, 182, 212, 0.9) 100%);
            color: white;
            border-radius: 24px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: 
                0 20px 40px rgba(139, 92, 246, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 255, 255, 0.1), 
                transparent);
            animation: shine 3s ease-in-out infinite;
        }
        
        @keyframes shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }
        
        /* بطاقات الأقسام */
        .department-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .department-card {
            background: linear-gradient(135deg, 
                rgba(15, 23, 42, 0.8) 0%, 
                rgba(30, 27, 75, 0.8) 100%);
            border: 1px solid rgba(139, 92, 246, 0.2);
            border-radius: 24px;
            padding: 2rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        .department-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #8b5cf6, #06b6d4, #10b981);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }
        
        .department-card:hover::before {
            transform: scaleX(1);
        }
        
        .department-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 
                0 25px 50px rgba(139, 92, 246, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            border-color: rgba(139, 92, 246, 0.4);
        }
        
        .department-icon {
            width: 70px;
            height: 70px;
            border-radius: 16px;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
        }
        
        .stats-badge {
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            color: white;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }
        
        .action-btn {
            background: linear-gradient(135deg, 
                rgba(139, 92, 246, 0.1) 0%, 
                rgba(6, 182, 212, 0.1) 100%);
            border: 1px solid rgba(139, 92, 246, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(139, 92, 246, 0.4);
        }
        
        .filter-tabs {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 16px;
            padding: 1rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(139, 92, 246, 0.2);
        }
        
        .filter-tab {
            background: transparent;
            border: 1px solid rgba(139, 92, 246, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            color: white;
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }
        
        .filter-tab:hover {
            background: rgba(139, 92, 246, 0.2);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="page-header">
                <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 800;">
                    <i class="bi bi-diagram-3 me-3"></i>
                    إدارة الأقسام الأكاديمية
                </h1>
                <p class="mb-0 mt-3" style="font-size: 1.2rem; opacity: 0.9;">
                    إدارة شاملة للأقسام والتخصصات الأكاديمية
                </p>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: #ffffff; font-weight: 700;">
                        <i class="bi bi-grid-3x3-gap me-2" style="color: #8b5cf6;"></i>
                        الأقسام الأكاديمية
                    </h2>
                    <p class="text-muted mb-0">{{ departments|length }} قسم مسجل في النظام</p>
                </div>
                <div class="d-flex gap-3">
                    <button class="action-btn" onclick="filterDepartments('all')">
                        <i class="bi bi-grid me-2"></i>عرض الكل
                    </button>
                    <a href="{{ url_for('add_department') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة قسم جديد
                    </a>
                </div>
            </div>

            <!-- فلاتر الأقسام -->
            <div class="filter-tabs">
                <div class="d-flex flex-wrap align-items-center">
                    <span class="me-3 text-white fw-bold">تصفية حسب الكلية:</span>
                    <button class="filter-tab active" onclick="filterByCollege('all')">جميع الكليات</button>
                    {% set colleges = departments|map(attribute='college')|unique|list %}
                    {% for college in colleges %}
                        {% if college %}
                            <button class="filter-tab" onclick="filterByCollege('{{ college.id }}')">{{ college.name_ar }}</button>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>

            {% if departments %}
                <!-- تجميع الأقسام حسب الكلية -->
                {% set departments_by_college = {} %}
                {% for department in departments %}
                    {% set college_name = department.college.name_ar if department.college else 'غير محدد' %}
                    {% if college_name not in departments_by_college %}
                        {% set _ = departments_by_college.update({college_name: []}) %}
                    {% endif %}
                    {% set _ = departments_by_college[college_name].append(department) %}
                {% endfor %}

                {% for college_name, college_departments in departments_by_college.items() %}
                    <div class="college-section mb-5" data-college="{{ college_departments[0].college.id if college_departments[0].college else 'none' }}">
                        <h3 class="mb-4" style="color: #8b5cf6; font-weight: 700; border-bottom: 2px solid rgba(139, 92, 246, 0.3); padding-bottom: 0.5rem;">
                            <i class="bi bi-building me-2"></i>{{ college_name }}
                            <span class="badge bg-primary ms-2">{{ college_departments|length }} قسم</span>
                        </h3>
                        
                        <div class="department-grid">
                            {% for department in college_departments %}
                                <div class="department-card" data-college="{{ department.college.id if department.college else 'none' }}">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="department-icon">
                                                {{ department.name[0] if department.name else 'ق' }}
                                            </div>
                                            <div class="ms-3">
                                                <h5 class="mb-1 fw-bold text-white">{{ department.name }}</h5>
                                                <small class="text-muted">{{ department.college.name_ar if department.college else 'غير محدد' }}</small>
                                            </div>
                                        </div>
                                        <div class="dropdown">
                                            <button class="action-btn btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url_for('edit_department', department_id=department.id) }}">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('professors') }}?department_id={{ department.id }}">
                                                    <i class="bi bi-people me-2"></i>عرض الأساتذة
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeleteDept('{{ department.name }}', {{ department.id }})">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    {% if department.description %}
                                        <div class="mb-3">
                                            <small class="text-muted">الوصف:</small>
                                            <p class="text-white mb-0">{{ department.description }}</p>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="row mb-3">
                                        {% if department.head_name %}
                                            <div class="col-6">
                                                <small class="text-muted">رئيس القسم:</small>
                                                <div class="text-white fw-bold">{{ department.head_name }}</div>
                                            </div>
                                        {% endif %}
                                        {% if department.secretary_name %}
                                            <div class="col-6">
                                                <small class="text-muted">سكرتير القسم:</small>
                                                <div class="text-white fw-bold">{{ department.secretary_name }}</div>
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex gap-2">
                                            {% set professors_count = department.professors|length if department.professors else 0 %}
                                            {% if professors_count > 0 %}
                                                <span class="stats-badge">{{ professors_count }} أستاذ</span>
                                            {% else %}
                                                <span class="badge bg-secondary rounded-pill">لا يوجد أساتذة</span>
                                            {% endif %}
                                            
                                            {% set subjects_count = department.subjects|length if department.subjects else 0 %}
                                            {% if subjects_count > 0 %}
                                                <span class="badge bg-info rounded-pill">{{ subjects_count }} مادة</span>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar3 me-1"></i>
                                            {{ department.created_at.strftime('%Y-%m-%d') if department.created_at else 'غير محدد' }}
                                        </small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <div class="department-icon mx-auto mb-4" style="width: 100px; height: 100px; font-size: 3rem; opacity: 0.5;">
                        <i class="bi bi-diagram-3"></i>
                    </div>
                    <h3 class="text-white mb-3">لا توجد أقسام مسجلة</h3>
                    <p class="text-muted mb-4">لم يتم تسجيل أي أقسام في النظام بعد</p>
                    <a href="{{ url_for('add_department') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة قسم جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        // فلترة الأقسام حسب الكلية
        function filterByCollege(collegeId) {
            const sections = document.querySelectorAll('.college-section');
            const tabs = document.querySelectorAll('.filter-tab');
            
            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // إضافة الفئة النشطة للتبويب المحدد
            event.target.classList.add('active');
            
            sections.forEach(section => {
                if (collegeId === 'all' || section.dataset.college === collegeId) {
                    section.style.display = 'block';
                    section.style.animation = 'slideInUp 0.5s ease-out';
                } else {
                    section.style.display = 'none';
                }
            });
        }

        // فلترة عامة للأقسام
        function filterDepartments(type) {
            const cards = document.querySelectorAll('.department-card');
            
            cards.forEach((card, index) => {
                card.style.display = 'block';
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        }

        // تأكيد حذف القسم
        function confirmDeleteDept(deptName, deptId) {
            if (confirm(`هل أنت متأكد من حذف قسم "${deptName}"؟\nسيتم حذف جميع البيانات المرتبطة به.`)) {
                fetch(`/admin/departments/delete/${deptId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }

        // تحسين الرسوم المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.department-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        });

        // رسوم متحركة
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-load {
                animation: slideInUp 0.6s ease-out;
                animation-fill-mode: both;
            }
            
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
