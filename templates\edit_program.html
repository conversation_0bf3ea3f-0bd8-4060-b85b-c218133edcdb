<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل البرنامج الأكاديمي - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('programs') }}">البرامج الأكاديمية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-pencil-square me-2"></i>تعديل البرنامج الأكاديمي: {{ program.name }}
                    </h1>
                    <a href="{{ url_for('programs') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-journal-bookmark me-2"></i>تعديل بيانات البرنامج الأكاديمي
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <!-- البيانات الأساسية -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="bi bi-info-circle me-2"></i>البيانات الأساسية
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        <i class="bi bi-journal-bookmark me-2"></i>اسم البرنامج <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           value="{{ program.name }}"
                                           placeholder="أدخل اسم البرنامج الأكاديمي">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="program_type" class="form-label">
                                        <i class="bi bi-award me-2"></i>نوع البرنامج <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="program_type" name="program_type" required>
                                        <option value="">اختر نوع البرنامج</option>
                                        <option value="ماجستير" {% if program.program_type == 'ماجستير' %}selected{% endif %}>ماجستير</option>
                                        <option value="دكتوراه" {% if program.program_type == 'دكتوراه' %}selected{% endif %}>دكتوراه</option>
                                        <option value="دبلوم عالي" {% if program.program_type == 'دبلوم عالي' %}selected{% endif %}>دبلوم عالي</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="duration" class="form-label">
                                        <i class="bi bi-clock me-2"></i>مدة البرنامج
                                    </label>
                                    <select class="form-select" id="duration" name="duration">
                                        <option value="">اختر مدة البرنامج</option>
                                        <option value="سنة واحدة" {% if program.duration == 'سنة واحدة' %}selected{% endif %}>سنة واحدة</option>
                                        <option value="سنتان" {% if program.duration == 'سنتان' %}selected{% endif %}>سنتان</option>
                                        <option value="ثلاث سنوات" {% if program.duration == 'ثلاث سنوات' %}selected{% endif %}>ثلاث سنوات</option>
                                        <option value="أربع سنوات" {% if program.duration == 'أربع سنوات' %}selected{% endif %}>أربع سنوات</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="department_id" class="form-label">
                                        <i class="bi bi-diagram-3 me-2"></i>القسم <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="department_id" name="department_id" required>
                                        <option value="">اختر القسم</option>
                                        {% for department in departments %}
                                        <option value="{{ department.id }}" {% if program.department_id == department.id %}selected{% endif %}>
                                            {{ department.name }} - {{ department.college.name_ar if department.college else '' }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <!-- الوصف -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-file-text me-2"></i>وصف البرنامج
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="description" class="form-label">
                                        <i class="bi bi-chat-text me-2"></i>وصف البرنامج
                                    </label>
                                    <textarea class="form-control" id="description" name="description" rows="4"
                                              placeholder="وصف مفصل عن البرنامج الأكاديمي وأهدافه...">{{ program.description or '' }}</textarea>
                                </div>
                            </div>
                            
                            <!-- إحصائيات البرنامج -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-info border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-graph-up me-2"></i>إحصائيات البرنامج
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title text-primary">عدد المواد</h5>
                                            <h3 class="text-success">{{ program.subjects|length }}</h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title text-primary">عدد الطلبة</h5>
                                            <h3 class="text-info">{{ program.students|length }}</h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title text-primary">إجمالي الوحدات</h5>
                                            <h3 class="text-warning">
                                                {% set total_units = program.subjects|sum(attribute='units') %}
                                                {{ total_units }}
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('programs') }}" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>حفظ التعديلات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const programType = document.getElementById('program_type').value;
            const departmentId = document.getElementById('department_id').value;
            
            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم البرنامج');
                document.getElementById('name').focus();
                return false;
            }
            
            if (!programType) {
                e.preventDefault();
                alert('يرجى اختيار نوع البرنامج');
                document.getElementById('program_type').focus();
                return false;
            }
            
            if (!departmentId) {
                e.preventDefault();
                alert('يرجى اختيار القسم');
                document.getElementById('department_id').focus();
                return false;
            }
        });
    </script>
</body>
</html>
