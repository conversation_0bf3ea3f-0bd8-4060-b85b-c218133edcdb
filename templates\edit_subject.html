<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل المادة الدراسية - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('subjects') }}">المواد الدراسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-pencil-square me-2"></i>تعديل المادة الدراسية: {{ subject.name }}
                    </h1>
                    <a href="{{ url_for('subjects') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-book me-2"></i>تعديل بيانات المادة الدراسية
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <!-- البيانات الأساسية -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="bi bi-info-circle me-2"></i>البيانات الأساسية
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        <i class="bi bi-book me-2"></i>اسم المادة <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           value="{{ subject.name }}"
                                           placeholder="أدخل اسم المادة الدراسية">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="program_id" class="form-label">
                                        <i class="bi bi-journal-bookmark me-2"></i>البرنامج الأكاديمي <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="program_id" name="program_id" required>
                                        <option value="">اختر البرنامج الأكاديمي</option>
                                        {% for program in programs %}
                                        <option value="{{ program.id }}" {% if subject.program_id == program.id %}selected{% endif %}>
                                            {{ program.name }} ({{ program.program_type }})
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <!-- الوحدات والساعات -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-clock me-2"></i>الوحدات والساعات
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="units" class="form-label">
                                        <i class="bi bi-hash me-2"></i>عدد الوحدات <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="units" name="units" required>
                                        <option value="">اختر عدد الوحدات</option>
                                        <option value="1" {% if subject.units == 1 %}selected{% endif %}>وحدة واحدة</option>
                                        <option value="2" {% if subject.units == 2 %}selected{% endif %}>وحدتان</option>
                                        <option value="3" {% if subject.units == 3 %}selected{% endif %}>ثلاث وحدات</option>
                                        <option value="4" {% if subject.units == 4 %}selected{% endif %}>أربع وحدات</option>
                                        <option value="6" {% if subject.units == 6 %}selected{% endif %}>ست وحدات</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="hours" class="form-label">
                                        <i class="bi bi-clock-history me-2"></i>عدد الساعات الأسبوعية
                                    </label>
                                    <input type="number" class="form-control" id="hours" name="hours" 
                                           value="{{ subject.hours }}"
                                           min="1" max="10" placeholder="عدد الساعات الأسبوعية">
                                    <div class="form-text">إذا تُركت فارغة، ستكون مساوية لعدد الوحدات</div>
                                </div>
                            </div>
                            
                            <!-- تصنيف المادة -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-tags me-2"></i>تصنيف المادة
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="subject_type" class="form-label">
                                        <i class="bi bi-bookmark me-2"></i>نوع المادة
                                    </label>
                                    <select class="form-select" id="subject_type" name="subject_type">
                                        <option value="">اختر نوع المادة</option>
                                        <option value="اجباري" {% if subject.subject_type == 'اجباري' %}selected{% endif %}>إجباري</option>
                                        <option value="اختياري" {% if subject.subject_type == 'اختياري' %}selected{% endif %}>اختياري</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="academic_year_type" class="form-label">
                                        <i class="bi bi-calendar3 me-2"></i>السنة الدراسية
                                    </label>
                                    <select class="form-select" id="academic_year_type" name="academic_year_type">
                                        <option value="">اختر السنة الدراسية</option>
                                        <option value="التحضيرية" {% if subject.academic_year_type == 'التحضيرية' %}selected{% endif %}>السنة التحضيرية</option>
                                        <option value="البحثية" {% if subject.academic_year_type == 'البحثية' %}selected{% endif %}>السنة البحثية</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- الوصف -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-file-text me-2"></i>وصف المادة
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="description" class="form-label">
                                        <i class="bi bi-chat-text me-2"></i>وصف المادة
                                    </label>
                                    <textarea class="form-control" id="description" name="description" rows="4"
                                              placeholder="وصف مفصل عن محتوى المادة وأهدافها...">{{ subject.description or '' }}</textarea>
                                </div>
                            </div>
                            
                            <!-- إحصائيات المادة -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-info border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-graph-up me-2"></i>إحصائيات المادة
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title text-primary">عدد الطلبة المسجلين</h5>
                                            <h3 class="text-success">{{ subject.grades|length }}</h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title text-primary">عدد الدرجات المدخلة</h5>
                                            <h3 class="text-info">
                                                {% set entered_grades = subject.grades|selectattr('total_grade')|list|length %}
                                                {{ entered_grades }}
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            {% if subject.grades|length > 0 %}
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> هذه المادة لديها {{ subject.grades|length }} طالب مسجل. تغيير البرنامج قد يؤثر على الدرجات المدخلة.
                            </div>
                            {% endif %}
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('subjects') }}" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>حفظ التعديلات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const units = document.getElementById('units').value;
            const programId = document.getElementById('program_id').value;
            
            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم المادة');
                document.getElementById('name').focus();
                return false;
            }
            
            if (!units) {
                e.preventDefault();
                alert('يرجى اختيار عدد الوحدات');
                document.getElementById('units').focus();
                return false;
            }
            
            if (!programId) {
                e.preventDefault();
                alert('يرجى اختيار البرنامج الأكاديمي');
                document.getElementById('program_id').focus();
                return false;
            }
        });

        // تحديد عدد الساعات تلقائياً حسب الوحدات
        document.getElementById('units').addEventListener('change', function() {
            const hoursInput = document.getElementById('hours');
            const unitsValue = this.value;
            
            if (unitsValue && !hoursInput.value) {
                hoursInput.value = unitsValue;
            }
        });
    </script>
</body>
</html>
