<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الجامعة - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('universities') }}">الجامعات</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-pencil-square me-2"></i>تعديل الجامعة: {{ university.name_ar }}
                    </h1>
                    <a href="{{ url_for('universities') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-building me-2"></i>تعديل بيانات الجامعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name_ar" class="form-label">
                                        <i class="bi bi-building me-2"></i>اسم الجامعة بالعربية <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name_ar" name="name_ar" required 
                                           value="{{ university.name_ar }}"
                                           placeholder="أدخل اسم الجامعة بالعربية">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="name_en" class="form-label">
                                        <i class="bi bi-building me-2"></i>اسم الجامعة بالإنجليزية
                                    </label>
                                    <input type="text" class="form-control" id="name_en" name="name_en"
                                           value="{{ university.name_en or '' }}"
                                           placeholder="University Name in English">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="address" class="form-label">
                                        <i class="bi bi-geo-alt me-2"></i>العنوان
                                    </label>
                                    <input type="text" class="form-control" id="address" name="address"
                                           value="{{ university.address or '' }}"
                                           placeholder="عنوان الجامعة">
                                </div>
                            </div>
                            
                            <!-- إحصائيات الجامعة -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-info border-bottom pb-2 mb-3 mt-4">
                                        <i class="bi bi-graph-up me-2"></i>إحصائيات الجامعة
                                    </h6>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title text-primary">عدد الكليات</h5>
                                            <h3 class="text-success">{{ university.colleges|length }}</h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title text-primary">عدد الأقسام</h5>
                                            <h3 class="text-info">
                                                {% set total_departments = 0 %}
                                                {% for college in university.colleges %}
                                                    {% set total_departments = total_departments + college.departments|length %}
                                                {% endfor %}
                                                {{ total_departments }}
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title text-primary">عدد البرامج</h5>
                                            <h3 class="text-warning">
                                                {% set total_programs = 0 %}
                                                {% for college in university.colleges %}
                                                    {% for department in college.departments %}
                                                        {% set total_programs = total_programs + department.programs|length %}
                                                    {% endfor %}
                                                {% endfor %}
                                                {{ total_programs }}
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            {% if university.colleges|length > 0 %}
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>معلومة:</strong> هذه الجامعة لديها {{ university.colleges|length }} كلية مرتبطة بها.
                            </div>
                            {% endif %}
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('universities') }}" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>حفظ التعديلات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من صحة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const nameAr = document.getElementById('name_ar').value.trim();
            
            if (!nameAr) {
                e.preventDefault();
                alert('يرجى إدخال اسم الجامعة بالعربية');
                document.getElementById('name_ar').focus();
                return false;
            }
        });
    </script>
</body>
</html>
