<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الدرجات - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        .grade-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            background: white;
            margin-bottom: 1.5rem;
            position: relative;
        }
        .grade-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        .grade-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .grade-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        .grade-excellent { background: #d4edda; color: #155724; }
        .grade-very-good { background: #d1ecf1; color: #0c5460; }
        .grade-good { background: #fff3cd; color: #856404; }
        .grade-pass { background: #f8d7da; color: #721c24; }
        .grade-fail { background: #f5c6cb; color: #721c24; }
        .animate-on-load {
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .chart-container {
            position: relative;
            height: 300px;
            background: white;
            border-radius: 15px;
            padding: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- عنوان الصفحة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="page-header animate-on-load">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-6 mb-2 fw-bold">
                                <i class="bi bi-clipboard-data me-3"></i>إدارة الدرجات والنتائج
                            </h1>
                            <p class="lead mb-0">نظام شامل لإدارة وتتبع درجات الطلبة وحساب المعدلات</p>
                            <div class="mt-3">
                                <span class="badge bg-light text-dark me-2">
                                    <i class="bi bi-clipboard-check me-1"></i>{{ grades|length }} درجة مدخلة
                                </span>
                                <span class="badge bg-light text-dark me-2">
                                    <i class="bi bi-people me-1"></i>
                                    {% set unique_students = grades|map(attribute='student')|unique|list|length %}
                                    {{ unique_students }} طالب
                                </span>
                                <span class="badge bg-light text-dark">
                                    <i class="bi bi-book me-1"></i>
                                    {% set unique_subjects = grades|map(attribute='subject')|unique|list|length %}
                                    {{ unique_subjects }} مادة
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('add_grades') }}" class="btn btn-light btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>إدخال درجات جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stats-card animate-on-load">
                    <i class="bi bi-award text-success" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1 text-success">
                        {% set excellent_count = 0 %}
                        {% for grade in grades %}
                            {% if grade.total_grade >= 90 %}
                                {% set excellent_count = excellent_count + 1 %}
                            {% endif %}
                        {% endfor %}
                        {{ excellent_count }}
                    </h4>
                    <p class="text-muted mb-0">ممتاز (90+)</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card animate-on-load">
                    <i class="bi bi-star text-primary" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1 text-primary">
                        {% set very_good_count = 0 %}
                        {% for grade in grades %}
                            {% if grade.total_grade >= 80 and grade.total_grade < 90 %}
                                {% set very_good_count = very_good_count + 1 %}
                            {% endif %}
                        {% endfor %}
                        {{ very_good_count }}
                    </h4>
                    <p class="text-muted mb-0">جيد جداً (80-89)</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card animate-on-load">
                    <i class="bi bi-check-circle text-warning" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1 text-warning">
                        {% set good_count = 0 %}
                        {% for grade in grades %}
                            {% if grade.total_grade >= 70 and grade.total_grade < 80 %}
                                {% set good_count = good_count + 1 %}
                            {% endif %}
                        {% endfor %}
                        {{ good_count }}
                    </h4>
                    <p class="text-muted mb-0">جيد (70-79)</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card animate-on-load">
                    <i class="bi bi-x-circle text-danger" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1 text-danger">
                        {% set fail_count = 0 %}
                        {% for grade in grades %}
                            {% if grade.total_grade < 60 %}
                                {% set fail_count = fail_count + 1 %}
                            {% endif %}
                        {% endfor %}
                        {{ fail_count }}
                    </h4>
                    <p class="text-muted mb-0">راسب (أقل من 60)</p>
                </div>
            </div>
        </div>

        <!-- قسم البحث والفلترة -->
        <div class="row">
            <div class="col-12">
                <div class="filter-section animate-on-load">
                    <div class="row align-items-end g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label fw-bold">
                                <i class="bi bi-search me-2"></i>البحث السريع
                            </label>
                            <input type="text" class="form-control search-box" id="search"
                                   placeholder="البحث بالطالب، المادة، أو الأستاذ...">
                        </div>
                        <div class="col-md-2">
                            <label for="grade_filter" class="form-label fw-bold">
                                <i class="bi bi-funnel me-2"></i>التقدير
                            </label>
                            <select class="form-select" id="grade_filter">
                                <option value="">جميع التقديرات</option>
                                <option value="ممتاز">ممتاز</option>
                                <option value="جيد جداً">جيد جداً</option>
                                <option value="جيد">جيد</option>
                                <option value="مقبول">مقبول</option>
                                <option value="راسب">راسب</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="subject_filter" class="form-label fw-bold">
                                <i class="bi bi-book me-2"></i>المادة
                            </label>
                            <select class="form-select" id="subject_filter">
                                <option value="">جميع المواد</option>
                                {% for subject in grades|map(attribute='subject')|unique %}
                                    {% if subject %}
                                    <option value="{{ subject.name }}">{{ subject.name }}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                                    <i class="bi bi-file-earmark-excel"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="printTable()">
                                    <i class="bi bi-printer"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="showCharts()">
                                    <i class="bi bi-bar-chart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div id="chartsSection" class="row mb-4" style="display: none;">
            <div class="col-md-6 mb-3">
                <div class="chart-container animate-on-load">
                    <h6 class="text-center mb-3">توزيع التقديرات</h6>
                    <canvas id="gradesChart"></canvas>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="chart-container animate-on-load">
                    <h6 class="text-center mb-3">متوسط الدرجات حسب المادة</h6>
                    <canvas id="subjectsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- جدول الدرجات -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow animate-on-load">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-table me-2"></i>قائمة الدرجات المدخلة
                            <span class="badge bg-primary ms-2">{{ grades|length }}</span>
                        </h6>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                                <i class="bi bi-file-earmark-excel me-1"></i>تصدير Excel
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="printTable()">
                                <i class="bi bi-printer me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if grades %}
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="gradesTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%" class="text-center">#</th>
                                        <th width="25%">
                                            <i class="bi bi-person me-2"></i>بيانات الطالب
                                        </th>
                                        <th width="20%">
                                            <i class="bi bi-book me-2"></i>المادة والأستاذ
                                        </th>
                                        <th width="25%" class="text-center">
                                            <i class="bi bi-clipboard-data me-2"></i>الدرجات
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-award me-2"></i>النتيجة النهائية
                                        </th>
                                        <th width="10%" class="text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for grade in grades %}
                                    <tr class="grade-row"
                                        data-student="{{ grade.student.name|lower if grade.student else '' }}"
                                        data-subject="{{ grade.subject.name|lower if grade.subject else '' }}"
                                        data-professor="{{ grade.professor.name|lower if grade.professor else '' }}"
                                        data-grade="{{ grade.grade_letter|lower if grade.grade_letter else '' }}">
                                        <td class="text-center fw-bold text-muted">{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3"
                                                     style="width: 40px; height: 40px;">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1 fw-bold">{{ grade.student.name if grade.student else 'غير محدد' }}</h6>
                                                    <small class="text-muted">
                                                        <i class="bi bi-calendar3 me-1"></i>{{ grade.academic_year.name if grade.academic_year else 'غير محدد' }}
                                                        <i class="bi bi-bookmark ms-2 me-1"></i>{{ grade.semester.name if grade.semester else 'غير محدد' }}
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="mb-2">
                                                <h6 class="mb-1 fw-bold">{{ grade.subject.name if grade.subject else 'غير محدد' }}</h6>
                                                <small class="text-muted">
                                                    <i class="bi bi-hash me-1"></i>{{ grade.subject.units if grade.subject else 0 }} وحدة
                                                </small>
                                            </div>
                                            <div class="text-muted small">
                                                <i class="bi bi-person-workspace me-1"></i>{{ grade.professor.name if grade.professor else 'غير محدد' }}
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="row g-2">
                                                <div class="col-4">
                                                    <div class="bg-light rounded p-2">
                                                        <small class="text-muted d-block">السعي</small>
                                                        <strong class="text-primary">{{ grade.midterm_grade or 0 }}</strong>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="bg-light rounded p-2">
                                                        <small class="text-muted d-block">النهائي</small>
                                                        <strong class="text-success">{{ grade.final_grade or 0 }}</strong>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="bg-primary text-white rounded p-2">
                                                        <small class="d-block">المجموع</small>
                                                        <strong>{{ grade.total_grade or 0 }}</strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            {% set grade_info = {
                                                'ممتاز': {'color': 'success', 'icon': 'award'},
                                                'جيد جداً': {'color': 'primary', 'icon': 'star'},
                                                'جيد': {'color': 'info', 'icon': 'check-circle'},
                                                'مقبول': {'color': 'warning', 'icon': 'check'},
                                                'راسب': {'color': 'danger', 'icon': 'x-circle'}
                                            } %}
                                            {% set current_grade = grade_info.get(grade.grade_letter, {'color': 'secondary', 'icon': 'question'}) %}
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="bi bi-{{ current_grade.icon }} text-{{ current_grade.color }}" style="font-size: 1.5rem;"></i>
                                                <span class="badge bg-{{ current_grade.color }} rounded-pill mt-1">
                                                    {{ grade.grade_letter or 'غير محدد' }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"
                                                        data-bs-toggle="tooltip"
                                                        title="تعديل الدرجة">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف الدرجة"
                                                        onclick="confirmDelete('{{ grade.student.name if grade.student else 'غير محدد' }}', '{{ grade.subject.name if grade.subject else 'غير محدد' }}')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-clipboard-data text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا توجد درجات مدخلة</h4>
                            <p class="text-muted">ابدأ بإدخال درجات جديدة</p>
                            <a href="{{ url_for('add_grades') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إدخال درجات جديدة
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسينات تفاعلية لصفحة الدرجات
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل المتدرج
            const animatedElements = document.querySelectorAll('.animate-on-load');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });

            // تفعيل tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // إعداد الفلاتر
            const searchInput = document.getElementById('search');
            const gradeFilter = document.getElementById('grade_filter');
            const subjectFilter = document.getElementById('subject_filter');

            // ربط أحداث الفلترة
            if (searchInput) searchInput.addEventListener('input', filterGrades);
            if (gradeFilter) gradeFilter.addEventListener('change', filterGrades);
            if (subjectFilter) subjectFilter.addEventListener('change', filterGrades);

            // تأثيرات hover للبطاقات
            const statsCards = document.querySelectorAll('.stats-card');
            statsCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // إعداد الرسوم البيانية
            initializeCharts();
        });

        // دالة الفلترة المحسنة
        function filterGrades() {
            const searchTerm = document.getElementById('search').value.toLowerCase();
            const gradeValue = document.getElementById('grade_filter').value;
            const subjectValue = document.getElementById('subject_filter').value;

            const tableRows = document.querySelectorAll('.grade-row');
            let visibleRows = 0;

            tableRows.forEach(row => {
                const student = row.getAttribute('data-student');
                const subject = row.getAttribute('data-subject');
                const professor = row.getAttribute('data-professor');
                const grade = row.getAttribute('data-grade');

                const matchesSearch = student.includes(searchTerm) ||
                                    subject.includes(searchTerm) ||
                                    professor.includes(searchTerm);
                const matchesGrade = !gradeValue || grade.includes(gradeValue.toLowerCase());
                const matchesSubject = !subjectValue || subject.includes(subjectValue.toLowerCase());

                if (matchesSearch && matchesGrade && matchesSubject) {
                    row.style.display = 'table-row';
                    visibleRows++;
                } else {
                    row.style.display = 'none';
                }
            });

            // تحديث عداد النتائج
            updateResultsCounter(visibleRows);
        }

        // تحديث عداد النتائج
        function updateResultsCounter(count) {
            const badges = document.querySelectorAll('.badge.bg-primary');
            badges.forEach(badge => {
                if (badge.textContent.includes('درجة') || !isNaN(badge.textContent)) {
                    badge.textContent = count;
                }
            });
        }

        // إظهار/إخفاء الرسوم البيانية
        function showCharts() {
            const chartsSection = document.getElementById('chartsSection');
            if (chartsSection.style.display === 'none') {
                chartsSection.style.display = 'flex';
                event.target.classList.add('active');
            } else {
                chartsSection.style.display = 'none';
                event.target.classList.remove('active');
            }
        }

        // إعداد الرسوم البيانية
        function initializeCharts() {
            // رسم بياني لتوزيع التقديرات
            const gradesCtx = document.getElementById('gradesChart');
            if (gradesCtx) {
                new Chart(gradesCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'راسب'],
                        datasets: [{
                            data: [
                                {% set excellent_count = 0 %}
                                {% set very_good_count = 0 %}
                                {% set good_count = 0 %}
                                {% set acceptable_count = 0 %}
                                {% set fail_count = 0 %}
                                {% for grade in grades %}
                                    {% if grade.total_grade >= 90 %}
                                        {% set excellent_count = excellent_count + 1 %}
                                    {% elif grade.total_grade >= 80 %}
                                        {% set very_good_count = very_good_count + 1 %}
                                    {% elif grade.total_grade >= 70 %}
                                        {% set good_count = good_count + 1 %}
                                    {% elif grade.total_grade >= 60 %}
                                        {% set acceptable_count = acceptable_count + 1 %}
                                    {% else %}
                                        {% set fail_count = fail_count + 1 %}
                                    {% endif %}
                                {% endfor %}
                                {{ excellent_count }},
                                {{ very_good_count }},
                                {{ good_count }},
                                {{ acceptable_count }},
                                {{ fail_count }}
                            ],
                            backgroundColor: [
                                '#28a745',
                                '#007bff',
                                '#17a2b8',
                                '#ffc107',
                                '#dc3545'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // رسم بياني لمتوسط الدرجات حسب المادة
            const subjectsCtx = document.getElementById('subjectsChart');
            if (subjectsCtx) {
                const subjectData = {};
                {% for grade in grades %}
                    {% if grade.subject and grade.total_grade %}
                        if (!subjectData['{{ grade.subject.name }}']) {
                            subjectData['{{ grade.subject.name }}'] = [];
                        }
                        subjectData['{{ grade.subject.name }}'].push({{ grade.total_grade }});
                    {% endif %}
                {% endfor %}

                const labels = Object.keys(subjectData);
                const averages = labels.map(subject => {
                    const grades = subjectData[subject];
                    return grades.reduce((a, b) => a + b, 0) / grades.length;
                });

                new Chart(subjectsCtx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'متوسط الدرجات',
                            data: averages,
                            backgroundColor: 'rgba(102, 126, 234, 0.8)',
                            borderColor: 'rgba(102, 126, 234, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
        }

        // دالة تأكيد الحذف
        function confirmDelete(studentName, subjectName) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>تأكيد حذف الدرجة
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <i class="bi bi-clipboard-x text-danger" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">هل أنت متأكد من حذف الدرجة؟</h5>
                            <p class="text-muted">الطالب: ${studentName}</p>
                            <p class="text-muted">المادة: ${subjectName}</p>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-danger">
                                <i class="bi bi-trash me-2"></i>حذف نهائياً
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                modal.remove();
            });
        }

        // دوال التصدير والطباعة
        function exportToExcel() {
            alert('سيتم تنفيذ تصدير Excel قريباً');
        }

        function printTable() {
            window.print();
        }
    </script>
</body>
</html>
