<!-- Navigation -->
<style>
    .modern-navbar {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(59, 130, 246, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 0.3rem 0;
        min-height: 60px;
    }

    .navbar-brand {
        font-weight: 700;
        font-size: 1.4rem;
        color: white !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
        font-family: 'Cairo', sans-serif;
    }

    .navbar-brand:hover {
        transform: scale(1.05);
        color: #60a5fa !important;
    }

    .horizontal-nav {
        display: flex;
        align-items: center;
        gap: 0.3rem;
        flex-wrap: wrap;
        width: 100%;
        justify-content: flex-start;
    }

    .nav-section {
        display: flex;
        align-items: center;
        gap: 0.2rem;
        padding: 0.3rem 0.6rem;
        border-radius: 10px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.12);
        margin: 0.1rem;
        position: relative;
    }

    .nav-section:not(:last-child)::after {
        content: '';
        position: absolute;
        left: -0.15rem;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 60%;
        background: rgba(148, 163, 184, 0.3);
    }

    .nav-section:hover {
        background: rgba(59, 130, 246, 0.18);
        border-color: rgba(59, 130, 246, 0.35);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }

    .nav-section-title {
        font-size: 0.75rem;
        font-weight: 700;
        color: #94a3b8;
        margin-bottom: 0;
        font-family: 'Cairo', sans-serif;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-left: 0.3rem;
        white-space: nowrap;
    }

    .nav-links {
        display: flex;
        gap: 0.1rem;
        flex-wrap: nowrap;
        align-items: center;
    }

    .nav-link-modern {
        color: #e2e8f0 !important;
        text-decoration: none;
        padding: 0.35rem 0.6rem;
        border-radius: 7px;
        font-size: 0.8rem;
        font-weight: 600;
        transition: all 0.25s ease;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-family: 'Cairo', sans-serif;
        white-space: nowrap;
        border: 1px solid transparent;
    }

    .nav-link-modern:hover {
        background: rgba(59, 130, 246, 0.25);
        color: #93c5fd !important;
        transform: translateY(-1px);
        border-color: rgba(59, 130, 246, 0.4);
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
    }

    .nav-link-modern.active {
        background: rgba(59, 130, 246, 0.35);
        color: #93c5fd !important;
        border-color: rgba(59, 130, 246, 0.5);
    }

    .nav-link-modern i {
        font-size: 0.85rem;
    }

    .user-menu {
        margin-right: 1rem;
    }

    .user-dropdown {
        background: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.3);
        border-radius: 12px;
        padding: 0.5rem 1rem;
        color: white !important;
        text-decoration: none;
        transition: all 0.3s ease;
        font-family: 'Cairo', sans-serif;
    }

    .user-dropdown:hover {
        background: rgba(59, 130, 246, 0.2);
        color: #60a5fa !important;
    }

    @media (max-width: 1400px) {
        .nav-section-title {
            display: none;
        }

        .nav-section {
            padding: 0.25rem 0.4rem;
        }

        .nav-link-modern {
            font-size: 0.75rem;
            padding: 0.3rem 0.5rem;
        }
    }

    @media (max-width: 1200px) {
        .horizontal-nav {
            gap: 0.2rem;
        }

        .nav-section {
            margin: 0.05rem;
            padding: 0.2rem 0.3rem;
        }

        .nav-links {
            gap: 0.05rem;
        }

        .nav-link-modern {
            font-size: 0.7rem;
            padding: 0.25rem 0.4rem;
            gap: 0.2rem;
        }

        .nav-link-modern i {
            font-size: 0.8rem;
        }
    }

    @media (max-width: 992px) {
        .horizontal-nav {
            flex-direction: column;
            align-items: stretch;
            gap: 0.3rem;
        }

        .nav-section {
            justify-content: center;
            width: 100%;
        }

        .nav-links {
            justify-content: center;
            flex-wrap: wrap;
        }
    }

    @media (max-width: 768px) {
        .nav-links {
            flex-direction: column;
            gap: 0.1rem;
            width: 100%;
        }

        .nav-link-modern {
            justify-content: center;
            padding: 0.5rem;
            width: 100%;
        }
    }
</style>

<nav class="navbar navbar-expand-lg modern-navbar fixed-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="{{ url_for('dashboard') }}">
            <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
        </a>

        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <div class="horizontal-nav me-auto">
                <!-- الرئيسية -->
                <div class="nav-section">
                    <a class="nav-link-modern {{ 'active' if request.endpoint == 'dashboard' else '' }}" href="{{ url_for('dashboard') }}">
                        <i class="bi bi-house-fill"></i>الرئيسية
                    </a>
                </div>

                {% if current_user.is_authenticated and current_user.user_type == 'admin' %}
                <!-- المؤسسات -->
                <div class="nav-section">
                    <div class="nav-section-title">المؤسسات</div>
                    <div class="nav-links">
                        <a class="nav-link-modern" href="{{ url_for('universities') }}">
                            <i class="bi bi-building"></i>جامعات
                        </a>
                        <a class="nav-link-modern" href="{{ url_for('colleges') }}">
                            <i class="bi bi-bank"></i>كليات
                        </a>
                        <a class="nav-link-modern" href="{{ url_for('departments') }}">
                            <i class="bi bi-diagram-3"></i>أقسام
                        </a>
                        <a class="nav-link-modern" href="{{ url_for('specializations') }}">
                            <i class="bi bi-bookmark"></i>تخصصات
                        </a>
                    </div>
                </div>

                <!-- الأشخاص -->
                <div class="nav-section">
                    <div class="nav-section-title">الأشخاص</div>
                    <div class="nav-links">
                        <a class="nav-link-modern" href="{{ url_for('students') }}">
                            <i class="bi bi-people"></i>طلبة
                        </a>
                        <a class="nav-link-modern" href="{{ url_for('professors') }}">
                            <i class="bi bi-person-workspace"></i>أساتذة
                        </a>
                        <a class="nav-link-modern" href="{{ url_for('users') }}">
                            <i class="bi bi-person-gear"></i>مستخدمون
                        </a>
                    </div>
                </div>

                <!-- الأكاديمي -->
                <div class="nav-section">
                    <div class="nav-section-title">الأكاديمي</div>
                    <div class="nav-links">
                        <a class="nav-link-modern" href="{{ url_for('programs') }}">
                            <i class="bi bi-journal-bookmark"></i>برامج
                        </a>
                        <a class="nav-link-modern" href="{{ url_for('subjects') }}">
                            <i class="bi bi-book"></i>مواد
                        </a>
                        <a class="nav-link-modern" href="{{ url_for('grades') }}">
                            <i class="bi bi-clipboard-data"></i>درجات
                        </a>
                    </div>
                </div>

                <!-- الأعوام والفصول -->
                <div class="nav-section">
                    <div class="nav-section-title">الأعوام</div>
                    <div class="nav-links">
                        <a class="nav-link-modern" href="{{ url_for('academic_years') }}">
                            <i class="bi bi-calendar-range"></i>أعوام
                        </a>
                        <a class="nav-link-modern" href="{{ url_for('semesters') }}">
                            <i class="bi bi-calendar3"></i>فصول
                        </a>
                        <a class="nav-link-modern" href="{{ url_for('admission_channels') }}">
                            <i class="bi bi-door-open"></i>قبول
                        </a>
                    </div>
                </div>
                {% endif %}

                <!-- التقارير -->
                <div class="nav-section">
                    <a class="nav-link-modern" href="{{ url_for('student_transcript') }}">
                        <i class="bi bi-file-earmark-text-fill"></i>التقارير
                    </a>
                </div>
            </div>

            <!-- User Menu -->
            <div class="user-menu">
                <div class="dropdown">
                    <a class="user-dropdown dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>{{ current_user.full_name if current_user.is_authenticated else 'مستخدم' }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        {% if current_user.is_authenticated %}
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                        {% else %}
                        <li><a class="dropdown-item" href="{{ url_for('login') }}">
                            <i class="bi bi-box-arrow-in-right me-2"></i>تسجيل الدخول
                        </a></li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>
