{% extends "base/layout.html" %}

{% block title %}لوحة التحكم - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
            </h1>
            <div class="text-muted">
                مرحباً {{ current_user.full_name }}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">الجامعات</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.universities }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-building fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">الكليات</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.colleges }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-buildings fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">الطلبة</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.students }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">الأساتذة</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.professors }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-workspace fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-secondary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">الأقسام</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.departments }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-diagram-3 fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-dark shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">البرامج</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.programs }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-mortarboard fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">المواد</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.subjects }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-book fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">الدرجات</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.grades }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clipboard-data fs-2 text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الروابط السريعة -->
{% if current_user.user_type == 'admin' %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-lightning-fill me-2"></i>الروابط السريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.students') }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-person-plus-fill me-2"></i>إضافة طالب جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.professors') }}" class="btn btn-outline-success w-100">
                            <i class="bi bi-person-workspace me-2"></i>إضافة أستاذ جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.grades') }}" class="btn btn-outline-info w-100">
                            <i class="bi bi-clipboard-data-fill me-2"></i>إدخال الدرجات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports.student_transcript') }}" class="btn btn-outline-warning w-100">
                            <i class="bi bi-file-earmark-text-fill me-2"></i>كشوف الدرجات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- أحدث النشاطات -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clock-history me-2"></i>أحدث الطلبة المسجلين
                </h6>
            </div>
            <div class="card-body">
                {% if recent_students %}
                    <div class="list-group list-group-flush">
                        {% for student in recent_students %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ student.name }}</strong><br>
                                <small class="text-muted">{{ student.program.name }}</small>
                            </div>
                            <small class="text-muted">{{ student.created_at.strftime('%Y-%m-%d') }}</small>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="bi bi-graph-up me-2"></i>أحدث الدرجات المدخلة
                </h6>
            </div>
            <div class="card-body">
                {% if recent_grades %}
                    <div class="list-group list-group-flush">
                        {% for grade in recent_grades %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ grade.student.name }}</strong><br>
                                <small class="text-muted">{{ grade.subject.name }}</small>
                            </div>
                            <span class="badge bg-{{ 'success' if grade.is_passed else 'danger' }} rounded-pill">
                                {{ grade.total_grade }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-right-primary {
    border-right: 0.25rem solid #4e73df !important;
}
.border-right-success {
    border-right: 0.25rem solid #1cc88a !important;
}
.border-right-info {
    border-right: 0.25rem solid #36b9cc !important;
}
.border-right-warning {
    border-right: 0.25rem solid #f6c23e !important;
}
.border-right-secondary {
    border-right: 0.25rem solid #858796 !important;
}
.border-right-dark {
    border-right: 0.25rem solid #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
</style>
{% endblock %}
