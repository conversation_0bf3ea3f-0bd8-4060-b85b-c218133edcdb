<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأساتذة - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: #0a0a0a;
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* خلفية ديناميكية للأساتذة */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 30%, #10b981 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, #3b82f6 0%, transparent 50%),
                radial-gradient(circle at 50% 20%, #f59e0b 0%, transparent 50%),
                linear-gradient(135deg, #1e1b4b 0%, #0f172a 100%);
            z-index: -2;
            animation: backgroundShift 30s ease-in-out infinite;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse"><path d="M 8 0 L 0 0 0 8" fill="none" stroke="%23ffffff" stroke-width="0.3" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
            opacity: 0.4;
        }

        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            50% { filter: hue-rotate(60deg) brightness(1.1); }
        }

        .main-container {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.9) 0%,
                rgba(30, 27, 75, 0.9) 100%);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 32px;
            padding: 3rem;
            margin: 2rem auto;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(24px) saturate(180%);
        }

        .page-header {
            background: linear-gradient(135deg,
                rgba(16, 185, 129, 0.9) 0%,
                rgba(59, 130, 246, 0.9) 100%);
            color: white;
            border-radius: 24px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow:
                0 20px 40px rgba(16, 185, 129, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.1),
                transparent);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }

        /* بطاقات الأساتذة */
        .professors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .professor-card {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.8) 0%,
                rgba(30, 27, 75, 0.8) 100%);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 24px;
            padding: 2rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .professor-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #3b82f6, #f59e0b);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .professor-card:hover::before {
            transform: scaleX(1);
        }

        .professor-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(16, 185, 129, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            border-color: rgba(16, 185, 129, 0.4);
        }

        .professor-avatar {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            background: linear-gradient(135deg, #10b981, #3b82f6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
            position: relative;
        }

        .professor-avatar::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 24px;
            background: linear-gradient(135deg, #10b981, #3b82f6, #f59e0b);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .professor-card:hover .professor-avatar::after {
            opacity: 0.6;
        }

        .rank-badge {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            color: white;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 600;
            font-size: 0.85rem;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .action-btn {
            background: linear-gradient(135deg,
                rgba(16, 185, 129, 0.1) 0%,
                rgba(59, 130, 246, 0.1) 100%);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
        }

        .filter-tabs {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 16px;
            padding: 1rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .filter-tab {
            background: transparent;
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .filter-tab:hover {
            background: rgba(16, 185, 129, 0.2);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

            <div class="page-header">
                <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 800;">
                    <i class="bi bi-person-workspace me-3"></i>
                    إدارة الأساتذة
                </h1>
                <p class="mb-0 mt-3" style="font-size: 1.2rem; opacity: 0.9;">
                    إدارة شاملة لأعضاء هيئة التدريس والأساتذة
                </p>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: #ffffff; font-weight: 700;">
                        <i class="bi bi-people me-2" style="color: #10b981;"></i>
                        أعضاء هيئة التدريس
                    </h2>
                    <p class="text-muted mb-0">{{ professors|length }} أستاذ مسجل في النظام</p>
                </div>
                <div class="d-flex gap-3">
                    <button class="action-btn" onclick="showAllProfessors()">
                        <i class="bi bi-grid me-2"></i>عرض الكل
                    </button>
                    <a href="{{ url_for('add_professor') }}" class="action-btn">
                        <i class="bi bi-person-plus me-2"></i>إضافة أستاذ جديد
                    </a>
                </div>
            </div>

            <!-- فلاتر الأساتذة -->
            <div class="filter-tabs">
                <div class="d-flex flex-wrap align-items-center">
                    <span class="me-3 text-white fw-bold">تصفية حسب اللقب العلمي:</span>
                    <button class="filter-tab active" onclick="filterByTitle('all')">جميع الألقاب</button>
                    <button class="filter-tab" onclick="filterByTitle('أستاذ')">أستاذ</button>
                    <button class="filter-tab" onclick="filterByTitle('أستاذ مشارك')">أستاذ مشارك</button>
                    <button class="filter-tab" onclick="filterByTitle('أستاذ مساعد')">أستاذ مساعد</button>
                    <button class="filter-tab" onclick="filterByTitle('مدرس')">مدرس</button>
                </div>
            </div>

            {% if professors %}
                <!-- تجميع الأساتذة حسب القسم -->
                {% set professors_by_department = {} %}
                {% for professor in professors %}
                    {% set dept_name = professor.department.name if professor.department else 'غير محدد' %}
                    {% if dept_name not in professors_by_department %}
                        {% set _ = professors_by_department.update({dept_name: []}) %}
                    {% endif %}
                    {% set _ = professors_by_department[dept_name].append(professor) %}
                {% endfor %}

                {% for dept_name, dept_professors in professors_by_department.items() %}
                    <div class="department-section mb-5" data-department="{{ dept_professors[0].department.id if dept_professors[0].department else 'none' }}">
                        <h3 class="mb-4" style="color: #10b981; font-weight: 700; border-bottom: 2px solid rgba(16, 185, 129, 0.3); padding-bottom: 0.5rem;">
                            <i class="bi bi-diagram-3 me-2"></i>{{ dept_name }}
                            <span class="badge bg-primary ms-2">{{ dept_professors|length }} أستاذ</span>
                        </h3>

                        <div class="professors-grid">
                            {% for professor in dept_professors %}
                                <div class="professor-card" data-title="{{ professor.academic_title or '' }}" data-degree="{{ professor.degree or '' }}">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="professor-avatar">
                                                {{ professor.name.split()[0][0] if professor.name else 'أ' }}
                                            </div>
                                            <div class="ms-3">
                                                <h5 class="mb-1 fw-bold text-white">{{ professor.name }}</h5>
                                                <small class="text-muted">{{ professor.email or 'لا يوجد بريد إلكتروني' }}</small>
                                            </div>
                                        </div>
                                        <div class="dropdown">
                                            <button class="action-btn btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url_for('edit_professor', professor_id=professor.id) }}">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('subjects') }}?professor_id={{ professor.id }}">
                                                    <i class="bi bi-book me-2"></i>عرض المواد
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeleteProf('{{ professor.name }}', {{ professor.id }})">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">اللقب العلمي:</small>
                                            <div class="rank-badge mt-1">{{ professor.academic_title or 'غير محدد' }}</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">الدرجة العلمية:</small>
                                            <div class="text-white fw-bold mt-1">{{ professor.degree or 'غير محدد' }}</div>
                                        </div>
                                    </div>

                                    {% if professor.specialization %}
                                        <div class="mb-3">
                                            <small class="text-muted">التخصص:</small>
                                            <div class="text-white">{{ professor.specialization }}</div>
                                        </div>
                                    {% endif %}

                                    {% if professor.phone %}
                                        <div class="mb-3">
                                            <small class="text-muted">الهاتف:</small>
                                            <div class="text-white">{{ professor.phone }}</div>
                                        </div>
                                    {% endif %}

                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex gap-2">
                                            {% set subjects_count = professor.subjects|length if professor.subjects else 0 %}
                                            {% if subjects_count > 0 %}
                                                <span class="badge bg-success rounded-pill">{{ subjects_count }} مادة</span>
                                            {% else %}
                                                <span class="badge bg-secondary rounded-pill">لا توجد مواد</span>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar3 me-1"></i>
                                            {{ professor.created_at.strftime('%Y-%m-%d') if professor.created_at else 'غير محدد' }}
                                        </small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <div class="professor-avatar mx-auto mb-4" style="width: 100px; height: 100px; font-size: 3rem; opacity: 0.5;">
                        <i class="bi bi-person-workspace"></i>
                    </div>
                    <h3 class="text-white mb-3">لا يوجد أساتذة مسجلون</h3>
                    <p class="text-muted mb-4">لم يتم تسجيل أي أساتذة في النظام بعد</p>
                    <a href="{{ url_for('add_professor') }}" class="action-btn">
                        <i class="bi bi-person-plus me-2"></i>إضافة أستاذ جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        // فلترة الأساتذة حسب اللقب العلمي
        function filterByTitle(title) {
            const cards = document.querySelectorAll('.professor-card');
            const tabs = document.querySelectorAll('.filter-tab');

            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));

            // إضافة الفئة النشطة للتبويب المحدد
            event.target.classList.add('active');

            cards.forEach(card => {
                if (title === 'all' || card.dataset.title === title) {
                    card.style.display = 'block';
                    card.style.animation = 'slideInUp 0.5s ease-out';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // عرض جميع الأساتذة
        function showAllProfessors() {
            const cards = document.querySelectorAll('.professor-card');
            const tabs = document.querySelectorAll('.filter-tab');

            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));
            tabs[0].classList.add('active'); // تفعيل تبويب "جميع الألقاب"

            cards.forEach((card, index) => {
                card.style.display = 'block';
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        }

        // تأكيد حذف الأستاذ
        function confirmDeleteProf(profName, profId) {
            if (confirm(`هل أنت متأكد من حذف الأستاذ "${profName}"؟\nسيتم حذف جميع البيانات المرتبطة به.`)) {
                fetch(`/admin/professors/delete/${profId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }

        // تحسين الرسوم المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.professor-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        });

        // رسوم متحركة
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-load {
                animation: slideInUp 0.6s ease-out;
                animation-fill-mode: both;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
                                <tbody>
                                    {% for professor in professors %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-info text-white rounded-circle">
                                                        {{ professor.name[0] }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong>{{ professor.name }}</strong><br>
                                                    {% if professor.email %}
                                                    <small class="text-muted">
                                                        <i class="bi bi-envelope me-1"></i>{{ professor.email }}
                                                    </small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if professor.academic_title %}
                                            <span class="badge bg-success">{{ professor.academic_title }}</span>
                                            {% if professor.title_date %}
                                            <br><small class="text-muted">{{ professor.title_date.strftime('%Y-%m-%d') }}</small>
                                            {% endif %}
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if professor.degree %}
                                            <span class="badge bg-primary">{{ professor.degree }}</span>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ professor.general_specialization or 'غير محدد' }}</td>
                                        <td>
                                            <div>
                                                <strong>{{ professor.department.name if professor.department else 'غير محدد' }}</strong>
                                                {% if professor.department and professor.department.college %}
                                                <br><small class="text-muted">{{ professor.department.college.name_ar }}</small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#professorModal{{ professor.id }}"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <a href="{{ url_for('edit_professor', id=professor.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   data-bs-toggle="tooltip"
                                                   title="تعديل">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف"
                                                        onclick="confirmDelete('{{ professor.name }}', {{ professor.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Modal لعرض تفاصيل الأستاذ -->
                                    <div class="modal fade" id="professorModal{{ professor.id }}" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تفاصيل الأستاذ: {{ professor.name }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">الاسم:</td>
                                                                    <td>{{ professor.name }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">اللقب العلمي:</td>
                                                                    <td>{{ professor.academic_title or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">الدرجة العلمية:</td>
                                                                    <td>{{ professor.degree or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">التخصص العام:</td>
                                                                    <td>{{ professor.general_specialization or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">التخصص الدقيق:</td>
                                                                    <td>{{ professor.specific_specialization or 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">القسم:</td>
                                                                    <td>{{ professor.department.name if professor.department else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">الهاتف:</td>
                                                                    <td>{{ professor.phone or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">البريد الإلكتروني:</td>
                                                                    <td>{{ professor.email or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">تاريخ الحصول على اللقب:</td>
                                                                    <td>{{ professor.title_date.strftime('%Y-%m-%d') if professor.title_date else 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    {% if professor.notes %}
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <h6>ملاحظات:</h6>
                                                            <p class="text-muted">{{ professor.notes }}</p>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تفعيل الفلاتر
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');
            const titleFilter = document.getElementById('title_filter');
            const degreeFilter = document.getElementById('degree_filter');
            const table = document.getElementById('professorsTable');
            
            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const titleValue = titleFilter.value;
                const degreeValue = degreeFilter.value;
                
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const name = row.cells[1].textContent.toLowerCase();
                    const title = row.cells[2].textContent.trim();
                    const degree = row.cells[3].textContent.trim();
                    
                    const matchesSearch = name.includes(searchTerm);
                    const matchesTitle = !titleValue || title.includes(titleValue);
                    const matchesDegree = !degreeValue || degree.includes(degreeValue);
                    
                    if (matchesSearch && matchesTitle && matchesDegree) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            searchInput.addEventListener('keyup', filterTable);
            titleFilter.addEventListener('change', filterTable);
            degreeFilter.addEventListener('change', filterTable);
        });

        function confirmDelete(name, id) {
            if (confirm('هل أنت متأكد من حذف الأستاذ: ' + name + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                window.location.href = '/admin/professors/delete/' + id;
            }
        }
        
        // تفعيل tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
