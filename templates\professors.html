<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأساتذة - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-workspace me-2"></i>إدارة الأساتذة
                    </h1>
                    <a href="{{ url_for('add_professor') }}" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>إضافة أستاذ جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" placeholder="البحث في الأساتذة...">
                            </div>
                            <div class="col-md-4">
                                <label for="title_filter" class="form-label">اللقب العلمي</label>
                                <select class="form-select" id="title_filter">
                                    <option value="">جميع الألقاب</option>
                                    <option value="أستاذ">أستاذ</option>
                                    <option value="أستاذ مساعد">أستاذ مساعد</option>
                                    <option value="أستاذ مشارك">أستاذ مشارك</option>
                                    <option value="مدرس">مدرس</option>
                                    <option value="مدرس مساعد">مدرس مساعد</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="degree_filter" class="form-label">الدرجة العلمية</label>
                                <select class="form-select" id="degree_filter">
                                    <option value="">جميع الدرجات</option>
                                    <option value="دكتوراه">دكتوراه</option>
                                    <option value="ماجستير">ماجستير</option>
                                    <option value="بكالوريوس">بكالوريوس</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-list-ul me-2"></i>قائمة الأساتذة
                            <span class="badge bg-primary ms-2">{{ professors|length }}</span>
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if professors %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="professorsTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="20%">اسم الأستاذ</th>
                                        <th width="15%">اللقب العلمي</th>
                                        <th width="10%">الدرجة</th>
                                        <th width="20%">التخصص العام</th>
                                        <th width="15%">القسم</th>
                                        <th width="15%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for professor in professors %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-info text-white rounded-circle">
                                                        {{ professor.name[0] }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong>{{ professor.name }}</strong><br>
                                                    {% if professor.email %}
                                                    <small class="text-muted">
                                                        <i class="bi bi-envelope me-1"></i>{{ professor.email }}
                                                    </small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if professor.academic_title %}
                                            <span class="badge bg-success">{{ professor.academic_title }}</span>
                                            {% if professor.title_date %}
                                            <br><small class="text-muted">{{ professor.title_date.strftime('%Y-%m-%d') }}</small>
                                            {% endif %}
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if professor.degree %}
                                            <span class="badge bg-primary">{{ professor.degree }}</span>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ professor.general_specialization or 'غير محدد' }}</td>
                                        <td>
                                            <div>
                                                <strong>{{ professor.department.name if professor.department else 'غير محدد' }}</strong>
                                                {% if professor.department and professor.department.college %}
                                                <br><small class="text-muted">{{ professor.department.college.name_ar }}</small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#professorModal{{ professor.id }}"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <a href="{{ url_for('edit_professor', id=professor.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   data-bs-toggle="tooltip"
                                                   title="تعديل">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف"
                                                        onclick="confirmDelete('{{ professor.name }}', {{ professor.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Modal لعرض تفاصيل الأستاذ -->
                                    <div class="modal fade" id="professorModal{{ professor.id }}" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تفاصيل الأستاذ: {{ professor.name }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">الاسم:</td>
                                                                    <td>{{ professor.name }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">اللقب العلمي:</td>
                                                                    <td>{{ professor.academic_title or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">الدرجة العلمية:</td>
                                                                    <td>{{ professor.degree or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">التخصص العام:</td>
                                                                    <td>{{ professor.general_specialization or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">التخصص الدقيق:</td>
                                                                    <td>{{ professor.specific_specialization or 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">القسم:</td>
                                                                    <td>{{ professor.department.name if professor.department else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">الهاتف:</td>
                                                                    <td>{{ professor.phone or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">البريد الإلكتروني:</td>
                                                                    <td>{{ professor.email or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">تاريخ الحصول على اللقب:</td>
                                                                    <td>{{ professor.title_date.strftime('%Y-%m-%d') if professor.title_date else 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    {% if professor.notes %}
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <h6>ملاحظات:</h6>
                                                            <p class="text-muted">{{ professor.notes }}</p>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-person-workspace text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا يوجد أساتذة مسجلون</h4>
                            <p class="text-muted">ابدأ بإضافة أستاذ جديد</p>
                            <a href="{{ url_for('add_professor') }}" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>إضافة أستاذ جديد
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تفعيل الفلاتر
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');
            const titleFilter = document.getElementById('title_filter');
            const degreeFilter = document.getElementById('degree_filter');
            const table = document.getElementById('professorsTable');
            
            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const titleValue = titleFilter.value;
                const degreeValue = degreeFilter.value;
                
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const name = row.cells[1].textContent.toLowerCase();
                    const title = row.cells[2].textContent.trim();
                    const degree = row.cells[3].textContent.trim();
                    
                    const matchesSearch = name.includes(searchTerm);
                    const matchesTitle = !titleValue || title.includes(titleValue);
                    const matchesDegree = !degreeValue || degree.includes(degreeValue);
                    
                    if (matchesSearch && matchesTitle && matchesDegree) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            searchInput.addEventListener('keyup', filterTable);
            titleFilter.addEventListener('change', filterTable);
            degreeFilter.addEventListener('change', filterTable);
        });

        function confirmDelete(name, id) {
            if (confirm('هل أنت متأكد من حذف الأستاذ: ' + name + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                window.location.href = '/admin/professors/delete/' + id;
            }
        }
        
        // تفعيل tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
