<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البرامج الأكاديمية - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap');
        
        body {
            background: #0a0a0a;
            font-family: 'Cairo', 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        /* خلفية ديناميكية للبرامج الأكاديمية */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 30% 40%, #10b981 0%, transparent 50%),
                radial-gradient(circle at 70% 20%, #3b82f6 0%, transparent 50%),
                radial-gradient(circle at 20% 80%, #f59e0b 0%, transparent 50%),
                radial-gradient(circle at 80% 60%, #8b5cf6 0%, transparent 50%),
                linear-gradient(135deg, #1e1b4b 0%, #0f172a 100%);
            z-index: -2;
            animation: backgroundShift 40s ease-in-out infinite;
        }
        
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="12" height="12" patternUnits="userSpaceOnUse"><path d="M 12 0 L 0 0 0 12" fill="none" stroke="%23ffffff" stroke-width="0.2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
            opacity: 0.3;
        }
        
        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            25% { filter: hue-rotate(90deg) brightness(1.1); }
            50% { filter: hue-rotate(180deg) brightness(0.9); }
            75% { filter: hue-rotate(270deg) brightness(1.1); }
        }
        
        .main-container {
            background: linear-gradient(135deg, 
                rgba(15, 23, 42, 0.95) 0%, 
                rgba(30, 27, 75, 0.95) 100%);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 32px;
            padding: 3rem;
            margin: 2rem auto;
            box-shadow: 
                0 32px 64px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(24px) saturate(180%);
        }
        
        .page-header {
            background: linear-gradient(135deg, 
                rgba(16, 185, 129, 0.9) 0%, 
                rgba(59, 130, 246, 0.9) 50%,
                rgba(245, 158, 11, 0.9) 100%);
            color: white;
            border-radius: 24px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: 
                0 20px 40px rgba(16, 185, 129, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 255, 255, 0.15), 
                transparent);
            animation: shine 4s ease-in-out infinite;
        }
        
        @keyframes shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }
        
        /* بطاقات البرامج الأكاديمية */
        .programs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .program-card {
            background: linear-gradient(135deg, 
                rgba(15, 23, 42, 0.8) 0%, 
                rgba(30, 27, 75, 0.8) 100%);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 24px;
            padding: 2rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            margin-bottom: 0;
        }
        
        .program-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #3b82f6, #f59e0b, #8b5cf6);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }
        
        .program-card:hover::before {
            transform: scaleX(1);
        }
        
        .program-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 
                0 25px 50px rgba(16, 185, 129, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            border-color: rgba(16, 185, 129, 0.4);
        }
        
        .program-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            background: linear-gradient(135deg, #10b981, #3b82f6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
        }
        
        .degree-badge {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            color: white;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 600;
            font-size: 0.85rem;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        .action-btn {
            background: linear-gradient(135deg, 
                rgba(16, 185, 129, 0.1) 0%, 
                rgba(59, 130, 246, 0.1) 100%);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.6rem 1.2rem;
            transition: all 0.3s ease;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
        }
        
        .action-btn:hover {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
        }
        
        .filter-tabs {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 16px;
            padding: 1rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .filter-tab {
            background: transparent;
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }
        
        .filter-tab.active {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        .filter-tab:hover {
            background: rgba(16, 185, 129, 0.2);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="page-header">
                <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 800; font-family: 'Cairo', sans-serif;">
                    <i class="bi bi-mortarboard me-3"></i>
                    البرامج الأكاديمية
                </h1>
                <p class="mb-0 mt-3" style="font-size: 1.2rem; opacity: 0.9; font-family: 'Cairo', sans-serif;">
                    إدارة شاملة لبرامج الدراسات العليا والشهادات الأكاديمية
                </p>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: #ffffff; font-weight: 700; font-family: 'Cairo', sans-serif;">
                        <i class="bi bi-journal-bookmark me-2" style="color: #10b981;"></i>
                        البرامج الأكاديمية
                    </h2>
                    <p class="text-muted mb-0">{{ programs|length }} برنامج أكاديمي مسجل في النظام</p>
                </div>
                <div class="d-flex gap-3">
                    <button class="action-btn" onclick="showAllPrograms()">
                        <i class="bi bi-grid me-2"></i>عرض الكل
                    </button>
                    <a href="{{ url_for('add_program') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة برنامج جديد
                    </a>
                </div>
            </div>

            <!-- فلاتر البرامج -->
            <div class="filter-tabs">
                <div class="d-flex flex-wrap align-items-center">
                    <span class="me-3 text-white fw-bold" style="font-family: 'Cairo', sans-serif;">تصفية حسب الدرجة:</span>
                    <button class="filter-tab active" onclick="filterByDegree('all')">جميع البرامج</button>
                    <button class="filter-tab" onclick="filterByDegree('ماجستير')">الماجستير</button>
                    <button class="filter-tab" onclick="filterByDegree('دكتوراه')">الدكتوراه</button>
                    <button class="filter-tab" onclick="filterByDegree('دبلوم')">الدبلوم</button>
                </div>
            </div>

            {% if programs %}
                <!-- تجميع البرامج حسب القسم -->
                {% set programs_by_department = {} %}
                {% for program in programs %}
                    {% set dept_name = program.department.name if program.department else 'غير محدد' %}
                    {% if dept_name not in programs_by_department %}
                        {% set _ = programs_by_department.update({dept_name: []}) %}
                    {% endif %}
                    {% set _ = programs_by_department[dept_name].append(program) %}
                {% endfor %}

                {% for dept_name, dept_programs in programs_by_department.items() %}
                    <div class="department-section mb-5" data-department="{{ dept_programs[0].department.id if dept_programs[0].department else 'none' }}">
                        <h3 class="mb-4" style="color: #10b981; font-weight: 700; border-bottom: 2px solid rgba(16, 185, 129, 0.3); padding-bottom: 0.5rem; font-family: 'Cairo', sans-serif;">
                            <i class="bi bi-diagram-3 me-2"></i>{{ dept_name }}
                            <span class="badge bg-primary ms-2">{{ dept_programs|length }} برنامج</span>
                        </h3>
                        
                        <div class="programs-grid">
                            {% for program in dept_programs %}
                                <div class="program-card" data-degree="{{ program.program_type or '' }}" data-duration="{{ program.duration or '' }}">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="program-icon">
                                                <i class="bi bi-mortarboard"></i>
                                            </div>
                                            <div class="ms-3">
                                                <h5 class="mb-1 fw-bold text-white" style="font-family: 'Cairo', sans-serif;">{{ program.name }}</h5>
                                                <small class="text-muted">{{ program.code or 'لا يوجد رمز' }}</small>
                                            </div>
                                        </div>
                                        <div class="dropdown">
                                            <button class="action-btn btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url_for('edit_program', id=program.id) }}">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('subjects') }}?program={{ program.id }}">
                                                    <i class="bi bi-book me-2"></i>المواد الدراسية
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('students') }}?program={{ program.id }}">
                                                    <i class="bi bi-people me-2"></i>الطلاب
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeleteProgram('{{ program.name }}', {{ program.id }})">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    {% if program.description %}
                                        <div class="mb-3">
                                            <small class="text-muted">الوصف:</small>
                                            <p class="text-white mb-0" style="font-family: 'Cairo', sans-serif;">{{ program.description }}</p>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">نوع البرنامج:</small>
                                            <div class="degree-badge mt-1">{{ program.program_type or 'غير محدد' }}</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">المدة:</small>
                                            <div class="text-white fw-bold mt-1" style="font-family: 'Cairo', sans-serif;">{{ program.duration or 'غير محدد' }}</div>
                                        </div>
                                    </div>
                                    
                                    {% if program.requirements %}
                                        <div class="mb-3">
                                            <small class="text-muted">المتطلبات:</small>
                                            <div class="text-white" style="font-family: 'Cairo', sans-serif;">{{ program.requirements }}</div>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex gap-2">
                                            {% set students_count = program.students|length if program.students else 0 %}
                                            {% if students_count > 0 %}
                                                <span class="badge bg-success rounded-pill">{{ students_count }} طالب</span>
                                            {% else %}
                                                <span class="badge bg-secondary rounded-pill">لا يوجد طلاب</span>
                                            {% endif %}
                                            
                                            {% set subjects_count = program.subjects|length if program.subjects else 0 %}
                                            {% if subjects_count > 0 %}
                                                <span class="badge bg-info rounded-pill">{{ subjects_count }} مادة</span>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar3 me-1"></i>
                                            {{ program.created_at.strftime('%Y-%m-%d') if program.created_at else 'غير محدد' }}
                                        </small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <div class="program-icon mx-auto mb-4" style="width: 100px; height: 100px; font-size: 3rem; opacity: 0.5;">
                        <i class="bi bi-mortarboard"></i>
                    </div>
                    <h3 class="text-white mb-3" style="font-family: 'Cairo', sans-serif;">لا توجد برامج أكاديمية مسجلة</h3>
                    <p class="text-muted mb-4">لم يتم تسجيل أي برامج أكاديمية في النظام بعد</p>
                    <a href="{{ url_for('add_program') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة برنامج جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        // فلترة البرامج حسب الدرجة
        function filterByDegree(degree) {
            const cards = document.querySelectorAll('.program-card');
            const tabs = document.querySelectorAll('.filter-tab');
            
            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // إضافة الفئة النشطة للتبويب المحدد
            event.target.classList.add('active');
            
            cards.forEach(card => {
                if (degree === 'all' || card.dataset.degree === degree) {
                    card.style.display = 'block';
                    card.style.animation = 'slideInUp 0.5s ease-out';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // عرض جميع البرامج
        function showAllPrograms() {
            const cards = document.querySelectorAll('.program-card');
            const tabs = document.querySelectorAll('.filter-tab');
            
            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));
            tabs[0].classList.add('active'); // تفعيل تبويب "جميع البرامج"
            
            cards.forEach((card, index) => {
                card.style.display = 'block';
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        }

        // تأكيد حذف البرنامج
        function confirmDeleteProgram(programName, programId) {
            if (confirm(`هل أنت متأكد من حذف البرنامج "${programName}"؟\nسيتم حذف جميع البيانات المرتبطة به.`)) {
                fetch(`/admin/programs/delete/${programId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }

        // تحسين الرسوم المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.program-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        });

        // رسوم متحركة
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-load {
                animation: slideInUp 0.6s ease-out;
                animation-fill-mode: both;
            }
            
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
