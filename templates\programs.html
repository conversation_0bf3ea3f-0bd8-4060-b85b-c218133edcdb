<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البرامج الأكاديمية - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
        }

        .main-container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem auto;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .page-header {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3);
            text-align: center;
        }
        .program-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            margin-bottom: 1.5rem;
            position: relative;
            position: relative;
        }
        .program-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        .program-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        .program-card.masters::before {
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .program-card.phd::before {
            background: linear-gradient(90deg, #f093fb, #f5576c);
        }
        .program-card.diploma::before {
            background: linear-gradient(90deg, #4facfe, #00f2fe);
        }
        .program-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        .program-icon.masters {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .program-icon.phd {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .program-icon.diploma {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            border-color: #43e97b;
            box-shadow: 0 0 0 0.2rem rgba(67, 233, 123, 0.25);
        }
        .stats-mini {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.8rem;
            text-align: center;
            margin: 0.2rem;
        }
        .animate-on-load {
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .duration-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.9);
            color: #333;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- عنوان الصفحة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="page-header animate-on-load">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-6 mb-2 fw-bold">
                                <i class="bi bi-journal-bookmark me-3"></i>البرامج الأكاديمية
                            </h1>
                            <p class="lead mb-0">إدارة شاملة لجميع برامج الدراسات العليا في النظام</p>
                            <div class="mt-3">
                                <span class="badge bg-light text-dark me-2">
                                    <i class="bi bi-mortarboard me-1"></i>{{ programs|length }} برنامج
                                </span>
                                <span class="badge bg-light text-dark me-2">
                                    <i class="bi bi-people me-1"></i>
                                    {% set total_students = 0 %}
                                    {% for program in programs %}
                                        {% set total_students = total_students + program.students|length %}
                                    {% endfor %}
                                    {{ total_students }} طالب
                                </span>
                                <span class="badge bg-light text-dark">
                                    <i class="bi bi-book me-1"></i>
                                    {% set total_subjects = 0 %}
                                    {% for program in programs %}
                                        {% set total_subjects = total_subjects + program.subjects|length %}
                                    {% endfor %}
                                    {{ total_subjects }} مادة
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('add_program') }}" class="btn btn-light btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>إضافة برنامج جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم البحث والفلترة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="filter-section animate-on-load">
                    <div class="row align-items-end g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label fw-bold">
                                <i class="bi bi-search me-2"></i>البحث السريع
                            </label>
                            <input type="text" class="form-control search-box" id="search"
                                   placeholder="البحث بالاسم، النوع، أو القسم...">
                        </div>
                        <div class="col-md-3">
                            <label for="type_filter" class="form-label fw-bold">
                                <i class="bi bi-funnel me-2"></i>نوع البرنامج
                            </label>
                            <select class="form-select" id="type_filter">
                                <option value="">جميع الأنواع</option>
                                <option value="ماجستير">ماجستير</option>
                                <option value="دكتوراه">دكتوراه</option>
                                <option value="دبلوم عالي">دبلوم عالي</option>
                                </select>
                        </div>
                        <div class="col-md-3">
                            <label for="duration_filter" class="form-label fw-bold">
                                <i class="bi bi-clock me-2"></i>المدة
                            </label>
                            <select class="form-select" id="duration_filter">
                                <option value="">جميع المدد</option>
                                <option value="سنة واحدة">سنة واحدة</option>
                                <option value="سنتان">سنتان</option>
                                <option value="ثلاث سنوات">ثلاث سنوات</option>
                                <option value="أربع سنوات">أربع سنوات</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary active" onclick="showCardView()">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="showTableView()">
                                    <i class="bi bi-table"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض البطاقات -->
        <div id="cardView" class="row">
            {% if programs %}
                {% for program in programs %}
                <div class="col-lg-4 col-md-6 mb-4 program-item animate-on-load"
                     data-name="{{ program.name|lower }}"
                     data-type="{{ program.program_type|lower if program.program_type else '' }}"
                     data-duration="{{ program.duration|lower if program.duration else '' }}"
                     data-department="{{ program.department.name|lower if program.department else '' }}">
                    <div class="program-card {{ program.program_type|lower if program.program_type else 'masters' }} position-relative">
                        <div class="duration-badge">
                            <i class="bi bi-clock me-1"></i>{{ program.duration or 'غير محدد' }}
                        </div>
                        <div class="card-body p-4">
                            <div class="d-flex align-items-start mb-3">
                                <div class="program-icon {{ program.program_type|lower if program.program_type else 'masters' }} me-3">
                                    {% if program.program_type == 'ماجستير' %}
                                        <i class="bi bi-mortarboard"></i>
                                    {% elif program.program_type == 'دكتوراه' %}
                                        <i class="bi bi-award"></i>
                                    {% else %}
                                        <i class="bi bi-bookmark"></i>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="card-title fw-bold mb-1">{{ program.name }}</h5>
                                    <p class="text-muted small mb-2">
                                        <i class="bi bi-building me-1"></i>{{ program.department.name if program.department else 'غير محدد' }}
                                    </p>
                                    <span class="badge bg-primary rounded-pill">{{ program.program_type or 'غير محدد' }}</span>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ url_for('edit_program', id=program.id) }}">
                                            <i class="bi bi-pencil me-2"></i>تعديل
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ url_for('subjects') }}?program={{ program.id }}">
                                            <i class="bi bi-book me-2"></i>المواد الدراسية
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"
                                               onclick="confirmDelete('{{ program.name }}', {{ program.id }})">
                                            <i class="bi bi-trash me-2"></i>حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="row g-2 mb-3">
                                <div class="col-4">
                                    <div class="stats-mini">
                                        <h6 class="fw-bold text-primary mb-1">{{ program.students|length }}</h6>
                                        <small class="text-muted">طالب</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stats-mini">
                                        <h6 class="fw-bold text-success mb-1">{{ program.subjects|length }}</h6>
                                        <small class="text-muted">مادة</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stats-mini">
                                        <h6 class="fw-bold text-info mb-1">
                                            {% set total_units = 0 %}
                                            {% for subject in program.subjects %}
                                                {% set total_units = total_units + (subject.units or 0) %}
                                            {% endfor %}
                                            {{ total_units }}
                                        </h6>
                                        <small class="text-muted">وحدة</small>
                                    </div>
                                </div>
                            </div>

                            {% if program.description %}
                            <p class="card-text text-muted small">{{ program.description[:100] }}{% if program.description|length > 100 %}...{% endif %}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-journal-bookmark text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">لا توجد برامج أكاديمية</h4>
                        <p class="text-muted">ابدأ بإضافة برنامج جديد</p>
                        <a href="{{ url_for('add_program') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>إضافة برنامج جديد
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- عرض الجدول -->
        <div id="tableView" class="row" style="display: none;">
            <div class="col-12">
                <div class="card shadow animate-on-load">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-table me-2"></i>قائمة البرامج الأكاديمية
                            <span class="badge bg-primary ms-2">{{ programs|length }}</span>
                        </h6>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                                <i class="bi bi-file-earmark-excel me-1"></i>تصدير Excel
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="printTable()">
                                <i class="bi bi-printer me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if programs %}
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="programsTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%" class="text-center">#</th>
                                        <th width="30%">
                                            <i class="bi bi-journal-bookmark me-2"></i>البرنامج
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-award me-2"></i>النوع والمدة
                                        </th>
                                        <th width="20%">
                                            <i class="bi bi-building me-2"></i>القسم
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-graph-up me-2"></i>الإحصائيات
                                        </th>
                                        <th width="15%" class="text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for program in programs %}
                                    <tr class="program-row"
                                        data-name="{{ program.name|lower }}"
                                        data-type="{{ program.program_type|lower if program.program_type else '' }}"
                                        data-duration="{{ program.duration|lower if program.duration else '' }}"
                                        data-department="{{ program.department.name|lower if program.department else '' }}">
                                        <td class="text-center fw-bold text-muted">{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="program-icon {{ program.program_type|lower if program.program_type else 'masters' }} me-3"
                                                     style="width: 45px; height: 45px; font-size: 1.2rem;">
                                                    {% if program.program_type == 'ماجستير' %}
                                                        <i class="bi bi-mortarboard"></i>
                                                    {% elif program.program_type == 'دكتوراه' %}
                                                        <i class="bi bi-award"></i>
                                                    {% else %}
                                                        <i class="bi bi-bookmark"></i>
                                                    {% endif %}
                                                </div>
                                                <div>
                                                    <h6 class="mb-1 fw-bold">{{ program.name }}</h6>
                                                    {% if program.description %}
                                                    <small class="text-muted">{{ program.description[:60] }}{% if program.description|length > 60 %}...{% endif %}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            {% set type_colors = {
                                                'ماجستير': 'success',
                                                'دكتوراه': 'danger',
                                                'دبلوم عالي': 'warning'
                                            } %}
                                            <div class="d-flex flex-column align-items-center gap-2">
                                                <span class="badge bg-{{ type_colors.get(program.program_type, 'secondary') }} rounded-pill">
                                                    {{ program.program_type or 'غير محدد' }}
                                                </span>
                                                <small class="text-muted">
                                                    <i class="bi bi-clock me-1"></i>{{ program.duration or 'غير محدد' }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            {% if program.department %}
                                            <div>
                                                <h6 class="mb-1 fw-bold">{{ program.department.name }}</h6>
                                                {% if program.department.college %}
                                                <small class="text-muted">{{ program.department.college.name_ar }}</small>
                                                {% endif %}
                                            </div>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex justify-content-center gap-2">
                                                <span class="badge bg-primary rounded-pill" title="الطلبة">
                                                    {{ program.students|length }}
                                                </span>
                                                <span class="badge bg-success rounded-pill" title="المواد">
                                                    {{ program.subjects|length }}
                                                </span>
                                                <span class="badge bg-info rounded-pill" title="الوحدات">
                                                    {% set total_units = 0 %}
                                                    {% for subject in program.subjects %}
                                                        {% set total_units = total_units + (subject.units or 0) %}
                                                    {% endfor %}
                                                    {{ total_units }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#programModal{{ program.id }}"
                                                        data-bs-toggle="tooltip"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <a href="{{ url_for('edit_program', id=program.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   data-bs-toggle="tooltip"
                                                   title="تعديل البرنامج">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف البرنامج"
                                                        onclick="confirmDelete('{{ program.name }}', {{ program.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Modal لعرض تفاصيل البرنامج -->
                                    <div class="modal fade" id="programModal{{ program.id }}" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تفاصيل البرنامج: {{ program.name }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">اسم البرنامج:</td>
                                                                    <td>{{ program.name }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">نوع البرنامج:</td>
                                                                    <td>{{ program.program_type }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">المدة:</td>
                                                                    <td>{{ program.duration or 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">القسم:</td>
                                                                    <td>{{ program.department.name if program.department else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد المواد:</td>
                                                                    <td>{{ program.subjects|length }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الطلبة:</td>
                                                                    <td>{{ program.students|length }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    
                                                    {% if program.description %}
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <h6>وصف البرنامج:</h6>
                                                            <p class="text-muted">{{ program.description }}</p>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                    
                                                    {% if program.subjects %}
                                                    <div class="row mt-3">
                                                        <div class="col-12">
                                                            <h6>المواد الدراسية:</h6>
                                                            <div class="row">
                                                                {% for subject in program.subjects %}
                                                                <div class="col-md-6 mb-2">
                                                                    <div class="card border-primary">
                                                                        <div class="card-body p-2">
                                                                            <h6 class="card-title mb-1">{{ subject.name }}</h6>
                                                                            <small class="text-muted">
                                                                                {{ subject.units }} وحدة - {{ subject.subject_type or 'غير محدد' }}
                                                                            </small>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                {% endfor %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-journal-bookmark text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا توجد برامج أكاديمية مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة برنامج أكاديمي جديد</p>
                            <a href="{{ url_for('add_program') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إضافة برنامج جديد
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسينات تفاعلية لصفحة البرامج الأكاديمية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل المتدرج
            const animatedElements = document.querySelectorAll('.animate-on-load');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });

            // تفعيل tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // إعداد الفلاتر
            const searchInput = document.getElementById('search');
            const typeFilter = document.getElementById('type_filter');
            const durationFilter = document.getElementById('duration_filter');

            // ربط أحداث الفلترة
            if (searchInput) searchInput.addEventListener('input', filterPrograms);
            if (typeFilter) typeFilter.addEventListener('change', filterPrograms);
            if (durationFilter) durationFilter.addEventListener('change', filterPrograms);

            // تأثيرات hover للبطاقات
            const programCards = document.querySelectorAll('.program-card');
            programCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // دالة الفلترة المحسنة
        function filterPrograms() {
            const searchTerm = document.getElementById('search').value.toLowerCase();
            const typeValue = document.getElementById('type_filter').value;
            const durationValue = document.getElementById('duration_filter').value;

            // فلترة البطاقات
            const cardView = document.getElementById('cardView');
            const programItems = cardView.querySelectorAll('.program-item');
            let visibleCards = 0;

            programItems.forEach(item => {
                const name = item.getAttribute('data-name');
                const type = item.getAttribute('data-type');
                const duration = item.getAttribute('data-duration');
                const department = item.getAttribute('data-department');

                const matchesSearch = name.includes(searchTerm) || department.includes(searchTerm);
                const matchesType = !typeValue || type.includes(typeValue.toLowerCase());
                const matchesDuration = !durationValue || duration.includes(durationValue.toLowerCase());

                if (matchesSearch && matchesType && matchesDuration) {
                    item.style.display = 'block';
                    item.classList.add('animate-on-load');
                    visibleCards++;
                } else {
                    item.style.display = 'none';
                }
            });

            // فلترة الجدول
            const tableView = document.getElementById('tableView');
            if (tableView) {
                const tableRows = tableView.querySelectorAll('.program-row');

                tableRows.forEach(row => {
                    const name = row.getAttribute('data-name');
                    const type = row.getAttribute('data-type');
                    const duration = row.getAttribute('data-duration');
                    const department = row.getAttribute('data-department');

                    const matchesSearch = name.includes(searchTerm) || department.includes(searchTerm);
                    const matchesType = !typeValue || type.includes(typeValue.toLowerCase());
                    const matchesDuration = !durationValue || duration.includes(durationValue.toLowerCase());

                    if (matchesSearch && matchesType && matchesDuration) {
                        row.style.display = 'table-row';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
        }

        // تبديل العرض بين البطاقات والجدول
        function showCardView() {
            document.getElementById('cardView').style.display = 'flex';
            document.getElementById('tableView').style.display = 'none';

            // تحديث الأزرار
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function showTableView() {
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('tableView').style.display = 'block';

            // تحديث الأزرار
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // دالة تأكيد الحذف المحسنة
        function confirmDelete(name, id) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>تأكيد حذف البرنامج
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <i class="bi bi-journal-x text-danger" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">هل أنت متأكد من حذف البرنامج؟</h5>
                            <p class="text-muted">${name}</p>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> سيتم حذف جميع المواد والطلبة المرتبطة بهذا البرنامج.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteProgram(${id})">
                                <i class="bi bi-trash me-2"></i>حذف نهائياً
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                modal.remove();
            });
        }

        // دالة الحذف الفعلي
        function deleteProgram(id) {
            window.location.href = '/admin/programs/delete/' + id;
        }

        // دوال التصدير والطباعة
        function exportToExcel() {
            alert('سيتم تنفيذ تصدير Excel قريباً');
        }

        function printTable() {
            window.print();
        }
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
