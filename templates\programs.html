<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البرامج الأكاديمية - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap');

        body {
            background: #0a0a0a;
            font-family: 'Cairo', 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* خلفية ديناميكية للبرامج الأكاديمية */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 30% 40%, #10b981 0%, transparent 50%),
                radial-gradient(circle at 70% 20%, #3b82f6 0%, transparent 50%),
                radial-gradient(circle at 20% 80%, #f59e0b 0%, transparent 50%),
                radial-gradient(circle at 80% 60%, #8b5cf6 0%, transparent 50%),
                linear-gradient(135deg, #1e1b4b 0%, #0f172a 100%);
            z-index: -2;
            animation: backgroundShift 40s ease-in-out infinite;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="12" height="12" patternUnits="userSpaceOnUse"><path d="M 12 0 L 0 0 0 12" fill="none" stroke="%23ffffff" stroke-width="0.2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
            opacity: 0.3;
        }

        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            25% { filter: hue-rotate(90deg) brightness(1.1); }
            50% { filter: hue-rotate(180deg) brightness(0.9); }
            75% { filter: hue-rotate(270deg) brightness(1.1); }
        }

        .main-container {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.95) 0%,
                rgba(30, 27, 75, 0.95) 100%);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 32px;
            padding: 3rem;
            margin: 2rem auto;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(24px) saturate(180%);
        }

        .page-header {
            background: linear-gradient(135deg,
                rgba(16, 185, 129, 0.9) 0%,
                rgba(59, 130, 246, 0.9) 50%,
                rgba(245, 158, 11, 0.9) 100%);
            color: white;
            border-radius: 24px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow:
                0 20px 40px rgba(16, 185, 129, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.15),
                transparent);
            animation: shine 4s ease-in-out infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }
        /* بطاقات البرامج الأكاديمية */
        .programs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .program-card {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.8) 0%,
                rgba(30, 27, 75, 0.8) 100%);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 24px;
            padding: 2rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            margin-bottom: 0;
        }

        .program-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #3b82f6, #f59e0b, #8b5cf6);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .program-card:hover::before {
            transform: scaleX(1);
        }

        .program-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(16, 185, 129, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            border-color: rgba(16, 185, 129, 0.4);
        }

        .program-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            background: linear-gradient(135deg, #10b981, #3b82f6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
        }

        .degree-badge {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            color: white;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 600;
            font-size: 0.85rem;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .action-btn {
            background: linear-gradient(135deg,
                rgba(16, 185, 129, 0.1) 0%,
                rgba(59, 130, 246, 0.1) 100%);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.6rem 1.2rem;
            transition: all 0.3s ease;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
        }

        .action-btn:hover {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
        }

        .filter-tabs {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 16px;
            padding: 1rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .filter-tab {
            background: transparent;
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .filter-tab:hover {
            background: rgba(16, 185, 129, 0.2);
            transform: translateY(-2px);
        }
        .program-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        .program-icon.masters {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .program-icon.phd {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .program-icon.diploma {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            border-color: #43e97b;
            box-shadow: 0 0 0 0.2rem rgba(67, 233, 123, 0.25);
        }
        .stats-mini {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.8rem;
            text-align: center;
            margin: 0.2rem;
        }
        .animate-on-load {
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .duration-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.9);
            color: #333;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

            <div class="page-header">
                <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 800; font-family: 'Cairo', sans-serif;">
                    <i class="bi bi-mortarboard me-3"></i>
                    البرامج الأكاديمية
                </h1>
                <p class="mb-0 mt-3" style="font-size: 1.2rem; opacity: 0.9; font-family: 'Cairo', sans-serif;">
                    إدارة شاملة لبرامج الدراسات العليا والشهادات الأكاديمية
                </p>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: #ffffff; font-weight: 700; font-family: 'Cairo', sans-serif;">
                        <i class="bi bi-journal-bookmark me-2" style="color: #10b981;"></i>
                        البرامج الأكاديمية
                    </h2>
                    <p class="text-muted mb-0">{{ programs|length }} برنامج أكاديمي مسجل في النظام</p>
                </div>
                <div class="d-flex gap-3">
                    <button class="action-btn" onclick="showAllPrograms()">
                        <i class="bi bi-grid me-2"></i>عرض الكل
                    </button>
                    <a href="{{ url_for('add_program') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة برنامج جديد
                    </a>
                </div>
            </div>

            <!-- فلاتر البرامج -->
            <div class="filter-tabs">
                <div class="d-flex flex-wrap align-items-center">
                    <span class="me-3 text-white fw-bold" style="font-family: 'Cairo', sans-serif;">تصفية حسب الدرجة:</span>
                    <button class="filter-tab active" onclick="filterByDegree('all')">جميع البرامج</button>
                    <button class="filter-tab" onclick="filterByDegree('ماجستير')">الماجستير</button>
                    <button class="filter-tab" onclick="filterByDegree('دكتوراه')">الدكتوراه</button>
                    <button class="filter-tab" onclick="filterByDegree('دبلوم')">الدبلوم</button>
                </div>
            </div>

                <!-- تجميع البرامج حسب القسم -->
                {% set programs_by_department = {} %}
                {% for program in programs %}
                    {% set dept_name = program.department.name if program.department else 'غير محدد' %}
                    {% if dept_name not in programs_by_department %}
                        {% set _ = programs_by_department.update({dept_name: []}) %}
                    {% endif %}
                    {% set _ = programs_by_department[dept_name].append(program) %}
                {% endfor %}

                {% for dept_name, dept_programs in programs_by_department.items() %}
                    <div class="department-section mb-5" data-department="{{ dept_programs[0].department.id if dept_programs[0].department else 'none' }}">
                        <h3 class="mb-4" style="color: #10b981; font-weight: 700; border-bottom: 2px solid rgba(16, 185, 129, 0.3); padding-bottom: 0.5rem; font-family: 'Cairo', sans-serif;">
                            <i class="bi bi-diagram-3 me-2"></i>{{ dept_name }}
                            <span class="badge bg-primary ms-2">{{ dept_programs|length }} برنامج</span>
                        </h3>

                                <div class="program-card" data-degree="{{ program.program_type or '' }}" data-duration="{{ program.duration or '' }}">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="program-icon">
                                                <i class="bi bi-mortarboard"></i>
                                            </div>
                                            <div class="ms-3">
                                                <h5 class="mb-1 fw-bold text-white" style="font-family: 'Cairo', sans-serif;">{{ program.name }}</h5>
                                                <small class="text-muted">{{ program.code or 'لا يوجد رمز' }}</small>
                                            </div>
                                        </div>
                                        <div class="dropdown">
                                            <button class="action-btn btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url_for('edit_program', id=program.id) }}">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('subjects') }}?program={{ program.id }}">
                                                    <i class="bi bi-book me-2"></i>المواد الدراسية
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('students') }}?program={{ program.id }}">
                                                    <i class="bi bi-people me-2"></i>الطلاب
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeleteProgram('{{ program.name }}', {{ program.id }})">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>

                                    {% if program.description %}
                                        <div class="mb-3">
                                            <small class="text-muted">الوصف:</small>
                                            <p class="text-white mb-0" style="font-family: 'Cairo', sans-serif;">{{ program.description }}</p>
                                        </div>
                                    {% endif %}

                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">نوع البرنامج:</small>
                                            <div class="degree-badge mt-1">{{ program.program_type or 'غير محدد' }}</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">المدة:</small>
                                            <div class="text-white fw-bold mt-1" style="font-family: 'Cairo', sans-serif;">{{ program.duration or 'غير محدد' }}</div>
                                        </div>
                                    </div>

                                    {% if program.requirements %}
                                        <div class="mb-3">
                                            <small class="text-muted">المتطلبات:</small>
                                            <div class="text-white" style="font-family: 'Cairo', sans-serif;">{{ program.requirements }}</div>
                                        </div>
                                    {% endif %}

                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex gap-2">
                                            {% set students_count = program.students|length if program.students else 0 %}
                                            {% if students_count > 0 %}
                                                <span class="badge bg-success rounded-pill">{{ students_count }} طالب</span>
                                            {% else %}
                                                <span class="badge bg-secondary rounded-pill">لا يوجد طلاب</span>
                                            {% endif %}

                                            {% set subjects_count = program.subjects|length if program.subjects else 0 %}
                                            {% if subjects_count > 0 %}
                                                <span class="badge bg-info rounded-pill">{{ subjects_count }} مادة</span>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar3 me-1"></i>
                                            {{ program.created_at.strftime('%Y-%m-%d') if program.created_at else 'غير محدد' }}
                                        </small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
            {% else %}
                <div class="text-center py-5">
                    <div class="program-icon mx-auto mb-4" style="width: 100px; height: 100px; font-size: 3rem; opacity: 0.5;">
                        <i class="bi bi-mortarboard"></i>
                    </div>
                    <h3 class="text-white mb-3" style="font-family: 'Cairo', sans-serif;">لا توجد برامج أكاديمية مسجلة</h3>
                    <p class="text-muted mb-4">لم يتم تسجيل أي برامج أكاديمية في النظام بعد</p>
                    <a href="{{ url_for('add_program') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة برنامج جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        // فلترة البرامج حسب الدرجة
        function filterByDegree(degree) {
            const cards = document.querySelectorAll('.program-card');
            const tabs = document.querySelectorAll('.filter-tab');

            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));

            // إضافة الفئة النشطة للتبويب المحدد
            event.target.classList.add('active');

            cards.forEach(card => {
                if (degree === 'all' || card.dataset.degree === degree) {
                    card.style.display = 'block';
                    card.style.animation = 'slideInUp 0.5s ease-out';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // عرض جميع البرامج
        function showAllPrograms() {
            const cards = document.querySelectorAll('.program-card');
            const tabs = document.querySelectorAll('.filter-tab');

            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));
            tabs[0].classList.add('active'); // تفعيل تبويب "جميع البرامج"

            cards.forEach((card, index) => {
                card.style.display = 'block';
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        }

        // تأكيد حذف البرنامج
        function confirmDeleteProgram(programName, programId) {
            if (confirm(`هل أنت متأكد من حذف البرنامج "${programName}"؟\nسيتم حذف جميع البيانات المرتبطة به.`)) {
                fetch(`/admin/programs/delete/${programId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }

        // تحسين الرسوم المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.program-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        });

        // رسوم متحركة
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-load {
                animation: slideInUp 0.6s ease-out;
                animation-fill-mode: both;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ url_for('edit_program', id=program.id) }}">
                                            <i class="bi bi-pencil me-2"></i>تعديل
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ url_for('subjects') }}?program={{ program.id }}">
                                            <i class="bi bi-book me-2"></i>المواد الدراسية
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"
                                               onclick="confirmDelete('{{ program.name }}', {{ program.id }})">
                                            <i class="bi bi-trash me-2"></i>حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="row g-2 mb-3">
                                <div class="col-4">
                                    <div class="stats-mini">
                                        <h6 class="fw-bold text-primary mb-1">{{ program.students|length }}</h6>
                                        <small class="text-muted">طالب</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stats-mini">
                                        <h6 class="fw-bold text-success mb-1">{{ program.subjects|length }}</h6>
                                        <small class="text-muted">مادة</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stats-mini">
                                        <h6 class="fw-bold text-info mb-1">
                                            {% set total_units = 0 %}
                                            {% for subject in program.subjects %}
                                                {% set total_units = total_units + (subject.units or 0) %}
                                            {% endfor %}
                                            {{ total_units }}
                                        </h6>
                                        <small class="text-muted">وحدة</small>
                                    </div>
                                </div>
                            </div>

                            {% if program.description %}
                            <p class="card-text text-muted small">{{ program.description[:100] }}{% if program.description|length > 100 %}...{% endif %}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-journal-bookmark text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">لا توجد برامج أكاديمية</h4>
                        <p class="text-muted">ابدأ بإضافة برنامج جديد</p>
                        <a href="{{ url_for('add_program') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>إضافة برنامج جديد
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- عرض الجدول -->
        <div id="tableView" class="row" style="display: none;">
            <div class="col-12">
                <div class="card shadow animate-on-load">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-table me-2"></i>قائمة البرامج الأكاديمية
                            <span class="badge bg-primary ms-2">{{ programs|length }}</span>
                        </h6>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                                <i class="bi bi-file-earmark-excel me-1"></i>تصدير Excel
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="printTable()">
                                <i class="bi bi-printer me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if programs %}
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="programsTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%" class="text-center">#</th>
                                        <th width="30%">
                                            <i class="bi bi-journal-bookmark me-2"></i>البرنامج
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-award me-2"></i>النوع والمدة
                                        </th>
                                        <th width="20%">
                                            <i class="bi bi-building me-2"></i>القسم
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-graph-up me-2"></i>الإحصائيات
                                        </th>
                                        <th width="15%" class="text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for program in programs %}
                                    <tr class="program-row"
                                        data-name="{{ program.name|lower }}"
                                        data-type="{{ program.program_type|lower if program.program_type else '' }}"
                                        data-duration="{{ program.duration|lower if program.duration else '' }}"
                                        data-department="{{ program.department.name|lower if program.department else '' }}">
                                        <td class="text-center fw-bold text-muted">{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="program-icon {{ program.program_type|lower if program.program_type else 'masters' }} me-3"
                                                     style="width: 45px; height: 45px; font-size: 1.2rem;">
                                                    {% if program.program_type == 'ماجستير' %}
                                                        <i class="bi bi-mortarboard"></i>
                                                    {% elif program.program_type == 'دكتوراه' %}
                                                        <i class="bi bi-award"></i>
                                                    {% else %}
                                                        <i class="bi bi-bookmark"></i>
                                                    {% endif %}
                                                </div>
                                                <div>
                                                    <h6 class="mb-1 fw-bold">{{ program.name }}</h6>
                                                    {% if program.description %}
                                                    <small class="text-muted">{{ program.description[:60] }}{% if program.description|length > 60 %}...{% endif %}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            {% set type_colors = {
                                                'ماجستير': 'success',
                                                'دكتوراه': 'danger',
                                                'دبلوم عالي': 'warning'
                                            } %}
                                            <div class="d-flex flex-column align-items-center gap-2">
                                                <span class="badge bg-{{ type_colors.get(program.program_type, 'secondary') }} rounded-pill">
                                                    {{ program.program_type or 'غير محدد' }}
                                                </span>
                                                <small class="text-muted">
                                                    <i class="bi bi-clock me-1"></i>{{ program.duration or 'غير محدد' }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            {% if program.department %}
                                            <div>
                                                <h6 class="mb-1 fw-bold">{{ program.department.name }}</h6>
                                                {% if program.department.college %}
                                                <small class="text-muted">{{ program.department.college.name_ar }}</small>
                                                {% endif %}
                                            </div>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex justify-content-center gap-2">
                                                <span class="badge bg-primary rounded-pill" title="الطلبة">
                                                    {{ program.students|length }}
                                                </span>
                                                <span class="badge bg-success rounded-pill" title="المواد">
                                                    {{ program.subjects|length }}
                                                </span>
                                                <span class="badge bg-info rounded-pill" title="الوحدات">
                                                    {% set total_units = 0 %}
                                                    {% for subject in program.subjects %}
                                                        {% set total_units = total_units + (subject.units or 0) %}
                                                    {% endfor %}
                                                    {{ total_units }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#programModal{{ program.id }}"
                                                        data-bs-toggle="tooltip"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <a href="{{ url_for('edit_program', id=program.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   data-bs-toggle="tooltip"
                                                   title="تعديل البرنامج">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف البرنامج"
                                                        onclick="confirmDelete('{{ program.name }}', {{ program.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Modal لعرض تفاصيل البرنامج -->
                                    <div class="modal fade" id="programModal{{ program.id }}" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تفاصيل البرنامج: {{ program.name }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">اسم البرنامج:</td>
                                                                    <td>{{ program.name }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">نوع البرنامج:</td>
                                                                    <td>{{ program.program_type }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">المدة:</td>
                                                                    <td>{{ program.duration or 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">القسم:</td>
                                                                    <td>{{ program.department.name if program.department else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد المواد:</td>
                                                                    <td>{{ program.subjects|length }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الطلبة:</td>
                                                                    <td>{{ program.students|length }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    
                                                    {% if program.description %}
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <h6>وصف البرنامج:</h6>
                                                            <p class="text-muted">{{ program.description }}</p>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                    
                                                    {% if program.subjects %}
                                                    <div class="row mt-3">
                                                        <div class="col-12">
                                                            <h6>المواد الدراسية:</h6>
                                                            <div class="row">
                                                                {% for subject in program.subjects %}
                                                                <div class="col-md-6 mb-2">
                                                                    <div class="card border-primary">
                                                                        <div class="card-body p-2">
                                                                            <h6 class="card-title mb-1">{{ subject.name }}</h6>
                                                                            <small class="text-muted">
                                                                                {{ subject.units }} وحدة - {{ subject.subject_type or 'غير محدد' }}
                                                                            </small>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                {% endfor %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-journal-bookmark text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا توجد برامج أكاديمية مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة برنامج أكاديمي جديد</p>
                            <a href="{{ url_for('add_program') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إضافة برنامج جديد
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسينات تفاعلية لصفحة البرامج الأكاديمية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل المتدرج
            const animatedElements = document.querySelectorAll('.animate-on-load');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });

            // تفعيل tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // إعداد الفلاتر
            const searchInput = document.getElementById('search');
            const typeFilter = document.getElementById('type_filter');
            const durationFilter = document.getElementById('duration_filter');

            // ربط أحداث الفلترة
            if (searchInput) searchInput.addEventListener('input', filterPrograms);
            if (typeFilter) typeFilter.addEventListener('change', filterPrograms);
            if (durationFilter) durationFilter.addEventListener('change', filterPrograms);

            // تأثيرات hover للبطاقات
            const programCards = document.querySelectorAll('.program-card');
            programCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // دالة الفلترة المحسنة
        function filterPrograms() {
            const searchTerm = document.getElementById('search').value.toLowerCase();
            const typeValue = document.getElementById('type_filter').value;
            const durationValue = document.getElementById('duration_filter').value;

            // فلترة البطاقات
            const cardView = document.getElementById('cardView');
            const programItems = cardView.querySelectorAll('.program-item');
            let visibleCards = 0;

            programItems.forEach(item => {
                const name = item.getAttribute('data-name');
                const type = item.getAttribute('data-type');
                const duration = item.getAttribute('data-duration');
                const department = item.getAttribute('data-department');

                const matchesSearch = name.includes(searchTerm) || department.includes(searchTerm);
                const matchesType = !typeValue || type.includes(typeValue.toLowerCase());
                const matchesDuration = !durationValue || duration.includes(durationValue.toLowerCase());

                if (matchesSearch && matchesType && matchesDuration) {
                    item.style.display = 'block';
                    item.classList.add('animate-on-load');
                    visibleCards++;
                } else {
                    item.style.display = 'none';
                }
            });

            // فلترة الجدول
            const tableView = document.getElementById('tableView');
            if (tableView) {
                const tableRows = tableView.querySelectorAll('.program-row');

                tableRows.forEach(row => {
                    const name = row.getAttribute('data-name');
                    const type = row.getAttribute('data-type');
                    const duration = row.getAttribute('data-duration');
                    const department = row.getAttribute('data-department');

                    const matchesSearch = name.includes(searchTerm) || department.includes(searchTerm);
                    const matchesType = !typeValue || type.includes(typeValue.toLowerCase());
                    const matchesDuration = !durationValue || duration.includes(durationValue.toLowerCase());

                    if (matchesSearch && matchesType && matchesDuration) {
                        row.style.display = 'table-row';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
        }

        // تبديل العرض بين البطاقات والجدول
        function showCardView() {
            document.getElementById('cardView').style.display = 'flex';
            document.getElementById('tableView').style.display = 'none';

            // تحديث الأزرار
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function showTableView() {
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('tableView').style.display = 'block';

            // تحديث الأزرار
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // دالة تأكيد الحذف المحسنة
        function confirmDelete(name, id) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>تأكيد حذف البرنامج
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <i class="bi bi-journal-x text-danger" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">هل أنت متأكد من حذف البرنامج؟</h5>
                            <p class="text-muted">${name}</p>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> سيتم حذف جميع المواد والطلبة المرتبطة بهذا البرنامج.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteProgram(${id})">
                                <i class="bi bi-trash me-2"></i>حذف نهائياً
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                modal.remove();
            });
        }

        // دالة الحذف الفعلي
        function deleteProgram(id) {
            window.location.href = '/admin/programs/delete/' + id;
        }

        // دوال التصدير والطباعة
        function exportToExcel() {
            alert('سيتم تنفيذ تصدير Excel قريباً');
        }

        function printTable() {
            window.print();
        }
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
