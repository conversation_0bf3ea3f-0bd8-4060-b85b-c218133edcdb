<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        .report-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            background: white;
            margin-bottom: 1.5rem;
            position: relative;
            cursor: pointer;
        }
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        .report-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
        }
        .stats-card.students::before { background: linear-gradient(90deg, #28a745, #20c997); }
        .stats-card.programs::before { background: linear-gradient(90deg, #007bff, #6610f2); }
        .stats-card.subjects::before { background: linear-gradient(90deg, #fd7e14, #e83e8c); }
        .stats-card.grades::before { background: linear-gradient(90deg, #20c997, #17a2b8); }
        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }
        .chart-container {
            position: relative;
            height: 400px;
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }
        .filter-section {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        .animate-on-load {
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .report-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }
        .report-icon.students { background: linear-gradient(135deg, #28a745, #20c997); }
        .report-icon.programs { background: linear-gradient(135deg, #007bff, #6610f2); }
        .report-icon.subjects { background: linear-gradient(135deg, #fd7e14, #e83e8c); }
        .report-icon.grades { background: linear-gradient(135deg, #20c997, #17a2b8); }
        .report-icon.analytics { background: linear-gradient(135deg, #6f42c1, #e83e8c); }
        .report-icon.export { background: linear-gradient(135deg, #17a2b8, #20c997); }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- عنوان الصفحة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="page-header animate-on-load">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-6 mb-2 fw-bold">
                                <i class="bi bi-graph-up me-3"></i>التقارير والإحصائيات
                            </h1>
                            <p class="lead mb-0">لوحة تحكم شاملة لمراقبة الأداء وتحليل البيانات الأكاديمية</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-light btn-lg me-2" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise me-2"></i>تحديث البيانات
                            </button>
                            <button class="btn btn-outline-light btn-lg" onclick="exportAllReports()">
                                <i class="bi bi-download me-2"></i>تصدير شامل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stats-card students animate-on-load">
                    <i class="bi bi-people text-success" style="font-size: 2.5rem;"></i>
                    <h3 class="mt-2 mb-1 text-success">{{ students_count or 0 }}</h3>
                    <p class="text-muted mb-0">إجمالي الطلبة</p>
                    <small class="text-success">
                        <i class="bi bi-arrow-up"></i> +5% من الشهر الماضي
                    </small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card programs animate-on-load">
                    <i class="bi bi-journal-bookmark text-primary" style="font-size: 2.5rem;"></i>
                    <h3 class="mt-2 mb-1 text-primary">{{ programs_count or 0 }}</h3>
                    <p class="text-muted mb-0">البرامج الأكاديمية</p>
                    <small class="text-primary">
                        <i class="bi bi-dash"></i> مستقر
                    </small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card subjects animate-on-load">
                    <i class="bi bi-book text-warning" style="font-size: 2.5rem;"></i>
                    <h3 class="mt-2 mb-1 text-warning">{{ subjects_count or 0 }}</h3>
                    <p class="text-muted mb-0">المواد الدراسية</p>
                    <small class="text-warning">
                        <i class="bi bi-arrow-up"></i> +2% من الشهر الماضي
                    </small>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card grades animate-on-load">
                    <i class="bi bi-clipboard-data text-info" style="font-size: 2.5rem;"></i>
                    <h3 class="mt-2 mb-1 text-info">{{ grades_count or 0 }}</h3>
                    <p class="text-muted mb-0">الدرجات المدخلة</p>
                    <small class="text-info">
                        <i class="bi bi-arrow-up"></i> +15% من الشهر الماضي
                    </small>
                </div>
            </div>
        </div>

        <!-- فلاتر التقارير -->
        <div class="row">
            <div class="col-12">
                <div class="filter-section animate-on-load">
                    <div class="row align-items-end g-3">
                        <div class="col-md-3">
                            <label for="academic_year" class="form-label fw-bold">
                                <i class="bi bi-calendar3 me-2"></i>السنة الأكاديمية
                            </label>
                            <select class="form-select" id="academic_year">
                                <option value="">جميع السنوات</option>
                                <option value="2023-2024">2023-2024</option>
                                <option value="2024-2025" selected>2024-2025</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="semester" class="form-label fw-bold">
                                <i class="bi bi-bookmark me-2"></i>الفصل الدراسي
                            </label>
                            <select class="form-select" id="semester">
                                <option value="">جميع الفصول</option>
                                <option value="first">الفصل الأول</option>
                                <option value="second">الفصل الثاني</option>
                                <option value="summer">الفصل الصيفي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="program" class="form-label fw-bold">
                                <i class="bi bi-journal-bookmark me-2"></i>البرنامج
                            </label>
                            <select class="form-select" id="program">
                                <option value="">جميع البرامج</option>
                                <!-- سيتم ملؤها ديناميكياً -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100" onclick="applyFilters()">
                                <i class="bi bi-funnel me-2"></i>تطبيق الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أنواع التقارير -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="report-card animate-on-load" onclick="generateStudentsReport()">
                    <div class="card-body p-4 text-center">
                        <div class="report-icon students">
                            <i class="bi bi-people"></i>
                        </div>
                        <h5 class="card-title fw-bold">تقرير الطلبة</h5>
                        <p class="card-text text-muted">إحصائيات شاملة عن الطلبة المسجلين والمتخرجين</p>
                        <div class="d-flex justify-content-center gap-2 mt-3">
                            <span class="badge bg-success">PDF</span>
                            <span class="badge bg-primary">Excel</span>
                            <span class="badge bg-info">Word</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="report-card animate-on-load" onclick="generateProgramsReport()">
                    <div class="card-body p-4 text-center">
                        <div class="report-icon programs">
                            <i class="bi bi-journal-bookmark"></i>
                        </div>
                        <h5 class="card-title fw-bold">تقرير البرامج</h5>
                        <p class="card-text text-muted">تحليل البرامج الأكاديمية ومعدلات الالتحاق</p>
                        <div class="d-flex justify-content-center gap-2 mt-3">
                            <span class="badge bg-success">PDF</span>
                            <span class="badge bg-primary">Excel</span>
                            <span class="badge bg-info">Word</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="report-card animate-on-load" onclick="generateGradesReport()">
                    <div class="card-body p-4 text-center">
                        <div class="report-icon grades">
                            <i class="bi bi-clipboard-data"></i>
                        </div>
                        <h5 class="card-title fw-bold">تقرير الدرجات</h5>
                        <p class="card-text text-muted">تحليل الأداء الأكاديمي والمعدلات التراكمية</p>
                        <div class="d-flex justify-content-center gap-2 mt-3">
                            <span class="badge bg-success">PDF</span>
                            <span class="badge bg-primary">Excel</span>
                            <span class="badge bg-info">Word</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسينات تفاعلية لصفحة التقارير
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل المتدرج
            const animatedElements = document.querySelectorAll('.animate-on-load');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });

            // تأثيرات hover للبطاقات
            const reportCards = document.querySelectorAll('.report-card');
            reportCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            const statsCards = document.querySelectorAll('.stats-card');
            statsCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تحديث البيانات كل 30 ثانية
            setInterval(updateStats, 30000);
        });

        // تحديث البيانات
        function refreshData() {
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = '<i class="bi bi-arrow-clockwise me-2 spinner-border spinner-border-sm"></i>جاري التحديث...';
            button.disabled = true;

            // محاكاة تحديث البيانات
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // إظهار رسالة نجاح
                showToast('تم تحديث البيانات بنجاح', 'success');

                // تحديث الإحصائيات
                updateStats();
            }, 2000);
        }

        // تحديث الإحصائيات
        function updateStats() {
            // محاكاة تحديث الأرقام
            const statsCards = document.querySelectorAll('.stats-card h3');
            statsCards.forEach(stat => {
                const currentValue = parseInt(stat.textContent);
                const newValue = currentValue + Math.floor(Math.random() * 3);
                animateNumber(stat, currentValue, newValue);
            });
        }

        // تحريك الأرقام
        function animateNumber(element, start, end) {
            const duration = 1000;
            const startTime = performance.now();

            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                const current = Math.floor(start + (end - start) * progress);
                element.textContent = current;

                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }

            requestAnimationFrame(update);
        }

        // تطبيق الفلاتر
        function applyFilters() {
            const academicYear = document.getElementById('academic_year').value;
            const semester = document.getElementById('semester').value;
            const program = document.getElementById('program').value;

            showToast('تم تطبيق الفلاتر بنجاح', 'info');

            // هنا يمكن إضافة منطق تطبيق الفلاتر الفعلي
            console.log('Filters applied:', { academicYear, semester, program });
        }

        // توليد تقرير الطلبة
        function generateStudentsReport() {
            showReportModal('تقرير الطلبة', 'students');
        }

        // توليد تقرير البرامج
        function generateProgramsReport() {
            showReportModal('تقرير البرامج الأكاديمية', 'programs');
        }

        // توليد تقرير الدرجات
        function generateGradesReport() {
            showReportModal('تقرير الدرجات والنتائج', 'grades');
        }

        // إظهار نافذة التقرير
        function showReportModal(title, type) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-file-earmark-text me-2"></i>${title}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">نوع التقرير</label>
                                    <select class="form-select" id="reportType">
                                        <option value="summary">تقرير موجز</option>
                                        <option value="detailed">تقرير مفصل</option>
                                        <option value="statistical">تقرير إحصائي</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">صيغة التصدير</label>
                                    <select class="form-select" id="exportFormat">
                                        <option value="pdf">PDF</option>
                                        <option value="excel">Excel</option>
                                        <option value="word">Word</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">من تاريخ</label>
                                    <input type="date" class="form-control" id="fromDate">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="toDate">
                                </div>
                                <div class="col-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeCharts">
                                        <label class="form-check-label" for="includeCharts">
                                            تضمين الرسوم البيانية
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-primary" onclick="generateReport('${type}')">
                                <i class="bi bi-download me-2"></i>توليد التقرير
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                modal.remove();
            });
        }

        // توليد التقرير
        function generateReport(type) {
            const reportType = document.getElementById('reportType').value;
            const exportFormat = document.getElementById('exportFormat').value;
            const fromDate = document.getElementById('fromDate').value;
            const toDate = document.getElementById('toDate').value;
            const includeCharts = document.getElementById('includeCharts').checked;

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            modal.hide();

            // إظهار شريط التقدم
            showProgressModal(type, exportFormat);
        }

        // إظهار شريط التقدم
        function showProgressModal(type, format) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-body text-center p-4">
                            <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;"></div>
                            <h5>جاري توليد التقرير...</h5>
                            <p class="text-muted">يرجى الانتظار، سيتم تحميل التقرير تلقائياً</p>
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     style="width: 0%" id="progressBar"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal, { backdrop: 'static' });
            bsModal.show();

            // محاكاة التقدم
            let progress = 0;
            const progressBar = document.getElementById('progressBar');
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 100) progress = 100;

                progressBar.style.width = progress + '%';

                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        bsModal.hide();
                        modal.remove();
                        showToast(`تم توليد تقرير ${type} بصيغة ${format} بنجاح`, 'success');
                    }, 500);
                }
            }, 200);
        }

        // تصدير جميع التقارير
        function exportAllReports() {
            showToast('سيتم تصدير جميع التقارير قريباً', 'info');
        }

        // إظهار رسالة Toast
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast position-fixed top-0 end-0 m-3`;
            toast.innerHTML = `
                <div class="toast-header">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info-circle'} text-${type === 'error' ? 'danger' : type} me-2"></i>
                    <strong class="me-auto">${type === 'success' ? 'نجح' : type === 'error' ? 'خطأ' : 'معلومات'}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            `;

            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            toast.addEventListener('hidden.bs.toast', function() {
                toast.remove();
            });
        }
    </script>
</body>
</html>
