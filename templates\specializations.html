<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التخصصات - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap');
        
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: 'Cairo', 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
            color: #1e293b;
            overflow-x: hidden;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 24px;
            padding: 3rem;
            margin: 2rem auto;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px) saturate(180%);
        }
        
        .page-header {
            background: linear-gradient(135deg, 
                rgba(139, 92, 246, 0.1) 0%, 
                rgba(59, 130, 246, 0.1) 100%);
            color: #1e293b;
            border: 2px solid rgba(139, 92, 246, 0.2);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: 
                0 10px 25px rgba(139, 92, 246, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .specialization-card {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 8px 25px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }
        
        .specialization-card:hover {
            transform: translateY(-5px);
            box-shadow: 
                0 15px 35px rgba(139, 92, 246, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 1);
            border-color: rgba(139, 92, 246, 0.3);
        }
        
        .specialization-icon {
            width: 80px;
            height: 80px;
            border-radius: 18px;
            background: linear-gradient(135deg, #8b5cf6, #3b82f6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 20px rgba(139, 92, 246, 0.25);
        }
        
        .action-btn {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            color: #8b5cf6;
            border-radius: 12px;
            padding: 0.6rem 1.2rem;
            transition: all 0.3s ease;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
        }
        
        .action-btn:hover {
            background: linear-gradient(135deg, #8b5cf6, #3b82f6);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
        }
    </style>
</head>
<body>
    {% include 'includes/navbar.html' %}

    <div class="container-fluid">
        <div class="main-container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="page-header">
                <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 800; font-family: 'Cairo', sans-serif;">
                    <i class="bi bi-bookmark me-3" style="color: #8b5cf6;"></i>
                    التخصصات العلمية
                </h1>
                <p class="mb-0 mt-3" style="font-size: 1.2rem; opacity: 0.9; font-family: 'Cairo', sans-serif;">
                    إدارة التخصصات العلمية والأكاديمية
                </p>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: #1e293b; font-weight: 700; font-family: 'Cairo', sans-serif;">
                        <i class="bi bi-list-ul me-2" style="color: #8b5cf6;"></i>
                        قائمة التخصصات
                    </h2>
                    <p class="text-muted mb-0">جميع التخصصات العلمية المتاحة</p>
                </div>
                <div class="d-flex gap-3">
                    <a href="{{ url_for('add_specialization') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة تخصص جديد
                    </a>
                </div>
            </div>

            {% if specializations %}
                <div class="row">
                    {% for specialization in specializations %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="specialization-card">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="specialization-icon">
                                            <i class="bi bi-bookmark"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h5 class="mb-1 fw-bold" style="color: #1e293b; font-family: 'Cairo', sans-serif;">{{ specialization.name }}</h5>
                                            <small class="text-muted">تخصص علمي</small>
                                        </div>
                                    </div>
                                    <div class="dropdown">
                                        <button class="action-btn btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeleteSpecialization('{{ specialization.name }}', {{ specialization.id }})">
                                                <i class="bi bi-trash me-2"></i>حذف
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex gap-2">
                                        {% set students_count = specialization.students|length if specialization.students else 0 %}
                                        {% set professors_count = specialization.professors|length if specialization.professors else 0 %}
                                        
                                        {% if students_count > 0 %}
                                            <span class="badge bg-primary rounded-pill">{{ students_count }} طالب</span>
                                        {% endif %}
                                        
                                        {% if professors_count > 0 %}
                                            <span class="badge bg-success rounded-pill">{{ professors_count }} أستاذ</span>
                                        {% endif %}
                                        
                                        {% if students_count == 0 and professors_count == 0 %}
                                            <span class="badge bg-secondary rounded-pill">لا يوجد منتسبون</span>
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">
                                        <i class="bi bi-calendar3 me-1"></i>
                                        {{ specialization.created_at.strftime('%Y-%m-%d') if specialization.created_at else 'غير محدد' }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <div class="specialization-icon mx-auto mb-4" style="width: 100px; height: 100px; font-size: 3rem; opacity: 0.5;">
                        <i class="bi bi-bookmark"></i>
                    </div>
                    <h3 class="mb-3" style="color: #1e293b; font-family: 'Cairo', sans-serif;">لا توجد تخصصات مسجلة</h3>
                    <p class="text-muted mb-4">لم يتم تسجيل أي تخصصات في النظام بعد</p>
                    <a href="{{ url_for('add_specialization') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة تخصص جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        // تأكيد حذف التخصص
        function confirmDeleteSpecialization(specializationName, specializationId) {
            if (confirm(`هل أنت متأكد من حذف التخصص "${specializationName}"؟\nسيتم حذف جميع البيانات المرتبطة به.`)) {
                fetch(`/admin/specializations/delete/${specializationId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        response.json().then(data => {
                            alert(data.message || 'حدث خطأ أثناء الحذف');
                        });
                    }
                });
            }
        }
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
