<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كشف درجات الطالب - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
        }

        .main-container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem auto;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .page-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(111, 66, 193, 0.3);
            text-align: center;
        }

        @media print {
            .no-print { display: none !important; }
            .card { border: none !important; box-shadow: none !important; }
            .table { font-size: 12px; }
            .badge { color: #000 !important; background-color: transparent !important; border: 1px solid #000 !important; }
            body { font-size: 12px; background: white !important; padding-top: 0 !important; }
            h1, h2, h3, h4, h5, h6 { color: #000 !important; }
            .main-container { background: white !important; box-shadow: none !important; margin: 0 !important; }
        }
        .table th { background-color: #f8f9fa; font-weight: 600; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
            <div class="page-header no-print">
                <h1 class="mb-0">
                    <i class="bi bi-file-earmark-text me-3"></i>
                    كشف درجات الطالب
                </h1>
                <p class="mb-0 mt-2">عرض وطباعة كشف درجات الطلبة</p>
            </div>

        <!-- فلاتر الاختيار -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="student_id" class="form-label">اختر الطالب</label>
                                <select class="form-select" id="student_id" name="student_id" onchange="this.form.submit()">
                                    <option value="">اختر الطالب</option>
                                    {% for student in students %}
                                    <option value="{{ student.id }}" {% if student.id|string == selected_student %}selected{% endif %}>
                                        {{ student.name }} - {{ student.program.name if student.program else 'غير محدد' }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        {% if student %}
        <!-- كشف الدرجات -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <!-- رأس الكشف -->
                    <div class="card-header text-center py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <h2 class="mb-1">كشف درجات الطالب</h2>
                        <h4 class="mb-0">نظام إدارة الدراسات العليا</h4>
                    </div>
                    
                    <div class="card-body">
                        <!-- معلومات الطالب -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="30%" class="fw-bold">اسم الطالب:</td>
                                        <td>{{ student.name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">البرنامج الدراسي:</td>
                                        <td>{{ student.program.name if student.program else 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">نوع البرنامج:</td>
                                        <td>{{ student.program.program_type if student.program else 'غير محدد' }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="30%" class="fw-bold">التخصص:</td>
                                        <td>{{ student.specialization.name if student.specialization else 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">العام الدراسي:</td>
                                        <td>{{ student.academic_year.name if student.academic_year else 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">تاريخ الطباعة:</td>
                                        <td id="print-date"></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <!-- الدرجات حسب السنوات والفصول -->
                        {% if transcript_data %}
                            {% for year_name, semesters in transcript_data.items() %}
                            <div class="mb-4">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="bi bi-calendar3 me-2"></i>{{ year_name }}
                                </h5>
                                
                                {% for semester_name, grades in semesters.items() %}
                                <div class="mb-3">
                                    <h6 class="text-secondary">{{ semester_name }}</h6>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-primary">
                                                <tr>
                                                    <th width="5%">#</th>
                                                    <th width="35%">اسم المادة</th>
                                                    <th width="10%">الوحدات</th>
                                                    <th width="12%">درجة السعي</th>
                                                    <th width="15%">درجة الامتحان النهائي</th>
                                                    <th width="13%">الدرجة الكاملة</th>
                                                    <th width="10%">التقدير</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for grade in grades %}
                                                <tr>
                                                    <td>{{ loop.index }}</td>
                                                    <td>{{ grade.subject.name if grade.subject else 'غير محدد' }}</td>
                                                    <td class="text-center">{{ grade.subject.units if grade.subject else 0 }}</td>
                                                    <td class="text-center">{{ grade.midterm_grade }}</td>
                                                    <td class="text-center">{{ grade.final_grade }}</td>
                                                    <td class="text-center fw-bold">{{ grade.total_grade }}</td>
                                                    <td class="text-center">
                                                        {% set grade_colors = {
                                                            'امتياز': 'success',
                                                            'جيد جداً': 'info',
                                                            'جيد': 'primary',
                                                            'مقبول': 'warning',
                                                            'ضعيف': 'danger'
                                                        } %}
                                                        <span class="badge bg-{{ grade_colors.get(grade.grade_letter, 'secondary') }}">
                                                            {{ grade.grade_letter }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                            <tfoot class="table-light">
                                                <tr>
                                                    <td colspan="2" class="fw-bold">المعدل الفصلي:</td>
                                                    <td class="text-center fw-bold">
                                                        {% set semester_units = grades|sum(attribute='subject.units') %}
                                                        {{ semester_units }}
                                                    </td>
                                                    <td colspan="3"></td>
                                                    <td class="text-center fw-bold text-primary">
                                                        {% set semester_points = 0 %}
                                                        {% set total_units = 0 %}
                                                        {% for grade in grades %}
                                                            {% set semester_points = semester_points + (grade.total_grade * grade.subject.units) %}
                                                            {% set total_units = total_units + grade.subject.units %}
                                                        {% endfor %}
                                                        {% set semester_gpa = (semester_points / total_units) if total_units > 0 else 0 %}
                                                        {{ "%.2f"|format(semester_gpa) }}
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% endfor %}
                            
                            <!-- المعدلات النهائية -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title text-primary">معدل السنة التحضيرية</h5>
                                            <h3 class="text-success">{{ "%.2f"|format(preparatory_gpa) }}</h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title text-primary">المعدل التراكمي النهائي</h5>
                                            <h3 class="text-success">{{ "%.2f"|format(cumulative_gpa) }}</h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-file-earmark-x text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا توجد درجات مسجلة</h4>
                            <p class="text-muted">لم يتم إدخال أي درجات لهذا الطالب بعد</p>
                        </div>
                        {% endif %}
                        
                        <!-- التوقيعات -->
                        <div class="row mt-5">
                            <div class="col-md-4 text-center">
                                <div class="border-top pt-3">
                                    <strong>توقيع المسؤول</strong>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="border-top pt-3">
                                    <strong>ختم القسم</strong>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="border-top pt-3">
                                    <strong>توقيع العميد</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% else %}
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-person-x text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">لم يتم اختيار طالب</h4>
                        <p class="text-muted">يرجى اختيار طالب من القائمة أعلاه لعرض كشف الدرجات</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث تاريخ الطباعة
        document.getElementById('print-date').textContent = new Date().toLocaleDateString('ar-IQ');
    </script>
</body>
</html>
