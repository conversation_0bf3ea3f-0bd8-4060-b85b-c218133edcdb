<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبة - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .page-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
        }
        .student-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            background: white;
            margin-bottom: 1.5rem;
        }
        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        .student-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
        }
        .student-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-graduated { background: #d1ecf1; color: #0c5460; }
        .animate-on-load {
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .stats-mini {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.5rem;
            text-align: center;
            margin: 0.2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- عنوان الصفحة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="page-header animate-on-load">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-6 mb-2 fw-bold">
                                <i class="bi bi-people me-3"></i>إدارة الطلبة
                            </h1>
                            <p class="lead mb-0">إدارة شاملة لجميع طلبة الدراسات العليا في النظام</p>
                            <div class="mt-3">
                                <span class="badge bg-light text-dark me-2">
                                    <i class="bi bi-person-check me-1"></i>{{ students|length }} طالب مسجل
                                </span>
                                <span class="badge bg-light text-dark">
                                    <i class="bi bi-mortarboard me-1"></i>دراسات عليا
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('add_student') }}" class="btn btn-light btn-lg">
                                <i class="bi bi-person-plus me-2"></i>إضافة طالب جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم البحث والفلترة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="filter-section animate-on-load">
                    <div class="row align-items-end g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label fw-bold">
                                <i class="bi bi-search me-2"></i>البحث السريع
                            </label>
                            <input type="text" class="form-control search-box" id="search"
                                   placeholder="البحث بالاسم، الرقم الجامعي، أو البرنامج...">
                        </div>
                        <div class="col-md-3">
                            <label for="status_filter" class="form-label fw-bold">
                                <i class="bi bi-funnel me-2"></i>حالة الطالب
                            </label>
                            <select class="form-select" id="status_filter">
                                <option value="">جميع الحالات</option>
                                <option value="مستمر بالدراسة">مستمر بالدراسة</option>
                                <option value="خريج">خريج</option>
                                    <option value="ترقين قيد">ترقين قيد</option>
                                    <option value="منقول">منقول</option>
                                    <option value="منسحب">منسحب</option>
                                    <option value="تأجيل">تأجيل</option>
                                </select>
                        </div>
                        <div class="col-md-3">
                            <label for="gender_filter" class="form-label fw-bold">
                                <i class="bi bi-gender-ambiguous me-2"></i>الجنس
                            </label>
                            <select class="form-select" id="gender_filter">
                                <option value="">الكل</option>
                                <option value="ذكر">ذكر</option>
                                <option value="أنثى">أنثى</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="showCardView()">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary active" onclick="showTableView()">
                                    <i class="bi bi-table"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض البطاقات -->
        <div id="cardView" class="row" style="display: none;">
            {% if students %}
                {% for student in students %}
                <div class="col-lg-4 col-md-6 mb-4 student-item animate-on-load"
                     data-name="{{ student.name|lower }}"
                     data-status="{{ student.status|lower if student.status else '' }}"
                     data-gender="{{ student.gender|lower if student.gender else '' }}"
                     data-program="{{ student.program.name|lower if student.program else '' }}">
                    <div class="student-card position-relative">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-start mb-3">
                                <div class="student-avatar me-3">
                                    {{ student.name.split()[0][0] if student.name else 'ط' }}
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="card-title fw-bold mb-1">{{ student.name }}</h5>
                                    <p class="text-muted small mb-2">
                                        <i class="bi bi-hash me-1"></i>{{ student.university_id or 'غير محدد' }}
                                    </p>
                                    {% if student.status %}
                                    <span class="status-badge status-{{ 'active' if student.status == 'مستمر بالدراسة' else 'inactive' }}">
                                        {{ student.status }}
                                    </span>
                                    {% endif %}
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ url_for('edit_student', id=student.id) }}">
                                            <i class="bi bi-pencil me-2"></i>تعديل
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ url_for('student_transcript', student_id=student.id) }}">
                                            <i class="bi bi-file-text me-2"></i>كشف الدرجات
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"
                                               onclick="confirmDelete('{{ student.name }}', {{ student.id }})">
                                            <i class="bi bi-trash me-2"></i>حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <div class="stats-mini">
                                        <i class="bi bi-gender-{{ 'male' if student.gender == 'ذكر' else 'female' }} text-primary"></i>
                                        <div class="small">{{ student.gender or 'غير محدد' }}</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stats-mini">
                                        <i class="bi bi-calendar3 text-success"></i>
                                        <div class="small">{{ student.academic_year.name if student.academic_year else 'غير محدد' }}</div>
                                    </div>
                                </div>
                            </div>

                            {% if student.program %}
                            <div class="mb-2">
                                <small class="text-muted">البرنامج الأكاديمي:</small>
                                <div class="fw-bold">{{ student.program.name }}</div>
                            </div>
                            {% endif %}

                            {% if student.specialization %}
                            <div class="mb-2">
                                <small class="text-muted">التخصص:</small>
                                <div class="fw-bold">{{ student.specialization.name }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">لا يوجد طلبة مسجلين</h4>
                        <p class="text-muted">ابدأ بإضافة طالب جديد</p>
                        <a href="{{ url_for('add_student') }}" class="btn btn-primary">
                            <i class="bi bi-person-plus me-2"></i>إضافة طالب جديد
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- عرض الجدول -->
        <div id="tableView" class="row">
            <div class="col-12">
                <div class="card shadow animate-on-load">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-table me-2"></i>قائمة الطلبة
                            <span class="badge bg-primary ms-2">{{ students|length }}</span>
                        </h6>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                                <i class="bi bi-file-earmark-excel me-1"></i>تصدير Excel
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="printTable()">
                                <i class="bi bi-printer me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if students %}
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="studentsTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%" class="text-center">#</th>
                                        <th width="25%">
                                            <i class="bi bi-person me-2"></i>بيانات الطالب
                                        </th>
                                        <th width="15%">
                                            <i class="bi bi-journal-bookmark me-2"></i>البرنامج
                                        </th>
                                        <th width="15%">
                                            <i class="bi bi-bookmark me-2"></i>التخصص
                                        </th>
                                        <th width="10%" class="text-center">
                                            <i class="bi bi-check-circle me-2"></i>الحالة
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-graph-up me-2"></i>الأداء
                                        </th>
                                        <th width="15%" class="text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for student in students %}
                                    <tr class="student-row"
                                        data-name="{{ student.name|lower }}"
                                        data-status="{{ student.status|lower if student.status else '' }}"
                                        data-gender="{{ student.gender|lower if student.gender else '' }}"
                                        data-program="{{ student.program.name|lower if student.program else '' }}">
                                        <td class="text-center fw-bold text-muted">{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="student-avatar me-3" style="width: 45px; height: 45px; font-size: 1.2rem;">
                                                    {{ student.name.split()[0][0] if student.name else 'ط' }}
                                                </div>
                                                <div>
                                                    <h6 class="mb-1 fw-bold">{{ student.name }}</h6>
                                                    <div class="d-flex gap-2">
                                                        <small class="text-muted">
                                                            <i class="bi bi-hash me-1"></i>{{ student.university_id or 'غير محدد' }}
                                                        </small>
                                                        <small class="text-muted">
                                                            <i class="bi bi-gender-{{ 'male' if student.gender == 'ذكر' else 'female' }} me-1"></i>{{ student.gender or 'غير محدد' }}
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if student.program %}
                                            <div>
                                                <h6 class="mb-1 fw-bold">{{ student.program.name }}</h6>
                                                <span class="badge bg-light text-dark">{{ student.program.program_type }}</span>
                                            </div>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if student.specialization %}
                                            <span class="fw-bold">{{ student.specialization.name }}</span>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            {% set status_colors = {
                                                'مستمر بالدراسة': 'success',
                                                'خريج': 'primary',
                                                'ترقين قيد': 'warning',
                                                'منقول': 'info',
                                                'منسحب': 'danger',
                                                'تأجيل': 'secondary'
                                            } %}
                                            {% if student.status %}
                                            <span class="badge bg-{{ status_colors.get(student.status, 'secondary') }} rounded-pill">
                                                {{ student.status }}
                                            </span>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            {% set grade_count = student.grades|length %}
                                            {% if grade_count > 0 %}
                                            <div class="d-flex justify-content-center align-items-center gap-2">
                                                <span class="badge bg-info rounded-pill">{{ grade_count }} مادة</span>
                                                {% set avg_grade = student.grades|map(attribute='total_grade')|select|list %}
                                                {% if avg_grade %}
                                                <span class="badge bg-success rounded-pill">
                                                    {{ "%.1f"|format(avg_grade|sum / avg_grade|length) }}
                                                </span>
                                                {% endif %}
                                            </div>
                                            {% else %}
                                            <span class="text-muted small">لا توجد درجات</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('student_transcript', student_id=student.id) }}"
                                                   class="btn btn-sm btn-outline-info"
                                                   data-bs-toggle="tooltip"
                                                   title="كشف الدرجات">
                                                    <i class="bi bi-file-earmark-text"></i>
                                                </a>
                                                <a href="{{ url_for('edit_student', id=student.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   data-bs-toggle="tooltip"
                                                   title="تعديل البيانات">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف الطالب"
                                                        onclick="confirmDelete('{{ student.name }}', {{ student.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا يوجد طلبة مسجلون</h4>
                            <p class="text-muted">ابدأ بإضافة طالب جديد</p>
                            <a href="{{ url_for('add_student') }}" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>إضافة طالب جديد
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسينات تفاعلية لصفحة الطلبة
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل المتدرج
            const animatedElements = document.querySelectorAll('.animate-on-load');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });

            // تفعيل tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // إعداد الفلاتر
            const searchInput = document.getElementById('search');
            const statusFilter = document.getElementById('status_filter');
            const genderFilter = document.getElementById('gender_filter');

            // ربط أحداث الفلترة
            if (searchInput) searchInput.addEventListener('input', filterStudents);
            if (statusFilter) statusFilter.addEventListener('change', filterStudents);
            if (genderFilter) genderFilter.addEventListener('change', filterStudents);

            // تأثيرات hover للبطاقات
            const studentCards = document.querySelectorAll('.student-card');
            studentCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // دالة الفلترة المحسنة
        function filterStudents() {
            const searchTerm = document.getElementById('search').value.toLowerCase();
            const statusValue = document.getElementById('status_filter').value;
            const genderValue = document.getElementById('gender_filter').value;

            // فلترة البطاقات
            const cardView = document.getElementById('cardView');
            const studentItems = cardView.querySelectorAll('.student-item');
            let visibleCards = 0;

            studentItems.forEach(item => {
                const name = item.getAttribute('data-name');
                const status = item.getAttribute('data-status');
                const gender = item.getAttribute('data-gender');
                const program = item.getAttribute('data-program');

                const matchesSearch = name.includes(searchTerm) || program.includes(searchTerm);
                const matchesStatus = !statusValue || status.includes(statusValue.toLowerCase());
                const matchesGender = !genderValue || gender.includes(genderValue.toLowerCase());

                if (matchesSearch && matchesStatus && matchesGender) {
                    item.style.display = 'block';
                    item.classList.add('animate-on-load');
                    visibleCards++;
                } else {
                    item.style.display = 'none';
                }
            });

            // فلترة الجدول
            const tableView = document.getElementById('tableView');
            const tableRows = tableView.querySelectorAll('.student-row');
            let visibleRows = 0;

            tableRows.forEach(row => {
                const name = row.getAttribute('data-name');
                const status = row.getAttribute('data-status');
                const gender = row.getAttribute('data-gender');
                const program = row.getAttribute('data-program');

                const matchesSearch = name.includes(searchTerm) || program.includes(searchTerm);
                const matchesStatus = !statusValue || status.includes(statusValue.toLowerCase());
                const matchesGender = !genderValue || gender.includes(genderValue.toLowerCase());

                if (matchesSearch && matchesStatus && matchesGender) {
                    row.style.display = 'table-row';
                    visibleRows++;
                } else {
                    row.style.display = 'none';
                }
            });

            // تحديث عداد النتائج
            updateResultsCounter(Math.max(visibleCards, visibleRows));
        }

        // تحديث عداد النتائج
        function updateResultsCounter(count) {
            const badges = document.querySelectorAll('.badge.bg-primary');
            badges.forEach(badge => {
                if (badge.textContent.includes('طالب')) {
                    badge.textContent = count;
                }
            });
        }

        // تبديل العرض بين البطاقات والجدول
        function showCardView() {
            document.getElementById('cardView').style.display = 'flex';
            document.getElementById('tableView').style.display = 'none';

            // تحديث الأزرار
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function showTableView() {
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('tableView').style.display = 'block';

            // تحديث الأزرار
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // دالة تأكيد الحذف المحسنة
        function confirmDelete(name, id) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>تأكيد حذف الطالب
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <i class="bi bi-person-x text-danger" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">هل أنت متأكد من حذف الطالب؟</h5>
                            <p class="text-muted">${name}</p>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> سيتم حذف جميع الدرجات والبيانات المرتبطة بهذا الطالب.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteStudent(${id})">
                                <i class="bi bi-trash me-2"></i>حذف نهائياً
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                modal.remove();
            });
        }

        // دالة الحذف الفعلي
        function deleteStudent(id) {
            const loadingToast = document.createElement('div');
            loadingToast.className = 'toast position-fixed top-0 end-0 m-3';
            loadingToast.innerHTML = `
                <div class="toast-header">
                    <div class="spinner-border spinner-border-sm text-primary me-2"></div>
                    <strong class="me-auto">جاري حذف الطالب...</strong>
                </div>
            `;
            document.body.appendChild(loadingToast);

            const bsToast = new bootstrap.Toast(loadingToast);
            bsToast.show();

            setTimeout(() => {
                window.location.href = '/admin/students/delete/' + id;
            }, 1000);
        }

        // دوال التصدير والطباعة
        function exportToExcel() {
            // تنفيذ تصدير Excel
            alert('سيتم تنفيذ تصدير Excel قريباً');
        }

        function printTable() {
            window.print();
        }
                    
                    if (matchesSearch && matchesStatus && matchesGender) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            searchInput.addEventListener('keyup', filterTable);
            statusFilter.addEventListener('change', filterTable);
            genderFilter.addEventListener('change', filterTable);
        });

        function confirmDelete(name) {
            if (confirm('هل أنت متأكد من حذف الطالب: ' + name + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                alert('تم الحذف بنجاح');
            }
        }
        
        // تفعيل tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        .bg-pink {
            background-color: #e91e63 !important;
        }
    </style>
</body>
</html>
