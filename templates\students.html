<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبة - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
            color: #1e293b;
            overflow-x: hidden;
        }

        /* خلفية ديناميكية */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 30%, #6366f1 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, #8b5cf6 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, #06b6d4 0%, transparent 50%),
                linear-gradient(135deg, #1e1b4b 0%, #0f172a 100%);
            z-index: -2;
            animation: backgroundShift 25s ease-in-out infinite;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse"><path d="M 8 0 L 0 0 0 8" fill="none" stroke="%23ffffff" stroke-width="0.3" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
            opacity: 0.4;
        }

        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            50% { filter: hue-rotate(30deg) brightness(1.1); }
        }

        .main-container {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.9) 0%,
                rgba(30, 27, 75, 0.9) 100%);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 32px;
            padding: 3rem;
            margin: 2rem auto;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(24px) saturate(180%);
        }

        .page-header {
            background: linear-gradient(135deg,
                rgba(16, 185, 129, 0.9) 0%,
                rgba(6, 182, 212, 0.9) 100%);
            color: white;
            border-radius: 24px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow:
                0 20px 40px rgba(16, 185, 129, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.1),
                transparent);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }
        .student-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            margin-bottom: 1.5rem;
            position: relative;
        }
        .student-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        /* تحسين الـ Accordion */
        .accordion-item {
            border: 1px solid rgba(0,0,0,0.1);
            border-radius: 15px !important;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .accordion-item:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            transform: translateY(-2px);
        }

        .accordion-button {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            padding: 1.5rem;
            font-weight: 600;
            border-radius: 15px !important;
        }

        .accordion-button:not(.collapsed) {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: none;
        }

        .accordion-button:focus {
            box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
        }

        .accordion-button::after {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
            transition: transform 0.3s ease;
        }

        .accordion-button:not(.collapsed)::after {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
            transform: rotate(180deg);
        }

        .accordion-body {
            border-top: 1px solid rgba(0,0,0,0.1);
        }

        .table-responsive {
            border-radius: 0 0 15px 15px;
        }
        .student-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }
        .student-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-graduated { background: #d1ecf1; color: #0c5460; }
        .animate-on-load {
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .stats-mini {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.5rem;
            text-align: center;
            margin: 0.2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="bi bi-house-fill me-1"></i>الرئيسية
                        </a>
                    </li>

                    {% if current_user.user_type == 'admin' %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear-fill me-1"></i>الإدارة
                        </a>
                        <ul class="dropdown-menu">
                            <li><h6 class="dropdown-header">المؤسسات التعليمية</h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('universities') }}">
                                <i class="bi bi-building me-2"></i>الجامعات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('colleges') }}">
                                <i class="bi bi-bank me-2"></i>الكليات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('departments') }}">
                                <i class="bi bi-diagram-3 me-2"></i>الأقسام
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('specializations') }}">
                                <i class="bi bi-bookmark me-2"></i>التخصصات
                            </a></li>

                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">الأشخاص</h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('students') }}">
                                <i class="bi bi-people me-2"></i>الطلبة
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('professors') }}">
                                <i class="bi bi-person-workspace me-2"></i>الأساتذة
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('users') }}">
                                <i class="bi bi-person-gear me-2"></i>المستخدمون
                            </a></li>

                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">الأكاديمي</h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('programs') }}">
                                <i class="bi bi-journal-bookmark me-2"></i>البرامج الأكاديمية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('subjects') }}">
                                <i class="bi bi-book me-2"></i>المواد الدراسية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('grades') }}">
                                <i class="bi bi-clipboard-data me-2"></i>الدرجات
                            </a></li>

                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">الأعوام والفصول الدراسية</h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('academic_years') }}">
                                <i class="bi bi-calendar-range me-2"></i>الأعوام الدراسية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('semesters') }}">
                                <i class="bi bi-calendar3 me-2"></i>الفصول الدراسية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admission_channels') }}">
                                <i class="bi bi-door-open me-2"></i>قنوات القبول
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('student_transcript') }}">
                            <i class="bi bi-file-earmark-text-fill me-1"></i>التقارير
                        </a>
                    </li>
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>{{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- عنوان الصفحة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="page-header animate-on-load">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-6 mb-2 fw-bold">
                                <i class="bi bi-people me-3"></i>إدارة الطلبة
                            </h1>
                            <p class="lead mb-0">إدارة شاملة لجميع طلبة الدراسات العليا في النظام</p>
                            <div class="mt-3">
                                <span class="badge bg-light text-dark me-2">
                                    <i class="bi bi-person-check me-1"></i>{{ students|length }} طالب مسجل
                                </span>
                                <span class="badge bg-light text-dark">
                                    <i class="bi bi-mortarboard me-1"></i>دراسات عليا
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('add_student') }}" class="btn btn-light btn-lg">
                                <i class="bi bi-person-plus me-2"></i>إضافة طالب جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم البحث والفلترة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="filter-section animate-on-load">
                    <div class="row align-items-end g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label fw-bold">
                                <i class="bi bi-search me-2"></i>البحث السريع
                            </label>
                            <input type="text" class="form-control search-box" id="search"
                                   placeholder="البحث بالاسم، الرقم الجامعي، أو البرنامج...">
                        </div>
                        <div class="col-md-3">
                            <label for="status_filter" class="form-label fw-bold">
                                <i class="bi bi-funnel me-2"></i>حالة الطالب
                            </label>
                            <select class="form-select" id="status_filter">
                                <option value="">جميع الحالات</option>
                                <option value="مستمر بالدراسة">مستمر بالدراسة</option>
                                <option value="خريج">خريج</option>
                                    <option value="ترقين قيد">ترقين قيد</option>
                                    <option value="منقول">منقول</option>
                                    <option value="منسحب">منسحب</option>
                                    <option value="تأجيل">تأجيل</option>
                                </select>
                        </div>
                        <div class="col-md-3">
                            <label for="gender_filter" class="form-label fw-bold">
                                <i class="bi bi-gender-ambiguous me-2"></i>الجنس
                            </label>
                            <select class="form-select" id="gender_filter">
                                <option value="">الكل</option>
                                <option value="ذكر">ذكر</option>
                                <option value="أنثى">أنثى</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="showCardView()">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary active" onclick="showTableView()">
                                    <i class="bi bi-table"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض البطاقات -->
        <div id="cardView" class="row" style="display: none;">
            {% if students %}
                {% for student in students %}
                <div class="col-lg-4 col-md-6 mb-4 student-item animate-on-load"
                     data-name="{{ student.name|lower }}"
                     data-status="{{ student.status|lower if student.status else '' }}"
                     data-gender="{{ student.gender|lower if student.gender else '' }}"
                     data-program="{{ student.program.name|lower if student.program else '' }}">
                    <div class="student-card position-relative">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-start mb-3">
                                <div class="student-avatar me-3">
                                    {{ student.name.split()[0][0] if student.name else 'ط' }}
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="card-title fw-bold mb-1">{{ student.name }}</h5>
                                    <p class="text-muted small mb-2">
                                        <i class="bi bi-hash me-1"></i>{{ student.university_id or 'غير محدد' }}
                                    </p>
                                    {% if student.status %}
                                    <span class="status-badge status-{{ 'active' if student.status == 'مستمر بالدراسة' else 'inactive' }}">
                                        {{ student.status }}
                                    </span>
                                    {% endif %}
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ url_for('edit_student', id=student.id) }}">
                                            <i class="bi bi-pencil me-2"></i>تعديل
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ url_for('student_transcript', student_id=student.id) }}">
                                            <i class="bi bi-file-text me-2"></i>كشف الدرجات
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"
                                               onclick="confirmDelete('{{ student.name }}', {{ student.id }})">
                                            <i class="bi bi-trash me-2"></i>حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <div class="stats-mini">
                                        <i class="bi bi-gender-{{ 'male' if student.gender == 'ذكر' else 'female' }} text-primary"></i>
                                        <div class="small">{{ student.gender or 'غير محدد' }}</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stats-mini">
                                        <i class="bi bi-calendar3 text-success"></i>
                                        <div class="small">{{ student.academic_year.name if student.academic_year else 'غير محدد' }}</div>
                                    </div>
                                </div>
                            </div>

                            {% if student.program %}
                            <div class="mb-2">
                                <small class="text-muted">البرنامج الأكاديمي:</small>
                                <div class="fw-bold">{{ student.program.name }}</div>
                            </div>
                            {% endif %}

                            {% if student.specialization %}
                            <div class="mb-2">
                                <small class="text-muted">التخصص:</small>
                                <div class="fw-bold">{{ student.specialization.name }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">لا يوجد طلبة مسجلين</h4>
                        <p class="text-muted">ابدأ بإضافة طالب جديد</p>
                        <a href="{{ url_for('add_student') }}" class="btn btn-primary">
                            <i class="bi bi-person-plus me-2"></i>إضافة طالب جديد
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- عرض الجدول -->
        <div id="tableView" class="row">
            <div class="col-12">
                <div class="card shadow animate-on-load">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-table me-2"></i>قائمة الطلبة
                            <span class="badge bg-primary ms-2">{{ students|length }}</span>
                        </h6>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                                <i class="bi bi-file-earmark-excel me-1"></i>تصدير Excel
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="printTable()">
                                <i class="bi bi-printer me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        {% if students %}
                            {% set students_by_year_and_type = {} %}
                            {% for student in students %}
                                {% set year_name = student.academic_year.name if student.academic_year else 'غير محدد' %}
                                {% set study_type = student.study_type if student.study_type else 'غير محدد' %}
                                {% set key = year_name + '|' + study_type %}
                                {% if key not in students_by_year_and_type %}
                                    {% set _ = students_by_year_and_type.update({key: []}) %}
                                {% endif %}
                                {% set _ = students_by_year_and_type[key].append(student) %}
                            {% endfor %}

                            <div class="accordion" id="studentsAccordion">
                                {% for key, group_students in students_by_year_and_type.items() %}
                                    {% set year_name, study_type = key.split('|') %}
                                    <div class="accordion-item border-0 mb-3">
                                        <h2 class="accordion-header" id="heading{{ loop.index }}">
                                            <button class="accordion-button collapsed" type="button"
                                                    data-bs-toggle="collapse"
                                                    data-bs-target="#collapse{{ loop.index }}"
                                                    aria-expanded="false"
                                                    aria-controls="collapse{{ loop.index }}">
                                                <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-calendar-event me-3 fs-5 text-primary"></i>
                                                        <div>
                                                            <h5 class="mb-0 fw-bold">{{ year_name }}</h5>
                                                            <small class="text-muted">{{ study_type }}</small>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex align-items-center gap-3">
                                                        <span class="badge bg-primary rounded-pill fs-6">{{ group_students|length }} طالب</span>
                                                        {% set active_count = group_students|selectattr('status', 'equalto', 'مستمر بالدراسة')|list|length %}
                                                        {% if active_count > 0 %}
                                                            <span class="badge bg-success rounded-pill">{{ active_count }} نشط</span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </button>
                                        </h2>
                                        <div id="collapse{{ loop.index }}"
                                             class="accordion-collapse collapse"
                                             aria-labelledby="heading{{ loop.index }}"
                                             data-bs-parent="#studentsAccordion">
                                            <div class="accordion-body p-0">
                                                <div class="table-responsive">
                                                    <table class="table table-hover align-middle mb-0">
                                                        <thead class="table-light">
                                                            <tr>
                                                                <th width="5%" class="text-center">#</th>
                                                                <th width="25%">
                                                                    <i class="bi bi-person me-2"></i>بيانات الطالب
                                                                </th>
                                                                <th width="15%">
                                                                    <i class="bi bi-journal-bookmark me-2"></i>البرنامج
                                                                </th>
                                                                <th width="15%">
                                                                    <i class="bi bi-bookmark me-2"></i>التخصص
                                                                </th>
                                                                <th width="10%" class="text-center">
                                                                    <i class="bi bi-check-circle me-2"></i>الحالة
                                                                </th>
                                                                <th width="15%" class="text-center">
                                                                    <i class="bi bi-graph-up me-2"></i>الأداء
                                                                </th>
                                                                <th width="15%" class="text-center">الإجراءات</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% for student in group_students %}
                                    <tr class="student-row"
                                        data-name="{{ student.name|lower }}"
                                        data-status="{{ student.status|lower if student.status else '' }}"
                                        data-gender="{{ student.gender|lower if student.gender else '' }}"
                                        data-program="{{ student.program.name|lower if student.program else '' }}">
                                        <td class="text-center fw-bold text-muted">{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="student-avatar me-3" style="width: 45px; height: 45px; font-size: 1.2rem;">
                                                    {{ student.name.split()[0][0] if student.name else 'ط' }}
                                                </div>
                                                <div>
                                                    <h6 class="mb-1 fw-bold">{{ student.name }}</h6>
                                                    <div class="d-flex gap-2">
                                                        <small class="text-muted">
                                                            <i class="bi bi-hash me-1"></i>{{ student.university_id or 'غير محدد' }}
                                                        </small>
                                                        <small class="text-muted">
                                                            <i class="bi bi-gender-{{ 'male' if student.gender == 'ذكر' else 'female' }} me-1"></i>{{ student.gender or 'غير محدد' }}
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if student.program %}
                                            <div>
                                                <h6 class="mb-1 fw-bold">{{ student.program.name }}</h6>
                                                <span class="badge bg-light text-dark">{{ student.program.program_type }}</span>
                                            </div>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if student.specialization %}
                                            <span class="fw-bold">{{ student.specialization.name }}</span>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            {% set status_colors = {
                                                'مستمر بالدراسة': 'success',
                                                'خريج': 'primary',
                                                'ترقين قيد': 'warning',
                                                'منقول': 'info',
                                                'منسحب': 'danger',
                                                'تأجيل': 'secondary'
                                            } %}
                                            {% if student.status %}
                                            <span class="badge bg-{{ status_colors.get(student.status, 'secondary') }} rounded-pill">
                                                {{ student.status }}
                                            </span>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            {% set grade_count = student.grades|length %}
                                            {% if grade_count > 0 %}
                                            <div class="d-flex justify-content-center align-items-center gap-2">
                                                <span class="badge bg-info rounded-pill">{{ grade_count }} مادة</span>
                                                {% set avg_grade = student.grades|map(attribute='total_grade')|select|list %}
                                                {% if avg_grade %}
                                                <span class="badge bg-success rounded-pill">
                                                    {{ "%.1f"|format(avg_grade|sum / avg_grade|length) }}
                                                </span>
                                                {% endif %}
                                            </div>
                                            {% else %}
                                            <span class="text-muted small">لا توجد درجات</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('student_transcript', student_id=student.id) }}"
                                                   class="btn btn-sm btn-outline-info"
                                                   data-bs-toggle="tooltip"
                                                   title="كشف الدرجات">
                                                    <i class="bi bi-file-earmark-text"></i>
                                                </a>
                                                <a href="{{ url_for('edit_student', id=student.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   data-bs-toggle="tooltip"
                                                   title="تعديل البيانات">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف الطالب"
                                                        onclick="confirmDelete('{{ student.name }}', {{ student.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا يوجد طلبة مسجلون</h4>
                            <p class="text-muted">ابدأ بإضافة طالب جديد</p>
                            <a href="{{ url_for('add_student') }}" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>إضافة طالب جديد
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسينات تفاعلية لصفحة الطلبة
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل المتدرج
            const animatedElements = document.querySelectorAll('.animate-on-load');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });

            // تفعيل tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // إعداد الفلاتر
            const searchInput = document.getElementById('search');
            const statusFilter = document.getElementById('status_filter');
            const genderFilter = document.getElementById('gender_filter');

            // ربط أحداث الفلترة
            if (searchInput) searchInput.addEventListener('input', filterStudents);
            if (statusFilter) statusFilter.addEventListener('change', filterStudents);
            if (genderFilter) genderFilter.addEventListener('change', filterStudents);

            // تأثيرات hover للبطاقات
            const studentCards = document.querySelectorAll('.student-card');
            studentCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // دالة الفلترة المحسنة
        function filterStudents() {
            const searchTerm = document.getElementById('search').value.toLowerCase();
            const statusValue = document.getElementById('status_filter').value;
            const genderValue = document.getElementById('gender_filter').value;

            // فلترة البطاقات
            const cardView = document.getElementById('cardView');
            const studentItems = cardView.querySelectorAll('.student-item');
            let visibleCards = 0;

            studentItems.forEach(item => {
                const name = item.getAttribute('data-name');
                const status = item.getAttribute('data-status');
                const gender = item.getAttribute('data-gender');
                const program = item.getAttribute('data-program');

                const matchesSearch = name.includes(searchTerm) || program.includes(searchTerm);
                const matchesStatus = !statusValue || status.includes(statusValue.toLowerCase());
                const matchesGender = !genderValue || gender.includes(genderValue.toLowerCase());

                if (matchesSearch && matchesStatus && matchesGender) {
                    item.style.display = 'block';
                    item.classList.add('animate-on-load');
                    visibleCards++;
                } else {
                    item.style.display = 'none';
                }
            });

            // فلترة الجدول
            const tableView = document.getElementById('tableView');
            const tableRows = tableView.querySelectorAll('.student-row');
            let visibleRows = 0;

            tableRows.forEach(row => {
                const name = row.getAttribute('data-name');
                const status = row.getAttribute('data-status');
                const gender = row.getAttribute('data-gender');
                const program = row.getAttribute('data-program');

                const matchesSearch = name.includes(searchTerm) || program.includes(searchTerm);
                const matchesStatus = !statusValue || status.includes(statusValue.toLowerCase());
                const matchesGender = !genderValue || gender.includes(genderValue.toLowerCase());

                if (matchesSearch && matchesStatus && matchesGender) {
                    row.style.display = 'table-row';
                    visibleRows++;
                } else {
                    row.style.display = 'none';
                }
            });

            // تحديث عداد النتائج
            updateResultsCounter(Math.max(visibleCards, visibleRows));
        }

        // تحديث عداد النتائج
        function updateResultsCounter(count) {
            const badges = document.querySelectorAll('.badge.bg-primary');
            badges.forEach(badge => {
                if (badge.textContent.includes('طالب')) {
                    badge.textContent = count;
                }
            });
        }

        // تبديل العرض بين البطاقات والجدول
        function showCardView() {
            document.getElementById('cardView').style.display = 'flex';
            document.getElementById('tableView').style.display = 'none';

            // تحديث الأزرار
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function showTableView() {
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('tableView').style.display = 'block';

            // تحديث الأزرار
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // دالة تأكيد الحذف المحسنة
        function confirmDelete(name, id) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>تأكيد حذف الطالب
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <i class="bi bi-person-x text-danger" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">هل أنت متأكد من حذف الطالب؟</h5>
                            <p class="text-muted">${name}</p>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> سيتم حذف جميع الدرجات والبيانات المرتبطة بهذا الطالب.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteStudent(${id})">
                                <i class="bi bi-trash me-2"></i>حذف نهائياً
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                modal.remove();
            });
        }

        // دالة الحذف الفعلي
        function deleteStudent(id) {
            console.log('Attempting to delete student with ID:', id);

            // إغلاق النافذة المنبثقة أولاً
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
            if (modal) {
                modal.hide();
            }

            // عرض رسالة التحميل
            showNotification('جاري حذف الطالب...', 'info');

            // إرسال طلب الحذف
            fetch(`/admin/students/delete/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('Response status:', response.status);

                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(data => {
                console.log('Response data:', data);

                if (data.success) {
                    showNotification(data.message || 'تم حذف الطالب بنجاح', 'success');

                    // إعادة تحميل الصفحة بعد ثانيتين
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showNotification(data.message || 'حدث خطأ أثناء الحذف', 'error');
                }
            })
            .catch(error => {
                console.error('Delete error:', error);
                showNotification('حدث خطأ في الاتصال: ' + error.message, 'error');
            });
        }

        // دالة عرض الإشعارات
        function showNotification(message, type) {
            // إزالة الإشعارات السابقة
            const existingNotifications = document.querySelectorAll('.custom-notification');
            existingNotifications.forEach(notif => notif.remove());

            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : type === 'info' ? 'info' : 'danger'} alert-dismissible fade show position-fixed custom-notification`;
            notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 350px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';

            let icon = 'exclamation-triangle';
            if (type === 'success') icon = 'check-circle';
            else if (type === 'info') icon = 'info-circle';

            notification.innerHTML = `
                <i class="bi bi-${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // إزالة الإشعار تلقائياً بعد 5 ثوان
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // دوال التصدير والطباعة
        function exportToExcel() {
            // تنفيذ تصدير Excel
            alert('سيتم تنفيذ تصدير Excel قريباً');
        }

        function printTable() {
            window.print();
        }
                    
                    if (matchesSearch && matchesStatus && matchesGender) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            searchInput.addEventListener('keyup', filterTable);
            statusFilter.addEventListener('change', filterTable);
            genderFilter.addEventListener('change', filterTable);
        });


        
        // تفعيل tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // تحسين الـ accordion
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات للـ accordion
            const accordionButtons = document.querySelectorAll('.accordion-button');
            accordionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // إضافة تأثير صوتي (اختياري)
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // إضافة عداد للطلبة في كل مجموعة
            const accordionItems = document.querySelectorAll('.accordion-item');
            accordionItems.forEach((item, index) => {
                const collapseElement = item.querySelector('.accordion-collapse');
                const button = item.querySelector('.accordion-button');

                collapseElement.addEventListener('shown.bs.collapse', function() {
                    // تحديث العداد عند فتح القسم
                    const rows = this.querySelectorAll('tbody tr');
                    console.log(`تم فتح قسم ${index + 1} - يحتوي على ${rows.length} طالب`);
                });
            });

            // إضافة إمكانية فتح/إغلاق جميع الأقسام
            const toggleAllBtn = document.createElement('button');
            toggleAllBtn.className = 'btn btn-outline-primary btn-sm mb-3';
            toggleAllBtn.innerHTML = '<i class="bi bi-arrows-expand me-2"></i>فتح جميع الأقسام';
            toggleAllBtn.onclick = function() {
                const allCollapses = document.querySelectorAll('.accordion-collapse');
                const isExpanded = this.innerHTML.includes('فتح');

                allCollapses.forEach(collapse => {
                    if (isExpanded) {
                        new bootstrap.Collapse(collapse, {show: true});
                    } else {
                        new bootstrap.Collapse(collapse, {hide: true});
                    }
                });

                this.innerHTML = isExpanded ?
                    '<i class="bi bi-arrows-collapse me-2"></i>إغلاق جميع الأقسام' :
                    '<i class="bi bi-arrows-expand me-2"></i>فتح جميع الأقسام';
            };

            // إضافة الزر قبل الـ accordion
            const accordion = document.getElementById('studentsAccordion');
            if (accordion) {
                accordion.parentNode.insertBefore(toggleAllBtn, accordion);
            }
        });
    </script>
        </div>
    </div>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        .bg-pink {
            background-color: #e91e63 !important;
        }
    </style>
</body>
</html>
