<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المواد الدراسية - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: '<PERSON><PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
        }

        .main-container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem auto;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .page-header {
            background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(253, 126, 20, 0.3);
            text-align: center;
        }
        .subject-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            background: white;
            margin-bottom: 1.5rem;
            position: relative;
        }
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        .subject-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        .subject-card.mandatory::before {
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .subject-card.elective::before {
            background: linear-gradient(90deg, #f093fb, #f5576c);
        }
        .subject-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        .subject-icon.mandatory {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .subject-icon.elective {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            border-color: #f093fb;
            box-shadow: 0 0 0 0.2rem rgba(240, 147, 251, 0.25);
        }
        .stats-mini {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.8rem;
            text-align: center;
            margin: 0.2rem;
        }
        .animate-on-load {
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .units-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.9);
            color: #333;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- عنوان الصفحة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="page-header animate-on-load">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-6 mb-2 fw-bold">
                                <i class="bi bi-book me-3"></i>المواد الدراسية
                            </h1>
                            <p class="lead mb-0">إدارة شاملة لجميع المواد والمقررات الدراسية في النظام</p>
                            <div class="mt-3">
                                <span class="badge bg-light text-dark me-2">
                                    <i class="bi bi-book me-1"></i>{{ subjects|length }} مادة
                                </span>
                                <span class="badge bg-light text-dark me-2">
                                    <i class="bi bi-people me-1"></i>
                                    {% set total_students = 0 %}
                                    {% for subject in subjects %}
                                        {% set total_students = total_students + subject.grades|length %}
                                    {% endfor %}
                                    {{ total_students }} طالب مسجل
                                </span>
                                <span class="badge bg-light text-dark">
                                    <i class="bi bi-hash me-1"></i>
                                    {% set total_units = 0 %}
                                    {% for subject in subjects %}
                                        {% set total_units = total_units + (subject.units or 0) %}
                                    {% endfor %}
                                    {{ total_units }} وحدة إجمالية
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('add_subject') }}" class="btn btn-light btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>إضافة مادة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم البحث والفلترة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="filter-section animate-on-load">
                    <div class="row align-items-end g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label fw-bold">
                                <i class="bi bi-search me-2"></i>البحث السريع
                            </label>
                            <input type="text" class="form-control search-box" id="search"
                                   placeholder="البحث بالاسم، البرنامج، أو الوصف...">
                        </div>
                        <div class="col-md-2">
                            <label for="type_filter" class="form-label fw-bold">
                                <i class="bi bi-funnel me-2"></i>نوع المادة
                            </label>
                            <select class="form-select" id="type_filter">
                                <option value="">جميع الأنواع</option>
                                <option value="اجباري">إجباري</option>
                                <option value="اختياري">اختياري</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="year_filter" class="form-label fw-bold">
                                <i class="bi bi-calendar3 me-2"></i>السنة الدراسية
                            </label>
                            <select class="form-select" id="year_filter">
                                <option value="">جميع السنوات</option>
                                <option value="التحضيرية">التحضيرية</option>
                                <option value="البحثية">البحثية</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="units_filter" class="form-label fw-bold">
                                <i class="bi bi-hash me-2"></i>عدد الوحدات
                            </label>
                            <select class="form-select" id="units_filter">
                                <option value="">جميع الوحدات</option>
                                <option value="1">وحدة واحدة</option>
                                <option value="2">وحدتان</option>
                                <option value="3">ثلاث وحدات</option>
                                    <option value="4">أربع وحدات</option>
                                    <option value="6">ست وحدات</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary active" onclick="showCardView()">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="showTableView()">
                                    <i class="bi bi-table"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض البطاقات -->
        <div id="cardView" class="row">
            {% if subjects %}
                {% for subject in subjects %}
                <div class="col-lg-4 col-md-6 mb-4 subject-item animate-on-load"
                     data-name="{{ subject.name|lower }}"
                     data-type="{{ subject.subject_type|lower if subject.subject_type else '' }}"
                     data-year="{{ subject.academic_year_type|lower if subject.academic_year_type else '' }}"
                     data-units="{{ subject.units or 0 }}"
                     data-program="{{ subject.program.name|lower if subject.program else '' }}">
                    <div class="subject-card {{ subject.subject_type|lower if subject.subject_type else 'mandatory' }} position-relative">
                        <div class="units-badge">
                            <i class="bi bi-hash me-1"></i>{{ subject.units or 0 }} وحدة
                        </div>
                        <div class="card-body p-4">
                            <div class="d-flex align-items-start mb-3">
                                <div class="subject-icon {{ subject.subject_type|lower if subject.subject_type else 'mandatory' }} me-3">
                                    <i class="bi bi-book"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="card-title fw-bold mb-1">{{ subject.name }}</h5>
                                    <p class="text-muted small mb-2">
                                        <i class="bi bi-journal-bookmark me-1"></i>{{ subject.program.name if subject.program else 'غير محدد' }}
                                    </p>
                                    <div class="d-flex gap-2 mb-2">
                                        {% if subject.subject_type %}
                                        <span class="badge bg-{{ 'primary' if subject.subject_type == 'اجباري' else 'warning' }} rounded-pill">
                                            {{ subject.subject_type }}
                                        </span>
                                        {% endif %}
                                        {% if subject.academic_year_type %}
                                        <span class="badge bg-info rounded-pill">{{ subject.academic_year_type }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ url_for('edit_subject', id=subject.id) }}">
                                            <i class="bi bi-pencil me-2"></i>تعديل
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ url_for('grades') }}?subject={{ subject.id }}">
                                            <i class="bi bi-clipboard-data me-2"></i>الدرجات
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"
                                               onclick="confirmDelete('{{ subject.name }}', {{ subject.id }})">
                                            <i class="bi bi-trash me-2"></i>حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <div class="stats-mini">
                                        <h6 class="fw-bold text-primary mb-1">{{ subject.grades|length }}</h6>
                                        <small class="text-muted">طالب مسجل</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stats-mini">
                                        <h6 class="fw-bold text-success mb-1">{{ subject.hours or subject.units or 0 }}</h6>
                                        <small class="text-muted">ساعة أسبوعية</small>
                                    </div>
                                </div>
                            </div>

                            {% if subject.description %}
                            <p class="card-text text-muted small">{{ subject.description[:80] }}{% if subject.description|length > 80 %}...{% endif %}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-book text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">لا توجد مواد دراسية</h4>
                        <p class="text-muted">ابدأ بإضافة مادة جديدة</p>
                        <a href="{{ url_for('add_subject') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>إضافة مادة جديدة
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- عرض الجدول -->
        <div id="tableView" class="row" style="display: none;">
            <div class="col-12">
                <div class="card shadow animate-on-load">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-table me-2"></i>قائمة المواد الدراسية
                            <span class="badge bg-primary ms-2">{{ subjects|length }}</span>
                        </h6>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                                <i class="bi bi-file-earmark-excel me-1"></i>تصدير Excel
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="printTable()">
                                <i class="bi bi-printer me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if subjects %}
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="subjectsTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%" class="text-center">#</th>
                                        <th width="30%">
                                            <i class="bi bi-book me-2"></i>المادة الدراسية
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-hash me-2"></i>الوحدات والساعات
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-bookmark me-2"></i>التصنيف
                                        </th>
                                        <th width="20%">
                                            <i class="bi bi-journal-bookmark me-2"></i>البرنامج
                                        </th>
                                        <th width="15%" class="text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subject in subjects %}
                                    <tr class="subject-row"
                                        data-name="{{ subject.name|lower }}"
                                        data-type="{{ subject.subject_type|lower if subject.subject_type else '' }}"
                                        data-year="{{ subject.academic_year_type|lower if subject.academic_year_type else '' }}"
                                        data-units="{{ subject.units or 0 }}"
                                        data-program="{{ subject.program.name|lower if subject.program else '' }}">
                                        <td class="text-center fw-bold text-muted">{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="subject-icon {{ subject.subject_type|lower if subject.subject_type else 'mandatory' }} me-3"
                                                     style="width: 45px; height: 45px; font-size: 1.2rem;">
                                                    <i class="bi bi-book"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1 fw-bold">{{ subject.name }}</h6>
                                                    {% if subject.description %}
                                                    <small class="text-muted">{{ subject.description[:50] }}{% if subject.description|length > 50 %}...{% endif %}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex justify-content-center gap-2">
                                                <span class="badge bg-primary rounded-pill" title="الوحدات">
                                                    {{ subject.units or 0 }} وحدة
                                                </span>
                                                <span class="badge bg-success rounded-pill" title="الساعات">
                                                    {{ subject.hours or subject.units or 0 }} ساعة
                                                </span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex flex-column gap-1">
                                                {% if subject.subject_type %}
                                                <span class="badge bg-{{ 'primary' if subject.subject_type == 'اجباري' else 'warning' }} rounded-pill">
                                                    {{ subject.subject_type }}
                                                </span>
                                                {% endif %}
                                                {% if subject.academic_year_type %}
                                                <span class="badge bg-info rounded-pill">
                                                    {{ subject.academic_year_type }}
                                                </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            {% if subject.program %}
                                            <div>
                                                <h6 class="mb-1 fw-bold">{{ subject.program.name }}</h6>
                                                <span class="badge bg-light text-dark">{{ subject.program.program_type }}</span>
                                            </div>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#subjectModal{{ subject.id }}"
                                                        data-bs-toggle="tooltip"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <a href="{{ url_for('edit_subject', id=subject.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   data-bs-toggle="tooltip"
                                                   title="تعديل المادة">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف المادة"
                                                        onclick="confirmDelete('{{ subject.name }}', {{ subject.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Modal لعرض تفاصيل المادة -->
                                    <div class="modal fade" id="subjectModal{{ subject.id }}" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تفاصيل المادة: {{ subject.name }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">اسم المادة:</td>
                                                                    <td>{{ subject.name }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الوحدات:</td>
                                                                    <td>{{ subject.units }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الساعات:</td>
                                                                    <td>{{ subject.hours }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">نوع المادة:</td>
                                                                    <td>{{ subject.subject_type or 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">السنة الدراسية:</td>
                                                                    <td>{{ subject.academic_year_type or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">البرنامج:</td>
                                                                    <td>{{ subject.program.name if subject.program else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">نوع البرنامج:</td>
                                                                    <td>{{ subject.program.program_type if subject.program else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الطلبة المسجلين:</td>
                                                                    <td>{{ subject.grades|length }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    
                                                    {% if subject.description %}
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <h6>وصف المادة:</h6>
                                                            <p class="text-muted">{{ subject.description }}</p>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-book text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا توجد مواد دراسية مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة مادة دراسية جديدة</p>
                            <a href="{{ url_for('add_subject') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إضافة مادة جديدة
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تفعيل الفلاتر
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');
            const typeFilter = document.getElementById('type_filter');
            const yearFilter = document.getElementById('year_filter');
            const unitsFilter = document.getElementById('units_filter');
            const table = document.getElementById('subjectsTable');
            
            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const typeValue = typeFilter.value;
                const yearValue = yearFilter.value;
                const unitsValue = unitsFilter.value;
                
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const name = row.cells[1].textContent.toLowerCase();
                    const units = row.cells[2].textContent.trim();
                    const type = row.cells[4].textContent.trim();
                    const year = row.cells[5].textContent.trim();
                    
                    const matchesSearch = name.includes(searchTerm);
                    const matchesType = !typeValue || type.includes(typeValue);
                    const matchesYear = !yearValue || year.includes(yearValue);
                    const matchesUnits = !unitsValue || units.includes(unitsValue);
                    
                    if (matchesSearch && matchesType && matchesYear && matchesUnits) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            searchInput.addEventListener('keyup', filterTable);
            typeFilter.addEventListener('change', filterTable);
            yearFilter.addEventListener('change', filterTable);
            unitsFilter.addEventListener('change', filterTable);
        });

        // تحسينات تفاعلية
        const animatedElements = document.querySelectorAll('.animate-on-load');
        animatedElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.1}s`;
        });

        // تفعيل tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // تأثيرات hover للبطاقات
        const subjectCards = document.querySelectorAll('.subject-card');
        subjectCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // تبديل العرض بين البطاقات والجدول
        function showCardView() {
            document.getElementById('cardView').style.display = 'flex';
            document.getElementById('tableView').style.display = 'none';

            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function showTableView() {
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('tableView').style.display = 'block';

            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // دالة تأكيد الحذف المحسنة
        function confirmDelete(name, id) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>تأكيد حذف المادة
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <i class="bi bi-book-x text-danger" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">هل أنت متأكد من حذف المادة؟</h5>
                            <p class="text-muted">${name}</p>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> سيتم حذف جميع الدرجات المرتبطة بهذه المادة.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteSubject(${id})">
                                <i class="bi bi-trash me-2"></i>حذف نهائياً
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                modal.remove();
            });
        }

        // دالة الحذف الفعلي
        function deleteSubject(id) {
            window.location.href = '/admin/subjects/delete/' + id;
        }

        // دوال التصدير والطباعة
        function exportToExcel() {
            alert('سيتم تنفيذ تصدير Excel قريباً');
        }

        function printTable() {
            window.print();
        }
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
