<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المواد الدراسية - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap');

        body {
            background: #0a0a0a;
            font-family: 'Cairo', 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* خلفية ديناميكية للمواد الدراسية */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 25% 25%, #ef4444 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #8b5cf6 0%, transparent 50%),
                radial-gradient(circle at 50% 10%, #06b6d4 0%, transparent 50%),
                radial-gradient(circle at 10% 80%, #10b981 0%, transparent 50%),
                linear-gradient(135deg, #1e1b4b 0%, #0f172a 100%);
            z-index: -2;
            animation: backgroundShift 35s ease-in-out infinite;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
            opacity: 0.3;
        }

        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            25% { filter: hue-rotate(90deg) brightness(1.1); }
            50% { filter: hue-rotate(180deg) brightness(0.9); }
            75% { filter: hue-rotate(270deg) brightness(1.1); }
        }

        .main-container {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.95) 0%,
                rgba(30, 27, 75, 0.95) 100%);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 32px;
            padding: 3rem;
            margin: 2rem auto;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(24px) saturate(180%);
        }

        .page-header {
            background: linear-gradient(135deg,
                rgba(239, 68, 68, 0.9) 0%,
                rgba(139, 92, 246, 0.9) 50%,
                rgba(6, 182, 212, 0.9) 100%);
            color: white;
            border-radius: 24px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow:
                0 20px 40px rgba(239, 68, 68, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.15),
                transparent);
            animation: shine 4s ease-in-out infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: 100%; }
        }
        /* بطاقات المواد الدراسية */
        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .subject-card {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.8) 0%,
                rgba(30, 27, 75, 0.8) 100%);
            border: 1px solid rgba(239, 68, 68, 0.2);
            border-radius: 24px;
            padding: 2rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            margin-bottom: 0;
        }

        .subject-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ef4444, #8b5cf6, #06b6d4, #10b981);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .subject-card:hover::before {
            transform: scaleX(1);
        }

        .subject-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(239, 68, 68, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            border-color: rgba(239, 68, 68, 0.4);
        }

        .subject-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            background: linear-gradient(135deg, #ef4444, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 24px rgba(239, 68, 68, 0.3);
        }

        .subject-type-badge {
            background: linear-gradient(135deg, #ef4444, #8b5cf6);
            color: white;
            border-radius: 12px;
            padding: 0.4rem 0.8rem;
            font-weight: 600;
            font-size: 0.85rem;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .units-badge {
            background: linear-gradient(135deg, #06b6d4, #10b981);
            color: white;
            border-radius: 12px;
            padding: 0.4rem 0.8rem;
            font-weight: 600;
            font-size: 0.85rem;
            box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
        }
        .action-btn {
            background: linear-gradient(135deg,
                rgba(239, 68, 68, 0.1) 0%,
                rgba(139, 92, 246, 0.1) 100%);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.6rem 1.2rem;
            transition: all 0.3s ease;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
        }

        .action-btn:hover {
            background: linear-gradient(135deg, #ef4444, #8b5cf6);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(239, 68, 68, 0.4);
        }

        .filter-tabs {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 16px;
            padding: 1rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .filter-tab {
            background: transparent;
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ffffff;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #ef4444, #8b5cf6);
            color: white;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .filter-tab:hover {
            background: rgba(239, 68, 68, 0.2);
            transform: translateY(-2px);
        }
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            border-color: #f093fb;
            box-shadow: 0 0 0 0.2rem rgba(240, 147, 251, 0.25);
        }
        .stats-mini {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.8rem;
            text-align: center;
            margin: 0.2rem;
        }
        .animate-on-load {
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .units-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.9);
            color: #333;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- عنوان الصفحة المحسن -->
            <div class="page-header">
                <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 800; font-family: 'Cairo', sans-serif;">
                    <i class="bi bi-book me-3"></i>
                    المواد الدراسية والمقررات
                </h1>
                <p class="mb-0 mt-3" style="font-size: 1.2rem; opacity: 0.9; font-family: 'Cairo', sans-serif;">
                    إدارة شاملة للمواد الدراسية والمقررات الأكاديمية
                </p>
            </div>

            <!-- إحصائيات المواد -->
            <div class="stats-container">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">{{ subjects|length }}</div>
                            <div class="stat-label">مادة دراسية</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">
                                {% set total_students = 0 %}
                                {% for subject in subjects %}
                                    {% set total_students = total_students + subject.grades|length %}
                                {% endfor %}
                                {{ total_students }}
                            </div>
                            <div class="stat-label">طالب مسجل</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">
                                {% set total_units = 0 %}
                                {% for subject in subjects %}
                                    {% set total_units = total_units + (subject.units or 0) %}
                                {% endfor %}
                                {{ total_units }}
                            </div>
                            <div class="stat-label">وحدة دراسية</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">
                                {% set mandatory_count = subjects|selectattr('type', 'equalto', 'إجباري')|list|length %}
                                {{ mandatory_count }}
                            </div>
                            <div class="stat-label">مادة إجبارية</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: #ffffff; font-weight: 700; font-family: 'Cairo', sans-serif;">
                        <i class="bi bi-journal-bookmark me-2" style="color: #ef4444;"></i>
                        قائمة المواد الدراسية
                    </h2>
                    <p class="text-muted mb-0">جميع المواد والمقررات المسجلة في النظام</p>
                </div>
                <div class="d-flex gap-3">
                    <button class="action-btn" onclick="showAllSubjects()">
                        <i class="bi bi-grid me-2"></i>عرض الكل
                    </button>
                    <a href="{{ url_for('add_subject') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة مادة جديدة
                    </a>
                </div>
            </div>

            <!-- فلاتر المواد -->
            <div class="filter-tabs">
                <div class="d-flex flex-wrap align-items-center">
                    <span class="me-3 text-white fw-bold" style="font-family: 'Cairo', sans-serif;">تصفية حسب النوع:</span>
                    <button class="filter-tab active" onclick="filterByType('all')">جميع المواد</button>
                    <button class="filter-tab" onclick="filterByType('إجباري')">المواد الإجبارية</button>
                    <button class="filter-tab" onclick="filterByType('اختياري')">المواد الاختيارية</button>
                    <button class="filter-tab" onclick="filterByType('عملي')">المواد العملية</button>
                    <button class="filter-tab" onclick="filterByType('نظري')">المواد النظرية</button>
                </div>
            </div>

                <!-- تجميع المواد حسب البرنامج -->
                {% set subjects_by_program = {} %}
                {% for subject in subjects %}
                    {% set program_name = subject.program.name if subject.program else 'غير محدد' %}
                    {% if program_name not in subjects_by_program %}
                        {% set _ = subjects_by_program.update({program_name: []}) %}
                    {% endif %}
                    {% set _ = subjects_by_program[program_name].append(subject) %}
                {% endfor %}

                {% for program_name, program_subjects in subjects_by_program.items() %}
                    <div class="program-section mb-5" data-program="{{ program_subjects[0].program.id if program_subjects[0].program else 'none' }}">
                        <h3 class="mb-4" style="color: #ef4444; font-weight: 700; border-bottom: 2px solid rgba(239, 68, 68, 0.3); padding-bottom: 0.5rem; font-family: 'Cairo', sans-serif;">
                            <i class="bi bi-mortarboard me-2"></i>{{ program_name }}
                            <span class="badge bg-primary ms-2">{{ program_subjects|length }} مادة</span>
                        </h3>

                        <div class="subjects-grid">
                                <div class="subject-card" data-type="{{ subject.subject_type or '' }}" data-year="{{ subject.academic_year_type or '' }}">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="subject-icon">
                                                <i class="bi bi-book"></i>
                                            </div>
                                            <div class="ms-3">
                                                <h5 class="mb-1 fw-bold text-white" style="font-family: 'Cairo', sans-serif;">{{ subject.name }}</h5>
                                                <small class="text-muted">{{ subject.code or 'لا يوجد رمز' }}</small>
                                            </div>
                                        </div>
                                        <div class="dropdown">
                                            <button class="action-btn btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url_for('edit_subject', id=subject.id) }}">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('grades') }}?subject={{ subject.id }}">
                                                    <i class="bi bi-clipboard-data me-2"></i>الدرجات
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDeleteSubject('{{ subject.name }}', {{ subject.id }})">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>

                                    {% if subject.description %}
                                        <div class="mb-3">
                                            <small class="text-muted">الوصف:</small>
                                            <p class="text-white mb-0" style="font-family: 'Cairo', sans-serif;">{{ subject.description }}</p>
                                        </div>
                                    {% endif %}

                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">نوع المادة:</small>
                                            <div class="subject-type-badge mt-1">{{ subject.subject_type or 'غير محدد' }}</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">عدد الوحدات:</small>
                                            <div class="units-badge mt-1">{{ subject.units or 0 }} وحدة</div>
                                        </div>
                                    </div>

                                    {% if subject.academic_year_type %}
                                        <div class="mb-3">
                                            <small class="text-muted">السنة الدراسية:</small>
                                            <div class="text-white fw-bold" style="font-family: 'Cairo', sans-serif;">{{ subject.academic_year_type }}</div>
                                        </div>
                                    {% endif %}

                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex gap-2">
                                            {% set students_count = subject.grades|length if subject.grades else 0 %}
                                            {% if students_count > 0 %}
                                                <span class="badge bg-success rounded-pill">{{ students_count }} طالب</span>
                                            {% else %}
                                                <span class="badge bg-secondary rounded-pill">لا يوجد طلاب</span>
                                            {% endif %}

                                            {% if subject.professor %}
                                                <span class="badge bg-info rounded-pill">{{ subject.professor.name }}</span>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar3 me-1"></i>
                                            {{ subject.created_at.strftime('%Y-%m-%d') if subject.created_at else 'غير محدد' }}
                                        </small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
            {% else %}
                <div class="text-center py-5">
                    <div class="subject-icon mx-auto mb-4" style="width: 100px; height: 100px; font-size: 3rem; opacity: 0.5;">
                        <i class="bi bi-book"></i>
                    </div>
                    <h3 class="text-white mb-3" style="font-family: 'Cairo', sans-serif;">لا توجد مواد دراسية مسجلة</h3>
                    <p class="text-muted mb-4">لم يتم تسجيل أي مواد دراسية في النظام بعد</p>
                    <a href="{{ url_for('add_subject') }}" class="action-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة مادة جديدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        // فلترة المواد حسب النوع
        function filterByType(type) {
            const cards = document.querySelectorAll('.subject-card');
            const tabs = document.querySelectorAll('.filter-tab');

            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));

            // إضافة الفئة النشطة للتبويب المحدد
            event.target.classList.add('active');

            cards.forEach(card => {
                if (type === 'all' || card.dataset.type === type) {
                    card.style.display = 'block';
                    card.style.animation = 'slideInUp 0.5s ease-out';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // عرض جميع المواد
        function showAllSubjects() {
            const cards = document.querySelectorAll('.subject-card');
            const tabs = document.querySelectorAll('.filter-tab');

            // إزالة الفئة النشطة من جميع التبويبات
            tabs.forEach(tab => tab.classList.remove('active'));
            tabs[0].classList.add('active'); // تفعيل تبويب "جميع المواد"

            cards.forEach((card, index) => {
                card.style.display = 'block';
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        }

        // تأكيد حذف المادة
        function confirmDeleteSubject(subjectName, subjectId) {
            if (confirm(`هل أنت متأكد من حذف المادة "${subjectName}"؟\nسيتم حذف جميع البيانات المرتبطة بها.`)) {
                fetch(`/admin/subjects/delete/${subjectId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }

        // تحسين الرسوم المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.subject-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-on-load');
            });
        });

        // رسوم متحركة
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-load {
                animation: slideInUp 0.6s ease-out;
                animation-fill-mode: both;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-book text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">لا توجد مواد دراسية</h4>
                        <p class="text-muted">ابدأ بإضافة مادة جديدة</p>
                        <a href="{{ url_for('add_subject') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>إضافة مادة جديدة
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- عرض الجدول -->
        <div id="tableView" class="row" style="display: none;">
            <div class="col-12">
                <div class="card shadow animate-on-load">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-table me-2"></i>قائمة المواد الدراسية
                            <span class="badge bg-primary ms-2">{{ subjects|length }}</span>
                        </h6>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                                <i class="bi bi-file-earmark-excel me-1"></i>تصدير Excel
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="printTable()">
                                <i class="bi bi-printer me-1"></i>طباعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if subjects %}
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="subjectsTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%" class="text-center">#</th>
                                        <th width="30%">
                                            <i class="bi bi-book me-2"></i>المادة الدراسية
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-hash me-2"></i>الوحدات والساعات
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-bookmark me-2"></i>التصنيف
                                        </th>
                                        <th width="20%">
                                            <i class="bi bi-journal-bookmark me-2"></i>البرنامج
                                        </th>
                                        <th width="15%" class="text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subject in subjects %}
                                    <tr class="subject-row"
                                        data-name="{{ subject.name|lower }}"
                                        data-type="{{ subject.subject_type|lower if subject.subject_type else '' }}"
                                        data-year="{{ subject.academic_year_type|lower if subject.academic_year_type else '' }}"
                                        data-units="{{ subject.units or 0 }}"
                                        data-program="{{ subject.program.name|lower if subject.program else '' }}">
                                        <td class="text-center fw-bold text-muted">{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="subject-icon {{ subject.subject_type|lower if subject.subject_type else 'mandatory' }} me-3"
                                                     style="width: 45px; height: 45px; font-size: 1.2rem;">
                                                    <i class="bi bi-book"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1 fw-bold">{{ subject.name }}</h6>
                                                    {% if subject.description %}
                                                    <small class="text-muted">{{ subject.description[:50] }}{% if subject.description|length > 50 %}...{% endif %}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex justify-content-center gap-2">
                                                <span class="badge bg-primary rounded-pill" title="الوحدات">
                                                    {{ subject.units or 0 }} وحدة
                                                </span>
                                                <span class="badge bg-success rounded-pill" title="الساعات">
                                                    {{ subject.hours or subject.units or 0 }} ساعة
                                                </span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex flex-column gap-1">
                                                {% if subject.subject_type %}
                                                <span class="badge bg-{{ 'primary' if subject.subject_type == 'اجباري' else 'warning' }} rounded-pill">
                                                    {{ subject.subject_type }}
                                                </span>
                                                {% endif %}
                                                {% if subject.academic_year_type %}
                                                <span class="badge bg-info rounded-pill">
                                                    {{ subject.academic_year_type }}
                                                </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            {% if subject.program %}
                                            <div>
                                                <h6 class="mb-1 fw-bold">{{ subject.program.name }}</h6>
                                                <span class="badge bg-light text-dark">{{ subject.program.program_type }}</span>
                                            </div>
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#subjectModal{{ subject.id }}"
                                                        data-bs-toggle="tooltip"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <a href="{{ url_for('edit_subject', id=subject.id) }}"
                                                   class="btn btn-sm btn-outline-primary"
                                                   data-bs-toggle="tooltip"
                                                   title="تعديل المادة">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف المادة"
                                                        onclick="confirmDelete('{{ subject.name }}', {{ subject.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Modal لعرض تفاصيل المادة -->
                                    <div class="modal fade" id="subjectModal{{ subject.id }}" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تفاصيل المادة: {{ subject.name }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">اسم المادة:</td>
                                                                    <td>{{ subject.name }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الوحدات:</td>
                                                                    <td>{{ subject.units }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الساعات:</td>
                                                                    <td>{{ subject.hours }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">نوع المادة:</td>
                                                                    <td>{{ subject.subject_type or 'غير محدد' }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <table class="table table-borderless">
                                                                <tr>
                                                                    <td class="fw-bold">السنة الدراسية:</td>
                                                                    <td>{{ subject.academic_year_type or 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">البرنامج:</td>
                                                                    <td>{{ subject.program.name if subject.program else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">نوع البرنامج:</td>
                                                                    <td>{{ subject.program.program_type if subject.program else 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="fw-bold">عدد الطلبة المسجلين:</td>
                                                                    <td>{{ subject.grades|length }}</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    
                                                    {% if subject.description %}
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <h6>وصف المادة:</h6>
                                                            <p class="text-muted">{{ subject.description }}</p>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-book text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا توجد مواد دراسية مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة مادة دراسية جديدة</p>
                            <a href="{{ url_for('add_subject') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إضافة مادة جديدة
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تفعيل الفلاتر
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');
            const typeFilter = document.getElementById('type_filter');
            const yearFilter = document.getElementById('year_filter');
            const unitsFilter = document.getElementById('units_filter');
            const table = document.getElementById('subjectsTable');
            
            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const typeValue = typeFilter.value;
                const yearValue = yearFilter.value;
                const unitsValue = unitsFilter.value;
                
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const name = row.cells[1].textContent.toLowerCase();
                    const units = row.cells[2].textContent.trim();
                    const type = row.cells[4].textContent.trim();
                    const year = row.cells[5].textContent.trim();
                    
                    const matchesSearch = name.includes(searchTerm);
                    const matchesType = !typeValue || type.includes(typeValue);
                    const matchesYear = !yearValue || year.includes(yearValue);
                    const matchesUnits = !unitsValue || units.includes(unitsValue);
                    
                    if (matchesSearch && matchesType && matchesYear && matchesUnits) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            searchInput.addEventListener('keyup', filterTable);
            typeFilter.addEventListener('change', filterTable);
            yearFilter.addEventListener('change', filterTable);
            unitsFilter.addEventListener('change', filterTable);
        });

        // تحسينات تفاعلية
        const animatedElements = document.querySelectorAll('.animate-on-load');
        animatedElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.1}s`;
        });

        // تفعيل tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // تأثيرات hover للبطاقات
        const subjectCards = document.querySelectorAll('.subject-card');
        subjectCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // تبديل العرض بين البطاقات والجدول
        function showCardView() {
            document.getElementById('cardView').style.display = 'flex';
            document.getElementById('tableView').style.display = 'none';

            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function showTableView() {
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('tableView').style.display = 'block';

            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // دالة تأكيد الحذف المحسنة
        function confirmDelete(name, id) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>تأكيد حذف المادة
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <i class="bi bi-book-x text-danger" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">هل أنت متأكد من حذف المادة؟</h5>
                            <p class="text-muted">${name}</p>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> سيتم حذف جميع الدرجات المرتبطة بهذه المادة.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteSubject(${id})">
                                <i class="bi bi-trash me-2"></i>حذف نهائياً
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                modal.remove();
            });
        }

        // دالة الحذف الفعلي
        function deleteSubject(id) {
            window.location.href = '/admin/subjects/delete/' + id;
        }

        // دوال التصدير والطباعة
        function exportToExcel() {
            alert('سيتم تنفيذ تصدير Excel قريباً');
        }

        function printTable() {
            window.print();
        }
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
