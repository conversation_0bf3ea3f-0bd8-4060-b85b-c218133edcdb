<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المواد الدراسية - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            padding-top: 80px;
        }

        .main-container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem auto;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .page-header {
            background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(253, 126, 20, 0.3);
            text-align: center;
        }

        .subject-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            background: white;
            margin-bottom: 1.5rem;
            position: relative;
        }
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        .subject-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #fd7e14, #e83e8c);
            border-radius: 15px 15px 0 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="main-container">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- عنوان الصفحة -->
            <div class="page-header">
                <h1 class="display-6 mb-2 fw-bold">
                    <i class="bi bi-book me-3"></i>المواد الدراسية
                </h1>
                <p class="lead mb-0">إدارة شاملة لجميع المواد والمقررات الدراسية في النظام</p>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>قائمة المواد الدراسية</h2>
                <a href="{{ url_for('add_subject') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة مادة جديدة
                </a>
            </div>

            <!-- عرض المواد -->
            {% if subjects %}
                <div class="row">
                    {% for subject in subjects %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="subject-card">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-start mb-3">
                                    <div class="flex-grow-1">
                                        <h5 class="card-title fw-bold mb-1">{{ subject.name }}</h5>
                                        <p class="text-muted small mb-2">
                                            <i class="bi bi-journal-bookmark me-1"></i>{{ subject.program.name if subject.program else 'غير محدد' }}
                                        </p>
                                        <div class="d-flex gap-2 mb-2">
                                            {% if subject.subject_type %}
                                            <span class="badge bg-primary rounded-pill">{{ subject.subject_type }}</span>
                                            {% endif %}
                                            {% if subject.academic_year_type %}
                                            <span class="badge bg-info rounded-pill">{{ subject.academic_year_type }}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="{{ url_for('edit_subject', id=subject.id) }}">
                                                <i class="bi bi-pencil me-2"></i>تعديل
                                            </a></li>
                                            <li><a class="dropdown-item" href="{{ url_for('grades') }}?subject={{ subject.id }}">
                                                <i class="bi bi-clipboard-data me-2"></i>الدرجات
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete('{{ subject.name }}', {{ subject.id }})">
                                                <i class="bi bi-trash me-2"></i>حذف
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="row g-2 mb-3">
                                    <div class="col-6">
                                        <div class="text-center">
                                            <h6 class="fw-bold text-primary mb-1">{{ subject.grades|length }}</h6>
                                            <small class="text-muted">طالب مسجل</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-center">
                                            <h6 class="fw-bold text-success mb-1">{{ subject.units or 0 }}</h6>
                                            <small class="text-muted">وحدة دراسية</small>
                                        </div>
                                    </div>
                                </div>

                                {% if subject.description %}
                                <p class="card-text text-muted small">{{ subject.description[:80] }}{% if subject.description|length > 80 %}...{% endif %}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-book display-1 text-muted mb-3"></i>
                    <h3 class="text-muted">لا توجد مواد دراسية مسجلة</h3>
                    <p class="text-muted mb-4">لم يتم تسجيل أي مواد دراسية في النظام بعد</p>
                    <a href="{{ url_for('add_subject') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة مادة جديدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        function confirmDelete(subjectName, subjectId) {
            if (confirm(`هل أنت متأكد من حذف المادة "${subjectName}"؟`)) {
                fetch(`/admin/subjects/delete/${subjectId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
