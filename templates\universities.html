<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الجامعات - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        .university-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            background: white;
            margin-bottom: 1.5rem;
        }
        .university-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        .university-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-action {
            border-radius: 8px;
            padding: 0.5rem 1rem;
            margin: 0.2rem;
            transition: all 0.3s ease;
        }
        .btn-action:hover {
            transform: scale(1.05);
        }
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        .animate-on-load {
            animation: fadeInUp 0.8s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show animate-on-load" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- عنوان الصفحة المحسن -->
        <div class="row">
            <div class="col-12">
                <div class="page-header animate-on-load">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-6 mb-2 fw-bold">
                                <i class="bi bi-building me-3"></i>إدارة الجامعات
                            </h1>
                            <p class="lead mb-0">إدارة شاملة لجميع الجامعات والمؤسسات التعليمية في النظام</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('add_university') }}" class="btn btn-light btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>إضافة جامعة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم البحث والفلترة -->
        <div class="row">
            <div class="col-12">
                <div class="filter-section animate-on-load">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text bg-light border-0">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" class="form-control search-box border-start-0"
                                       id="searchInput" placeholder="البحث في الجامعات...">
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="showCardView()">
                                    <i class="bi bi-grid-3x3-gap me-1"></i>بطاقات
                                </button>
                                <button type="button" class="btn btn-outline-primary active" onclick="showTableView()">
                                    <i class="bi bi-table me-1"></i>جدول
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- عرض البطاقات -->
        <div id="cardView" class="row" style="display: none;">
            {% if universities %}
                {% for university in universities %}
                <div class="col-lg-4 col-md-6 mb-4 university-item animate-on-load"
                     data-name="{{ university.name_ar|lower }}"
                     data-address="{{ university.address|lower if university.address else '' }}">
                    <div class="university-card position-relative">
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="flex-grow-1">
                                    <h5 class="card-title fw-bold text-primary mb-2">
                                        <i class="bi bi-building me-2"></i>{{ university.name_ar }}
                                    </h5>
                                    {% if university.name_en %}
                                    <p class="text-muted small mb-2">{{ university.name_en }}</p>
                                    {% endif %}
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ url_for('edit_university', id=university.id) }}">
                                            <i class="bi bi-pencil me-2"></i>تعديل
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"
                                               onclick="confirmDelete('{{ university.name_ar }}', {{ university.id }})">
                                            <i class="bi bi-trash me-2"></i>حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            {% if university.address %}
                            <p class="card-text text-muted mb-3">
                                <i class="bi bi-geo-alt me-2"></i>{{ university.address }}
                            </p>
                            {% endif %}

                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stats-card">
                                        <h6 class="fw-bold text-primary mb-1">{{ university.colleges|length }}</h6>
                                        <small class="text-muted">كلية</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stats-card">
                                        <h6 class="fw-bold text-success mb-1">
                                            {% set total_departments = 0 %}
                                            {% for college in university.colleges %}
                                                {% set total_departments = total_departments + college.departments|length %}
                                            {% endfor %}
                                            {{ total_departments }}
                                        </h6>
                                        <small class="text-muted">قسم</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stats-card">
                                        <h6 class="fw-bold text-info mb-1">
                                            {% set total_programs = 0 %}
                                            {% for college in university.colleges %}
                                                {% for department in college.departments %}
                                                    {% set total_programs = total_programs + department.programs|length %}
                                                {% endfor %}
                                            {% endfor %}
                                            {{ total_programs }}
                                        </h6>
                                        <small class="text-muted">برنامج</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">لا توجد جامعات مسجلة</h4>
                        <p class="text-muted">ابدأ بإضافة جامعة جديدة</p>
                        <a href="{{ url_for('add_university') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>إضافة جامعة جديدة
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- عرض الجدول -->
        <div id="tableView" class="row">
            <div class="col-12">
                <div class="card shadow animate-on-load">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-table me-2"></i>قائمة الجامعات
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if universities %}
                        <div class="table-responsive">
                            <table class="table table-hover align-middle" id="universitiesTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%" class="text-center">#</th>
                                        <th width="25%">
                                            <i class="bi bi-building me-2"></i>اسم الجامعة
                                        </th>
                                        <th width="20%">
                                            <i class="bi bi-translate me-2"></i>الاسم الإنجليزي
                                        </th>
                                        <th width="20%">
                                            <i class="bi bi-geo-alt me-2"></i>العنوان
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-bar-chart me-2"></i>الإحصائيات
                                        </th>
                                        <th width="15%" class="text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for university in universities %}
                                    <tr class="university-row" data-name="{{ university.name_ar|lower }}"
                                        data-address="{{ university.address|lower if university.address else '' }}">
                                        <td class="text-center fw-bold text-muted">{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3"
                                                     style="width: 40px; height: 40px;">
                                                    <i class="bi bi-building text-white"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0 fw-bold">{{ university.name_ar }}</h6>
                                                    <small class="text-muted">جامعة</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ university.name_en or 'غير محدد' }}</span>
                                        </td>
                                        <td>
                                            {% if university.address %}
                                                <i class="bi bi-geo-alt text-muted me-1"></i>
                                                {{ university.address }}
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex justify-content-center gap-2">
                                                <span class="badge bg-primary rounded-pill" title="الكليات">
                                                    {{ university.colleges|length }}
                                                </span>
                                                <span class="badge bg-success rounded-pill" title="الأقسام">
                                                    {% set total_departments = 0 %}
                                                    {% for college in university.colleges %}
                                                        {% set total_departments = total_departments + college.departments|length %}
                                                    {% endfor %}
                                                    {{ total_departments }}
                                                </span>
                                                <span class="badge bg-info rounded-pill" title="البرامج">
                                                    {% set total_programs = 0 %}
                                                    {% for college in university.colleges %}
                                                        {% for department in college.departments %}
                                                            {% set total_programs = total_programs + department.programs|length %}
                                                        {% endfor %}
                                                    {% endfor %}
                                                    {{ total_programs }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('edit_university', id=university.id) }}"
                                                   class="btn btn-sm btn-outline-primary btn-action"
                                                   data-bs-toggle="tooltip"
                                                   title="تعديل الجامعة">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger btn-action"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف الجامعة"
                                                        onclick="confirmDelete('{{ university.name_ar }}', {{ university.id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا توجد جامعات مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة جامعة جديدة</p>
                            <a href="{{ url_for('add_university') }}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إضافة جامعة جديدة
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسينات تفاعلية لصفحة الجامعات
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل المتدرج
            const animatedElements = document.querySelectorAll('.animate-on-load');
            animatedElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });

            // تفعيل tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // البحث المباشر
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    filterUniversities(searchTerm);
                });
            }

            // تأثيرات hover للبطاقات
            const universityCards = document.querySelectorAll('.university-card');
            universityCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // دالة البحث والفلترة
        function filterUniversities(searchTerm) {
            const cardView = document.getElementById('cardView');
            const tableView = document.getElementById('tableView');

            // فلترة البطاقات
            const universityItems = cardView.querySelectorAll('.university-item');
            universityItems.forEach(item => {
                const name = item.getAttribute('data-name');
                const address = item.getAttribute('data-address');

                if (name.includes(searchTerm) || address.includes(searchTerm)) {
                    item.style.display = 'block';
                    item.classList.add('animate-on-load');
                } else {
                    item.style.display = 'none';
                }
            });

            // فلترة الجدول
            const tableRows = tableView.querySelectorAll('.university-row');
            tableRows.forEach(row => {
                const name = row.getAttribute('data-name');
                const address = row.getAttribute('data-address');

                if (name.includes(searchTerm) || address.includes(searchTerm)) {
                    row.style.display = 'table-row';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // تبديل العرض بين البطاقات والجدول
        function showCardView() {
            document.getElementById('cardView').style.display = 'flex';
            document.getElementById('tableView').style.display = 'none';

            // تحديث الأزرار
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function showTableView() {
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('tableView').style.display = 'block';

            // تحديث الأزرار
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // دالة تأكيد الحذف المحسنة
        function confirmDelete(name, id) {
            // إنشاء modal تأكيد مخصص
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>تأكيد الحذف
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <i class="bi bi-building text-danger" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">هل أنت متأكد من حذف الجامعة؟</h5>
                            <p class="text-muted">${name}</p>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع البيانات المرتبطة.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteUniversity(${id})">
                                <i class="bi bi-trash me-2"></i>حذف نهائياً
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // إزالة المودال بعد الإغلاق
            modal.addEventListener('hidden.bs.modal', function() {
                modal.remove();
            });
        }

        // دالة الحذف الفعلي
        function deleteUniversity(id) {
            // إظهار مؤشر التحميل
            const loadingToast = document.createElement('div');
            loadingToast.className = 'toast position-fixed top-0 end-0 m-3';
            loadingToast.innerHTML = `
                <div class="toast-header">
                    <div class="spinner-border spinner-border-sm text-primary me-2"></div>
                    <strong class="me-auto">جاري الحذف...</strong>
                </div>
            `;
            document.body.appendChild(loadingToast);

            const bsToast = new bootstrap.Toast(loadingToast);
            bsToast.show();

            // تنفيذ الحذف
            setTimeout(() => {
                window.location.href = '/admin/universities/delete/' + id;
            }, 1000);
        }

        // إضافة تأثيرات للأزرار
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-action')) {
                e.target.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    e.target.style.transform = 'scale(1)';
                }, 100);
            }
        });
    </script>
</body>
</html>
