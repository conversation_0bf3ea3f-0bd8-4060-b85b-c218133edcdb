<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام إدارة الدراسات العليا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-mortarboard-fill me-2"></i>نظام إدارة الدراسات العليا
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-people-fill me-2"></i>إدارة المستخدمين
                    </h1>
                    <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>إضافة مستخدم جديد
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-list-ul me-2"></i>قائمة المستخدمين
                            <span class="badge bg-primary ms-2">{{ users|length }}</span>
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if users %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="15%">اسم المستخدم</th>
                                        <th width="25%">الاسم الكامل</th>
                                        <th width="20%">البريد الإلكتروني</th>
                                        <th width="10%">نوع المستخدم</th>
                                        <th width="10%">الحالة</th>
                                        <th width="15%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in users %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-{{ 'danger' if user.user_type == 'admin' else 'primary' }} text-white rounded-circle">
                                                        {{ user.full_name[0] }}
                                                    </div>
                                                </div>
                                                <strong>{{ user.username }}</strong>
                                            </div>
                                        </td>
                                        <td>{{ user.full_name }}</td>
                                        <td>
                                            {% if user.email %}
                                            <i class="bi bi-envelope me-1"></i>{{ user.email }}
                                            {% else %}
                                            <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if user.user_type == 'admin' %}
                                            <span class="badge bg-danger">مدير</span>
                                            {% else %}
                                            <span class="badge bg-primary">مستخدم</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if user.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                            {% else %}
                                            <span class="badge bg-secondary">غير نشط</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#userModal{{ user.id }}"
                                                        title="عرض التفاصيل">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                {% if user.username != 'admin' %}
                                                <button class="btn btn-sm btn-outline-warning" 
                                                        onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})"
                                                        title="{{ 'تعطيل' if user.is_active else 'تفعيل' }}">
                                                    <i class="bi bi-{{ 'pause' if user.is_active else 'play' }}"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete('{{ user.username }}')"
                                                        title="حذف">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Modal لعرض تفاصيل المستخدم -->
                                    <div class="modal fade" id="userModal{{ user.id }}" tabindex="-1">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تفاصيل المستخدم: {{ user.username }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <table class="table table-borderless">
                                                        <tr>
                                                            <td class="fw-bold">اسم المستخدم:</td>
                                                            <td>{{ user.username }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">الاسم الكامل:</td>
                                                            <td>{{ user.full_name }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">البريد الإلكتروني:</td>
                                                            <td>{{ user.email or 'غير محدد' }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">رقم الهاتف:</td>
                                                            <td>{{ user.phone or 'غير محدد' }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">نوع المستخدم:</td>
                                                            <td>
                                                                {% if user.user_type == 'admin' %}
                                                                <span class="badge bg-danger">مدير النظام</span>
                                                                {% else %}
                                                                <span class="badge bg-primary">مستخدم عادي</span>
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">الحالة:</td>
                                                            <td>
                                                                {% if user.is_active %}
                                                                <span class="badge bg-success">نشط</span>
                                                                {% else %}
                                                                <span class="badge bg-secondary">غير نشط</span>
                                                                {% endif %}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">تاريخ الإنشاء:</td>
                                                            <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">لا يوجد مستخدمون</h4>
                            <p class="text-muted">ابدأ بإضافة مستخدم جديد</p>
                            <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>إضافة مستخدم جديد
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleUserStatus(userId, isActive) {
            const action = isActive ? 'تعطيل' : 'تفعيل';
            if (confirm('هل أنت متأكد من ' + action + ' هذا المستخدم؟')) {
                // هنا يمكن إضافة كود AJAX لتغيير حالة المستخدم
                alert('تم ' + action + ' المستخدم بنجاح');
                location.reload();
            }
        }

        function confirmDelete(username) {
            if (confirm('هل أنت متأكد من حذف المستخدم: ' + username + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                alert('تم حذف المستخدم بنجاح');
                location.reload();
            }
        }
    </script>

    <style>
        .avatar-sm {
            width: 2rem;
            height: 2rem;
        }
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>
</body>
</html>
