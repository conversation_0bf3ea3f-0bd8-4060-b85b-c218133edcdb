#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع المسارات في النظام
Test all routes in the system
"""

import requests
import sys

def test_route(url, description):
    """اختبار مسار واحد"""
    try:
        response = requests.get(url, timeout=5)
        status = "✅ يعمل" if response.status_code == 200 else f"❌ خطأ {response.status_code}"
        print(f"{description}: {status}")
        return response.status_code == 200
    except Exception as e:
        print(f"{description}: ❌ خطأ في الاتصال - {str(e)}")
        return False

def main():
    """اختبار جميع المسارات"""
    base_url = "http://localhost:5001"
    
    print("🔍 اختبار جميع مسارات النظام...")
    print("=" * 50)
    
    # المسارات الأساسية
    routes = [
        (f"{base_url}/", "الصفحة الرئيسية"),
        (f"{base_url}/login", "صفحة تسجيل الدخول"),
        
        # مسارات الإدارة (تحتاج تسجيل دخول)
        (f"{base_url}/admin/dashboard", "لوحة التحكم"),
        (f"{base_url}/admin/universities", "إدارة الجامعات"),
        (f"{base_url}/admin/colleges", "إدارة الكليات"),
        (f"{base_url}/admin/departments", "إدارة الأقسام"),
        (f"{base_url}/admin/programs", "إدارة البرامج الأكاديمية"),
        (f"{base_url}/admin/subjects", "إدارة المواد الدراسية"),
        (f"{base_url}/admin/professors", "إدارة الأساتذة"),
        (f"{base_url}/admin/students", "إدارة الطلبة"),
        (f"{base_url}/admin/grades", "إدارة الدرجات"),
        (f"{base_url}/admin/users", "إدارة المستخدمين"),
        
        # مسارات الإضافة
        (f"{base_url}/admin/universities/add", "إضافة جامعة"),
        (f"{base_url}/admin/colleges/add", "إضافة كلية"),
        (f"{base_url}/admin/departments/add", "إضافة قسم"),
        (f"{base_url}/admin/programs/add", "إضافة برنامج أكاديمي"),
        (f"{base_url}/admin/subjects/add", "إضافة مادة دراسية"),
        (f"{base_url}/admin/professors/add", "إضافة أستاذ"),
        (f"{base_url}/admin/students/add", "إضافة طالب"),
        (f"{base_url}/admin/grades/add", "إضافة درجات"),
        (f"{base_url}/admin/users/add", "إضافة مستخدم"),
    ]
    
    working_routes = 0
    total_routes = len(routes)
    
    print("\n📋 اختبار المسارات الأساسية:")
    print("-" * 30)
    
    for url, description in routes:
        if test_route(url, description):
            working_routes += 1
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {working_routes}/{total_routes} مسار يعمل بشكل صحيح")
    
    if working_routes == total_routes:
        print("🎉 جميع المسارات تعمل بشكل مثالي!")
    else:
        print("⚠️  بعض المسارات تحتاج إلى مراجعة")
        print("💡 ملاحظة: المسارات المحمية تحتاج تسجيل دخول")
    
    print("\n🔐 لاختبار المسارات المحمية:")
    print("1. افتح المتصفح على: http://localhost:5001")
    print("2. سجل دخول بـ: admin / admin123")
    print("3. تصفح جميع الأقسام للتأكد من عملها")

if __name__ == "__main__":
    main()
